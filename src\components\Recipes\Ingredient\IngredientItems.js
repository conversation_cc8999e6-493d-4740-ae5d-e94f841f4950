'use client';
import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  Divider,
  gridClasses,
  useTheme,
  useMediaQuery,
  Popover,
} from '@mui/material';
import { fetchFromStorage, saveToStorage } from '@/helper/context';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import { identifiers } from '@/helper/constants/identifier';
import { DataGrid } from '@mui/x-data-grid';
import {
  checkOrganizationRole,
  getCurrencySymbol,
  setApiMessage,
  // DateFormat,
} from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import CustomSelect from '@/components/UI/CustomSelect';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import AddIcon from '@mui/icons-material/Add';
import CustomOrgPagination from '@/components/UI/customPagination';
import FilterListIcon from '@mui/icons-material/FilterList';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import { useRouter } from 'next/navigation';
import {
  getIngredientItemsList,
  getIngredientList,
  getAllergenList,
  deleteIngredient,
  exportIngredient,
  importIngredientCategory,
} from '@/services/recipeService';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { RECIPE_URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import DownloadIcon from '@mui/icons-material/Download';
import UploadIcon from '@mui/icons-material/Upload';
import ExcelFileImporter from './components/ExcelFileImporter';
import './ingredient.scss';
const organizationOptions = [
  { value: 'org1', label: 'Organization 1' },
  { value: 'org2', label: 'Organization 2' },
  { value: 'org3', label: 'Organization 3' },
];

// Static category options

const createSortableHeader = (field, label, sortOrder, onSort) => (
  <Box className="d-flex align-center gap-5">
    <Box className="wrap-header-text d-flex align-center">
      <Typography className="title-text fw600">{label}</Typography>
      <Box className="amount-text arrow-wrap">
        {sortOrder?.key === field && sortOrder?.value === 'DESC' ? (
          <ArrowDownward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        ) : (
          <ArrowUpward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        )}
      </Box>
    </Box>
  </Box>
);

const createColumns = (
  sortOrder,
  handleSort,
  handleAddEdit,
  handleDelete,
  currentPage,
  rowsPerPage,
  paginatedData,
  currency
) => [
  {
    field: 'id',
    headerName: 'ID',
    width: 48,
    minWidth: 48,
    sortable: false,
    flex: 0,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => {
      // Find the index of this row in the paginated data
      const rowIndex = paginatedData.findIndex(
        (row) => row?.id === params?.row?.id
      );
      const sequentialNumber = (currentPage - 1) * rowsPerPage + rowIndex + 1;
      return (
        <Typography className="text-ellipsis">{sequentialNumber}</Typography>
      );
    },
  },
  {
    field: 'name',
    headerName: 'Name',
    width: 300,
    minWidth: 300,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('name', 'Name', sortOrder, handleSort),
    renderCell: (params) => (
      <Box className="h100 d-flex align-center">
        <Box className="">
          <Typography className="title-text fw600">
            {params?.row?.ingredient_name}
          </Typography>
          <Typography className="sub-title-text">
            {params?.row?.ingredient_description}
          </Typography>
        </Box>
      </Box>
    ),
  },
  {
    field: 'category',
    headerName: 'Category',
    width: 150,
    minWidth: 150,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('category', 'Category', sortOrder, handleSort),
    renderCell: (params) => {
      return (
        <Box className="h100 d-flex align-center">
          <Box>
            <Typography className="body-sm category-name-text">
              {params?.row?.category && params?.row?.category?.length > 0
                ? params?.row?.category
                    ?.map((cat) => cat?.category_name)
                    .join(', ')
                : '-'}
            </Typography>
            <Typography className="sub-title-text fw500">
              {params?.row?.measure_of_cost?.unit_title ?? '-'}
            </Typography>
          </Box>
        </Box>
      );
    },
  },
  {
    field: 'allergy',
    headerName: 'Allergen',
    width: 200,
    minWidth: 200,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => (
      <Box className="d-flex align-center gap-sm flex-wrap mt8 mb8">
        {params?.row?.allergy && params?.row?.allergy?.length > 0 ? (
          params?.row?.allergy?.map((item, index) => {
            return (
              <Typography
                key={index}
                className={`sub-title-text status-inactive ${item?.attribute_title?.includes('Free') ? 'status-active' : ''}`}
              >
                {item?.attribute_title}
              </Typography>
            );
          })
        ) : (
          <Typography className="sub-title-text">-</Typography>
        )}
      </Box>
    ),
  },
  {
    field: 'dietary_info',
    headerName: 'Dietary Info',
    width: 200,
    minWidth: 200,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => (
      <Box className="d-flex align-center gap-sm flex-wrap mt8 mb8">
        {params?.row?.dietary_info && params?.row?.dietary_info?.length > 0 ? (
          params?.row?.dietary_info?.map((item, index) => (
            <Typography
              key={index}
              className={`sub-title-text status-active ${item?.attribute_title?.includes('Free') ? 'status-inactive' : ''}`}
            >
              {item?.attribute_title}
            </Typography>
          ))
        ) : (
          <Typography className="sub-title-text">-</Typography>
        )}
      </Box>
    ),
  },
  {
    field: 'cost',
    headerName: 'Cost',
    width: 150,
    minWidth: 150,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('cost', 'Cost', sortOrder, handleSort),
    renderCell: (params) => (
      <Box className="h100 d-flex align-center">
        <Typography className="title-text">
          {currency}
          {params?.row?.cost}
          {params?.row?.measure_of_cost?.unit_title
            ? `/${params?.row?.measure_of_cost?.unit_title}`
            : ''}
        </Typography>
      </Box>
    ),
  },
  {
    field: 'status',
    headerName: 'Status',
    width: 150,
    minWidth: 150,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('status', 'Status', sortOrder, handleSort),
    renderCell: (params) => {
      return (
        <Box className={'title-text h100 d-flex align-center justify-center '}>
          <Typography
            className={`sub-title-text fw600 ${
              params?.row?.ingredient_status === 'active'
                ? 'label-active'
                : params?.row?.ingredient_status === 'inactive'
                  ? 'failed'
                  : ''
            }`}
          >
            {params?.row?.ingredient_status}
          </Typography>
        </Box>
      );
    },
  },
  // {
  //   field: 'actionBy',
  //   headerName: 'Action By',
  //   width: 200,
  //   minWidth: 200,
  //   flex: 1,
  //   sortable: false,
  //   headerAlign: 'start',
  //   align: 'start',
  //   renderCell: (params) => (
  //     <Box className="gap-5 h100 d-flex align-center">
  //       <Box className="text-ellipsis">
  //         <Typography className="title-text">
  //           {params?.row?.actionBy}
  //         </Typography>
  //         <Typography className="title-text">
  //           {DateFormat(params?.row?.actionDate, 'datesWithhour')}
  //         </Typography>
  //       </Box>
  //     </Box>
  //   ),
  //   // renderHeader: () =>
  //   //   createSortableHeader('actionBy', 'Action By', sortOrder, handleSort),
  // },
  {
    field: 'actions',
    headerName: 'Actions',
    width: 120,
    minWidth: 120,
    flex: 1,
    sortable: false,
    headerAlign: 'center',
    align: 'center',
    renderCell: (params) => (
      <Box className="d-flex actions align-center justify-center h100">
        {!params?.row?.isDefault ? (
          <>
            <Tooltip
              title={<Typography className="sub-title-text">Edit</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box
                className="d-flex"
                onClick={() => handleAddEdit(params?.row)}
              >
                <EditIcon />
              </Box>
            </Tooltip>
            <Tooltip
              title={<Typography className="sub-title-text">Delete</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box className="d-flex">
                <DeleteIcon onClick={() => handleDelete(params.row.id)} />
              </Box>
            </Tooltip>
          </>
        ) : (
          '-'
        )}
      </Box>
    ),
  },
];

export default function IngredientItems() {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const { authState } = useContext(AuthContext);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [filteredIngredients, setFilteredIngredients] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isTableAdjusting, setIsTableAdjusting] = useState(false);
  const [importSampleFileUrl, setImportSampleFileUrl] = useState('');
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [allergenOptions, setAllergenOptions] = useState([]);
  const [dietaryOptions, setDietaryOptions] = useState([]);
  const [filterData, setFilterData] = useState({
    status: '',
    category: '',
    allergen: '',
    dietary: '',
    organizationId: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    category: '',
    allergen: '',
    dietary: '',
    organizationId: '',
    searchValue: '',
  });

  const filters = [
    {
      key: 'search',
      label: 'Search',
      options: [],
      permission: true,
    },
    {
      key: 'status',
      label: 'Status',
      options: staticOptions?.ORG_STATUS,
      permission: true,
    },
    {
      key: 'category',
      label: 'Category',
      options: categoryOptions,
      permission: true,
    },
    {
      key: 'allergen',
      label: 'Allergen',
      options: allergenOptions,
      permission: true,
    },
    {
      key: 'dietary',
      label: 'Dietary Info',
      options: dietaryOptions,
      permission: true,
    },
    {
      key: 'organizationId',
      label: 'Organization',
      options: organizationOptions,
      permission: checkOrganizationRole('super_admin'),
    },
  ];

  // API function to get ingredient categories for filter
  const getIngredientCategoriesData = async () => {
    try {
      const response = await getIngredientList(
        '', // search - empty for getting all categories
        1, // page - first page only
        { status: 'active' }, // filter - only active categories
        '', // Rpp - empty to get default limit
        { key: 'category_name', value: 'ASC' } // Sort by name
      );

      // Transform categories to filter options format
      const categoryFilterOptions =
        response?.ingredients?.map((category) => ({
          value: category?.id,
          label: category?.category_name,
        })) || [];

      setCategoryOptions(categoryFilterOptions);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setCategoryOptions([]);
    }
  };

  // API function to get allergens for filter
  const getAllergensData = async () => {
    try {
      const response = await getAllergenList(
        '', // search - empty for getting all allergens
        1, // page - first page only
        { status: 'active' }, // filter - only active allergens
        '', // Rpp - empty to get default limit
        { key: 'attribute_title', value: 'ASC' } // Sort by name
      );

      // Transform allergens to filter options format
      const allergenFilterOptions =
        response?.allergens?.map((allergen) => ({
          value: allergen?.id,
          label: allergen?.attribute_title,
        })) || [];

      setAllergenOptions(allergenFilterOptions);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setAllergenOptions([]);
    }
  };

  // API function to get dietary info for filter (using same API with type=dietary)
  const getDietaryInfoData = async () => {
    try {
      // Using getAllergenList API but with type=dietary instead of type=allergen
      const response = await axiosInstance.get(
        RECIPE_URLS.GET_ALL_ALLERGENS +
          `?page=1&limit=&search=&type=dietary&status=active&sort=attribute_title&order=ASC`
      );

      if (response.status === 200) {
        // Transform dietary info to filter options format
        const dietaryFilterOptions =
          response?.data?.data?.map((dietary) => ({
            value: dietary?.id,
            label: dietary?.attribute_title,
          })) || [];

        setDietaryOptions(dietaryFilterOptions);
      } else {
        setDietaryOptions([]);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setDietaryOptions([]);
    }
  };

  // API function to get ingredient items

  const getIngredientItemsListData = async (
    search,
    page,
    filter,
    Rpp,
    Sort,
    showLoader = true
  ) => {
    try {
      if (showLoader) {
        setIsTableAdjusting(true);
      }
      const response = await getIngredientItemsList(
        search,
        page,
        filter,
        Rpp,
        Sort
      );

      setFilteredIngredients(response?.ingredients);
      setTotalCount(response?.totalCount);

      // Store the import sample file URL from API response
      if (response?.import_sample_file) {
        setImportSampleFileUrl(response.import_sample_file);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setFilteredIngredients([]);
      setTotalCount(0);
    } finally {
      if (showLoader) {
        setTimeout(() => {
          setIsTableAdjusting(false);
        }, 100);
      }
    }
  };

  // Download sample template for ingredient import
  const downloadSampleTemplate = async () => {
    try {
      // Check if import sample file URL is available
      if (!importSampleFileUrl) {
        setApiMessage(
          'error',
          'Sample template URL not available. Please refresh the page and try again.'
        );
        return;
      }

      // Extract filename from URL or use default
      let filename = 'ingredient_import_template.xlsx';
      try {
        const url = new URL(importSampleFileUrl);
        const pathParts = url.pathname.split('/');
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart && lastPart.includes('.')) {
          filename = lastPart;
        }
      } catch {
        // Could not parse filename from URL, using default
      }

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = importSampleFileUrl;
      link.setAttribute('download', filename);
      link.setAttribute('target', '_blank'); // Open in new tab as fallback
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download sample template error:', error);
      setApiMessage(
        'error',
        'Failed to download sample template. Please try again.'
      );
    }
  };

  const currency = getCurrencySymbol(authState?.currency_details);

  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      if (prevFilters?.includes(key)) {
        return prevFilters?.filter((item) => item !== key);
      } else {
        const index = filters.findIndex((filter) => filter.key === key);
        const newFilters = [...prevFilters];
        newFilters.splice(index, 0, key);
        return newFilters;
      }
    });
  };

  const getFirstFourFilters = () => {
    setSelectedFilters(selectedFilters?.slice(0, 4));
    saveToStorage(identifiers?.INGREDIENT_FILTER, selectedFilters?.slice(0, 4));
  };

  const saveLayout = () => {
    saveToStorage(identifiers?.INGREDIENT_FILTER, selectedFilters);
    setOpenFilterDrawer(false);
  };

  useEffect(() => {
    const savedFilters = fetchFromStorage(identifiers?.INGREDIENT_FILTER);
    if (!savedFilters) {
      setSelectedFilters(filters.slice(0, 4)?.map((filter) => filter?.key));
    } else {
      setSelectedFilters(savedFilters);
    }
  }, []);

  // Load initial data, categories, allergens and dietary info on component mount
  useEffect(() => {
    // Fetch ingredient categories for filter
    getIngredientCategoriesData();

    // Fetch allergens for filter
    getAllergensData();

    // Fetch dietary info for filter
    getDietaryInfoData();

    // Fetch ingredient items
    getIngredientItemsListData(
      '', // search
      1, // page
      {
        status: '',
        category: '',
        allergen: '',
        dietary: '',
        organizationId: '',
      }, // filter
      rowsPerPage, // Rpp
      { key: '', value: 'ASC' }, // Sort
      true // Show loader for initial load
    );
  }, []);

  // Filter options

  const handleDelete = (id) => {
    setDeleteDialogOpen(true);
    setDeleteId(id);
  };

  const handleConfirmDelete = async () => {
    try {
      // Call the delete API
      await deleteIngredient(deleteId);

      // Show success message
      setApiMessage('success', 'Ingredient deleted successfully');

      // Remove the ingredient from the local state (no page refresh)
      const updatedIngredients = filteredIngredients.filter(
        (ingredient) => ingredient.id !== deleteId
      );
      setFilteredIngredients(updatedIngredients);

      // Update total count
      setTotalCount((prevCount) => prevCount - 1);

      // Close the delete dialog
      handleCloseDeleteDialog();
    } catch (error) {
      // Show error message
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to delete ingredient'
      );

      // Close the delete dialog even on error
      handleCloseDeleteDialog();
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleSort = async (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setCurrentPage(1);

    // Call API with new sort order without showing loader
    await getIngredientItemsListData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      rowsPerPage,
      newSortOrder,
      false // Don't show loader for sorting
    );
  };

  const handleSearch = async () => {
    try {
      // Call API with current search and filter values (with loader)
      await getIngredientItemsListData(
        searchValue,
        1, // Reset to first page
        filterDataApplied,
        rowsPerPage,
        sortOrder,
        true // Show loader for search
      );
      // Apply current search value to filterDataApplied
      setFilterDataApplied((prev) => ({
        ...prev,
        searchValue: searchValue,
      }));
      setCurrentPage(1);
    } catch (error) {
      // Error handling is done in the API function
      console.error('Search error:', error);
    }
  };

  const handleClearSearch = async () => {
    setSearchValue('');
    setFilterData({
      status: '',
      category: '',
      allergen: '',
      dietary: '',
      organizationId: '',
    });
    setFilterDataApplied({
      status: '',
      category: '',
      allergen: '',
      dietary: '',
      organizationId: '',
      searchValue: '', // Clear applied search value too
    });
    setCurrentPage(1);

    // Call API with cleared filters (with loader)
    try {
      await getIngredientItemsListData(
        '',
        1,
        {
          status: '',
          category: '',
          allergen: '',
          dietary: '',
          organizationId: '',
          searchValue: '',
        },
        rowsPerPage,
        sortOrder,
        true // Show loader for clear search
      );
    } catch (error) {
      // Error handling is done in the API function
      console.error('Clear search error:', error);
    }
  };

  const handleApplyFilter = async () => {
    setCurrentPage(1);

    // Create new filter object with current filter values
    const newFilterDataApplied = {
      status: filterData?.status,
      category: filterData?.category,
      allergen: filterData?.allergen,
      dietary: filterData?.dietary,
      organizationId: filterData?.organizationId,
      searchValue: searchValue, // Include current search value
    };

    // Update the applied filter state
    setFilterDataApplied(newFilterDataApplied);

    // Call API directly with new filter values (with loader)
    try {
      await getIngredientItemsListData(
        searchValue,
        1, // Reset to first page
        newFilterDataApplied,
        rowsPerPage,
        sortOrder,
        true // Show loader for apply filter
      );
    } catch (error) {
      // Error handling is done in the API function
      console.error('Apply filter error:', error);
    }

    if (isMobile) {
      setOpenFilterDrawer(false);
    }
  };

  const handleClearFilter = () => {
    handleClearSearch();
    if (isMobile) {
      setOpenFilterDrawer(false);
    }
  };

  const handleKeyPress = async (event) => {
    if (event?.key === 'Enter') {
      await handleSearch();
      setOpenFilterDrawer(false);
    }
  };

  // Pagination handlers
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);
    await getIngredientItemsListData(
      filterDataApplied.searchValue || '',
      newPage,
      filterDataApplied,
      rowsPerPage,
      sortOrder,
      true // Show loader for page change
    );
  };

  const handleRowsPerPageChange = async (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    await getIngredientItemsListData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      newRowsPerPage,
      sortOrder,
      true // Show loader for rows per page change
    );
  };

  const handleEdit = (item) => {
    router.push(`/recipes/ingredients/${item?.ingredient_slug || item?.id}`);
  };

  const columns = createColumns(
    sortOrder,
    handleSort,
    handleEdit,
    handleDelete,
    currentPage,
    rowsPerPage,
    filteredIngredients,
    currency
  );

  // Download popover state
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const id = open ? 'download-popover' : undefined;

  // Import modal state
  const [importModal, setImportModal] = useState(false);
  const [acceptedMedia, setAcceptedMedia] = useState([]);

  // Open download popover
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  // Close download popover
  const handleClose = () => {
    setAnchorEl(null);
  };

  // Download handler
  const handleDownload = async (format) => {
    try {
      // Use applied filters and search value

      const response = await exportIngredient(format);
      // Get filename from response headers or fallback
      let filename = `ingredients.${format === 'excel' ? 'xlsx' : 'csv'}`;
      const disposition = response.headers['content-disposition'];
      if (disposition) {
        const match = disposition.match(/filename="?([^";]+)"?/);
        if (match) filename = match[1];
      }
      // Create blob and trigger download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to export ingredients'
      );
    } finally {
      handleClose();
    }
  };

  // Dropzone configuration is now handled by ImportModalContent component

  // Handle import modal open
  const handleOpenImportModal = () => {
    setImportModal(true);
    setAcceptedMedia([]);
  };

  // Handle import modal close
  const handleCloseImportModal = () => {
    setImportModal(false);
    setAcceptedMedia([]);
  };

  // Handle file import
  const handleImportFile = async () => {
    if (!acceptedMedia || acceptedMedia.length === 0) {
      setApiMessage('error', 'Please select a file to import.');
      return;
    }

    try {
      const response = await importIngredientCategory(acceptedMedia[0]);
      setApiMessage(
        'success',
        response?.message || 'File imported successfully'
      );

      // Refresh the list after import
      await getIngredientItemsListData(
        filterDataApplied.searchValue || '',
        currentPage,
        filterDataApplied,
        rowsPerPage,
        sortOrder,
        true // Show loader for refresh after import
      );

      handleCloseImportModal();
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to import file'
      );
    }
  };

  // uploadedMedia function is now handled by ImportModalContent component

  return (
    <Box className="ingredient-list-container h100">
      <Box className="recipe-category-filter-wrap">
        <Box className="section-right-title">
          <Typography className="sub-header-text">Ingredient</Typography>
        </Box>
        <Box className="d-flex align-center gap-sm">
          <Box>
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Import</Typography>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <DownloadIcon />
                </Tooltip>
              }
              onClick={handleOpenImportModal}
            />
          </Box>
          <Box>
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Export</Typography>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <UploadIcon />
                </Tooltip>
              }
              onClick={handleClick}
            />
          </Box>

          <Box className="mr8 pr4">
            <CustomButton
              title="Add Ingredient"
              startIcon={<AddIcon />}
              onClick={() => {
                router.push('/recipes/ingredients/create');
              }}
            />
          </Box>
        </Box>
      </Box>
      <Divider />
      <Box className="section-right-content">
        <Box className="search-section-wrap">
          {!isMobile &&
            selectedFilters?.map((key) => {
              const filter = filters?.find((f) => f?.key === key);
              return filter?.permission ? (
                <React.Fragment key={key}>
                  {key === 'search' ? (
                    <Box className="search-section-fields">
                      <CustomSearch
                        fullWidth
                        setSearchValue={setSearchValue}
                        onKeyPress={handleKeyPress}
                        searchValue={searchValue}
                      />
                    </Box>
                  ) : (
                    <Box className="search-section-fields">
                      <CustomSelect
                        placeholder={filter?.label}
                        options={filter?.options}
                        value={
                          filter?.options?.find((opt) => {
                            return opt?.value === filterData[key];
                          }) || ''
                        }
                        onChange={(e) =>
                          setFilterData({
                            ...filterData,
                            [key]: e?.value,
                          })
                        }
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    </Box>
                  )}
                </React.Fragment>
              ) : null;
            })}

          {!isMobile && (
            <>
              <Box>
                <CustomButton
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Apply Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <CheckIcon />
                    </Tooltip>
                  }
                  onClick={handleApplyFilter}
                />
              </Box>
              <Box>
                <CustomButton
                  variant="outlined"
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Clear Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <ClearOutlinedIcon />
                    </Tooltip>
                  }
                  onClick={handleClearFilter}
                />
              </Box>
            </>
          )}
          <Box>
            <CustomButton
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Filter</Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <FilterListIcon />
                </Tooltip>
              }
              onClick={() => {
                setOpenFilterDrawer(true);
              }}
            />
          </Box>
        </Box>
        <Box className="table-container table-layout">
          {isTableAdjusting ? (
            <ContentLoader />
          ) : filteredIngredients.length === 0 ? (
            <NoDataView
              title="No Ingredient found"
              description="There is no Ingredient available at the moment."
            />
          ) : (
            <>
              <DataGrid
                key={`datagrid-${filteredIngredients.length}-${currentPage}`}
                rows={filteredIngredients}
                columns={columns}
                pageSize={rowsPerPage}
                rowCount={totalCount}
                checkboxSelection={false}
                disableSelectionOnClick
                hideMenuIcon
                paginationMode="server"
                disableVirtualization={false}
                getRowHeight={() => 'auto'}
                sx={{
                  transition: 'none !important',
                  animation: 'none !important',
                  '& *': {
                    transition: 'none !important',
                    animation: 'none !important',
                    transform: 'none !important',
                  },
                  [`& .${gridClasses.cell}`]: {
                    py: 1,
                    transition: 'none',
                  },
                }}
              />
              <CustomOrgPagination
                currentPage={currentPage}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={handlePageChange}
                OnRowPerPage={handleRowsPerPageChange}
              />
            </>
          )}
        </Box>
        <RightDrawer
          anchor={'right'}
          open={openFilterDrawer}
          onClose={() => setOpenFilterDrawer(false)}
          title="Filter"
          className="filter-options-drawer"
          content={
            <FilterComponent
              filters={filters}
              filterData={filterData}
              setFilterData={setFilterData}
              selectedFilters={selectedFilters}
              toggleFilter={toggleFilter}
              saveLayout={saveLayout}
              setOpenFilterDrawer={setOpenFilterDrawer}
              setSelectedFilters={setSelectedFilters}
              getFirstFourFilters={getFirstFourFilters}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              handleKeyPress={handleKeyPress}
              isMobile={isMobile}
              handleApplyFilter={handleApplyFilter}
              handleClearFilter={handleClearFilter}
            />
          }
        />
        <DialogBox
          open={deleteDialogOpen}
          handleClose={handleCloseDeleteDialog}
          title="Confirmation"
          className="delete-modal"
          dividerClass="delete-modal-divider"
          content={
            <DeleteModal
              handleCancel={handleCloseDeleteDialog}
              handleConfirm={handleConfirmDelete}
              text="Are you sure you want to delete this? This action cannot be undone."
            />
          }
        />
        <Popover
          className="export-popover"
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
        >
          <Box className="export-option">
            <Typography
              className="p14 fw600 pb8 cursor-pointer"
              onClick={() => handleDownload('excel')}
            >
              Excel
            </Typography>
            <Typography
              className="p14 fw600 cursor-pointer"
              onClick={() => handleDownload('csv')}
            >
              CSV
            </Typography>
          </Box>
        </Popover>
        <DialogBox
          open={importModal}
          handleClose={handleCloseImportModal}
          title="Import Ingredients"
          className="small-dialog-box-container"
          content={
            <ExcelFileImporter
              acceptedMedia={acceptedMedia}
              setAcceptedMedia={setAcceptedMedia}
              onCancel={handleCloseImportModal}
              onImport={handleImportFile}
              title="Upload Excel File"
              description="Before importing data, ensure the file follows the required format for accuracy and consistency. Download the sample file as a reference and match the file name, date, and structure to avoid errors."
              onDownloadSample={downloadSampleTemplate}
              showDownloadSample={true}
              dropText="Drop your Excel file here"
            />
          }
        />
      </Box>
    </Box>
  );
}
