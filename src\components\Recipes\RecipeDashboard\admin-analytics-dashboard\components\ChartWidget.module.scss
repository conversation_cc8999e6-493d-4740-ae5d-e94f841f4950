// ChartWidget SCSS Module
// Colors from the design system
$primary: #2D5A3D;
$primary-50: #F0F7F2;
$accent: #E67E22;
$surface: #FFFFFF;
$text-primary: #2C3E50;
$text-secondary: #7F8C8D;
$border-color: #e5e7eb;
$border-light: #f3f4f6;
$background-light: #f9fafb;

// Transitions
$transition-smooth: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
$transition-smooth-300: all 300ms cubic-bezier(0.4, 0, 0.2, 1);

// Shadows
$shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);

// Main widget container
.chartWidget {
  background-color: $surface;
  border: 1px solid $border-color;
  border-radius: 0.5rem;
  box-shadow: $shadow-card;
  position: relative;

  &.fullscreen {
    position: fixed;
    top: 1rem;
    left: 1rem;
    right: 1rem;
    bottom: 1rem;
    z-index: 50;
  }
}

// Widget header
.widgetHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid $border-light;
}

.headerInfo {
  display: flex;
  flex-direction: column;
}

.widgetTitle {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 1.125rem;
  color: $text-primary;
  margin: 0;
}

.widgetSubtitle {
  font-size: 0.875rem;
  color: $text-secondary;
  margin: 0;
  margin-top: 0.25rem;
}

.headerControls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chartOptions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.optionButton {
  padding: 0.25rem;
  border-radius: 0.25rem;
  border: none;
  background: transparent;
  color: $text-secondary;
  cursor: pointer;
  transition: $transition-smooth;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: $primary;
  }

  &.optionButtonActive {
    color: $primary;
    background-color: $primary-50;
  }
}

.fullscreenButton {
  padding: 0.25rem;
  border: none;
  background: transparent;
  color: $text-secondary;
  cursor: pointer;
  transition: $transition-smooth;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: $primary;
  }
}

// Chart content area
.chartContent {
  padding: 1rem;
  height: 20rem;

  &.chartContentFullscreen {
    height: calc(100vh - 8rem);
  }
}

// Chart footer
.chartFooter {
  padding: 0.75rem 1rem;
  border-top: 1px solid $border-light;
  background-color: $background-light;
}

.footerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
}

.legendContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legendDot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.legendLabel {
  color: $text-secondary;
}

.exportButton {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: $primary;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: $transition-smooth;

  &:hover {
    color: darken($primary, 10%);
  }
}

// Heatmap styles
.heatmapGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  height: 100%;
}

.heatmapItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: $transition-smooth;

  &:hover {
    transform: scale(1.05);
  }
}

.heatmapHour {
  font-size: 0.75rem;
  font-weight: 500;
}

.heatmapDay {
  font-size: 0.75rem;
}

.heatmapValue {
  font-size: 0.875rem;
  font-weight: 700;
}

// Funnel chart styles
.funnelContainer {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  height: 100%;
  justify-content: center;
}

.funnelStage {
  position: relative;
}

.funnelStageHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.funnelStageTitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: $text-primary;
}

.funnelStagePercentage {
  font-size: 0.875rem;
  color: $text-secondary;
}

.funnelProgressTrack {
  width: 100%;
  background-color: #e5e7eb;
  border-radius: 9999px;
  height: 0.75rem;
  overflow: hidden;
}

.funnelProgressBar {
  height: 0.75rem;
  border-radius: 9999px;
  transition: $transition-smooth-300;
}

.funnelStageCount {
  font-size: 0.75rem;
  color: $text-secondary;
  margin-top: 0.25rem;
  display: block;
}

// Responsive design
@media (max-width: 768px) {
  .widgetHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .headerControls {
    align-self: flex-end;
  }

  .footerContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .legendContainer {
    flex-wrap: wrap;
  }

  .heatmapGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .chartContent {
    padding: 0.5rem;
    height: 16rem;
  }

  .widgetHeader {
    padding: 0.75rem;
  }

  .chartFooter {
    padding: 0.5rem 0.75rem;
  }
}
