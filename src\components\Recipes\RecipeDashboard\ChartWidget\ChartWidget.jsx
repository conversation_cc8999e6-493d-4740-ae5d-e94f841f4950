'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import Icon from '@/components/AppIcon';
import './chartwidget.scss';

const ChartWidget = ({
  title,
  type = 'line',
  data = [],
  dateRange = '7days',
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartOptions, setChartOptions] = useState({
    showGrid: true,
    showTooltip: true,
    animate: true,
  });

  // Using CSS variables for consistent theming
  const colors = {
    primary: '#135e96',
    secondary: '#f7f7f7',
    accent: '#db972c',
    success: '#038d2a',
    warning: '#db972c',
    error: '#d32f2f',
  };

  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data}>
        {chartOptions.showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        )}
        <XAxis dataKey="name" stroke="#7F8C8D" fontSize={12} tickLine={false} />
        <YAxis
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
          />
        )}
        <Line
          type="monotone"
          dataKey="views"
          stroke={colors.primary}
          strokeWidth={2}
          dot={{ fill: colors.primary, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.primary, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Line
          type="monotone"
          dataKey="likes"
          stroke={colors.accent}
          strokeWidth={2}
          dot={{ fill: colors.accent, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.accent, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data}>
        {chartOptions.showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        )}
        <XAxis dataKey="name" stroke="#7F8C8D" fontSize={12} tickLine={false} />
        <YAxis
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
          />
        )}
        <Bar
          dataKey="recipes"
          fill={colors.primary}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Bar
          dataKey="views"
          fill={colors.accent}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderHeatmap = () => (
    <div className="chart-widget__heatmap-grid">
      {data.map((item, index) => (
        <div
          key={index}
          className="chart-widget__heatmap-cell"
          style={{
            backgroundColor: `rgba(19, 94, 150, ${item.value / 100})`,
            color: item.value > 50 ? '#ffffff' : '#2C3E50',
          }}
        >
          <span className="chart-widget__heatmap-hour">{item.hour}</span>
          <span className="chart-widget__heatmap-day">{item.day}</span>
          <span className="chart-widget__heatmap-value">{item.value}</span>
        </div>
      ))}
    </div>
  );

  const renderFunnelChart = () => (
    <div className="chart-widget__funnel-grid">
      {data.map((stage, index) => (
        <div key={index} className="chart-widget__funnel-stage">
          <div className="chart-widget__funnel-header">
            <span className="chart-widget__funnel-stage-name">
              {stage.stage}
            </span>
            <span className="chart-widget__funnel-stage-percent">
              {stage.percentage}%
            </span>
          </div>
          <div className="chart-widget__funnel-bar-bg">
            <div
              className="chart-widget__funnel-bar"
              style={{
                width: `${stage.percentage}%`,
                backgroundColor: colors.primary,
                opacity: 1 - index * 0.15,
              }}
            />
          </div>
          <span className="chart-widget__funnel-count">
            {stage.count.toLocaleString()} users
          </span>
        </div>
      ))}
    </div>
  );

  const renderChart = () => {
    switch (type) {
      case 'line':
        return renderLineChart();
      case 'bar':
        return renderBarChart();
      case 'heatmap':
        return renderHeatmap();
      case 'funnel':
        return renderFunnelChart();
      default:
        return renderLineChart();
    }
  };

  const toggleChartOption = (option) => {
    setChartOptions((prev) => ({
      ...prev,
      [option]: !prev[option],
    }));
  };

  return (
    <div
      className={`chart-widget${isFullscreen ? ' chart-widget--fullscreen' : ''}`}
    >
      {/* Widget Header */}
      <div className="chart-widget__header">
        <div className="chart-widget__header-info">
          <h3 className="chart-widget__title">{title}</h3>
          <p className="chart-widget__subtitle">
            {dateRange === '7days'
              ? 'Last 7 days'
              : dateRange === '30days'
                ? 'Last 30 days'
                : dateRange === 'quarter'
                  ? 'Last quarter'
                  : 'Last year'}
          </p>
        </div>
        <div className="chart-widget__options">
          {/* Chart Options */}
          <div className="chart-widget__controls">
            <button
              onClick={() => toggleChartOption('showGrid')}
              className={`chart-widget__option-btn${chartOptions.showGrid ? ' chart-widget__option-btn--active' : ''}`}
              title="Toggle grid"
            >
              <Icon name="Grid3X3" size={16} />
            </button>
            <button
              onClick={() => toggleChartOption('animate')}
              className={`chart-widget__option-btn${chartOptions.animate ? ' chart-widget__option-btn--active' : ''}`}
              title="Toggle animation"
            >
              <Icon name="Play" size={16} />
            </button>
          </div>
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="chart-widget__fullscreen-btn"
            title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
          >
            <Icon name={isFullscreen ? 'Minimize2' : 'Maximize2'} size={16} />
          </button>
        </div>
      </div>
      {/* Chart Content */}
      <div className="chart-widget__content">{renderChart()}</div>
      {/* Chart Footer */}
      <div className="chart-widget__footer">
        <div className="chart-widget__legend">
          {type === 'line' && (
            <>
              <div className="chart-widget__legend-item">
                <div
                  className="chart-widget__legend-color"
                  style={{ backgroundColor: colors.primary }}
                ></div>
                <span className="chart-widget__legend-text">Views</span>
              </div>
              <div className="chart-widget__legend-item">
                <div
                  className="chart-widget__legend-color"
                  style={{ backgroundColor: colors.accent }}
                ></div>
                <span className="chart-widget__legend-text">Likes</span>
              </div>
            </>
          )}
          {type === 'bar' && (
            <>
              <div className="chart-widget__legend-item">
                <div
                  className="chart-widget__legend-color"
                  style={{ backgroundColor: colors.primary }}
                ></div>
                <span className="chart-widget__legend-text">Recipes</span>
              </div>
              <div className="chart-widget__legend-item">
                <div
                  className="chart-widget__legend-color"
                  style={{ backgroundColor: colors.accent }}
                ></div>
                <span className="chart-widget__legend-text">Views</span>
              </div>
            </>
          )}
        </div>
        <button className="chart-widget__export-btn">
          <Icon name="Download" size={14} />
          <span className="chart-widget__export-text">Export</span>
        </button>
      </div>
    </div>
  );
};

export default ChartWidget;
