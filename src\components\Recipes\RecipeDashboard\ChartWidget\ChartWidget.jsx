import React, { useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  <PERSON>Chart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import Icon from '@/components/AppIcon';
import './chartwidget.scss';

const ChartWidget = ({ title, type, data, dateRange }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartOptions, setChartOptions] = useState({
    showGrid: true,
    showTooltip: true,
    animate: true,
  });

  const colors = {
    primary: '#2D5A3D',
    secondary: '#8B4513',
    accent: '#E67E22',
    success: '#27AE60',
    warning: '#F39C12',
    error: '#E74C3C',
  };

  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data}>
        {chartOptions.showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        )}
        <XAxis dataKey="name" stroke="#7F8C8D" fontSize={12} tickLine={false} />
        <YAxis
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
          />
        )}
        <Line
          type="monotone"
          dataKey="views"
          stroke={colors.primary}
          strokeWidth={2}
          dot={{ fill: colors.primary, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.primary, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Line
          type="monotone"
          dataKey="likes"
          stroke={colors.accent}
          strokeWidth={2}
          dot={{ fill: colors.accent, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.accent, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data}>
        {chartOptions.showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        )}
        <XAxis dataKey="name" stroke="#7F8C8D" fontSize={12} tickLine={false} />
        <YAxis
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
          />
        )}
        <Bar
          dataKey="recipes"
          fill={colors.primary}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Bar
          dataKey="views"
          fill={colors.accent}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderHeatmap = () => (
    <div className="chart-widget-heatmap-grid">
      {data.map((item, index) => (
        <div
          key={index}
          className="chart-widget-heatmap-cell"
          style={{
            backgroundColor: `rgba(45, 90, 61, ${item.value / 100})`,
            color: item.value > 50 ? '#ffffff' : '#2C3E50',
          }}
        >
          <span className="chart-widget-heatmap-hour">{item.hour}</span>
          <span className="chart-widget-heatmap-day">{item.day}</span>
          <span className="chart-widget-heatmap-value">{item.value}</span>
        </div>
      ))}
    </div>
  );

  const renderFunnelChart = () => (
    <div className="chart-widget-funnel-grid">
      {data.map((stage, index) => (
        <div key={index} className="chart-widget-funnel-stage">
          <div className="chart-widget-funnel-header">
            <span className="chart-widget-funnel-stage-name">
              {stage.stage}
            </span>
            <span className="chart-widget-funnel-stage-percent">
              {stage.percentage}%
            </span>
          </div>
          <div className="chart-widget-funnel-bar-bg">
            <div
              className="chart-widget-funnel-bar"
              style={{
                width: `${stage.percentage}%`,
                backgroundColor: colors.primary,
                opacity: 1 - index * 0.15,
              }}
            />
          </div>
          <span className="chart-widget-funnel-count">
            {stage.count.toLocaleString()} users
          </span>
        </div>
      ))}
    </div>
  );

  const renderChart = () => {
    switch (type) {
      case 'line':
        return renderLineChart();
      case 'bar':
        return renderBarChart();
      case 'heatmap':
        return renderHeatmap();
      case 'funnel':
        return renderFunnelChart();
      default:
        return renderLineChart();
    }
  };

  const toggleChartOption = (option) => {
    setChartOptions((prev) => ({
      ...prev,
      [option]: !prev[option],
    }));
  };

  return (
    <div
      className={`chart-widget-container${isFullscreen ? ' fullscreen' : ''}`}
    >
      {/* Widget Header */}
      <div className="chart-widget-header">
        <div>
          <h3 className="chart-widget-title">{title}</h3>
          <p className="chart-widget-subtitle">
            {dateRange === '7days'
              ? 'Last 7 days'
              : dateRange === '30days'
                ? 'Last 30 days'
                : dateRange === 'quarter'
                  ? 'Last quarter'
                  : 'Last year'}
          </p>
        </div>
        <div className="chart-widget-options">
          {/* Chart Options */}
          <div className="chart-widget-options">
            <button
              onClick={() => toggleChartOption('showGrid')}
              className={`chart-widget-option-btn${chartOptions.showGrid ? ' active' : ''}`}
              title="Toggle grid"
            >
              <Icon name="Grid3X3" size={16} />
            </button>
            <button
              onClick={() => toggleChartOption('animate')}
              className={`chart-widget-option-btn${chartOptions.animate ? ' active' : ''}`}
              title="Toggle animation"
            >
              <Icon name="Play" size={16} />
            </button>
          </div>
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="chart-widget-option-btn"
            title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
          >
            <Icon name={isFullscreen ? 'Minimize2' : 'Maximize2'} size={16} />
          </button>
        </div>
      </div>
      {/* Chart Content */}
      <div
        className={`chart-widget-content${isFullscreen ? ' fullscreen' : ''}`}
      >
        {renderChart()}
      </div>
      {/* Chart Footer */}
      <div className="chart-widget-footer">
        <div className="chart-widget-legend">
          {type === 'line' && (
            <>
              <div className="chart-widget-legend-item">
                <div
                  className="chart-widget-legend-color"
                  style={{ backgroundColor: colors.primary }}
                ></div>
                <span>Views</span>
              </div>
              <div className="chart-widget-legend-item">
                <div
                  className="chart-widget-legend-color"
                  style={{ backgroundColor: colors.accent }}
                ></div>
                <span>Likes</span>
              </div>
            </>
          )}
          {type === 'bar' && (
            <>
              <div className="chart-widget-legend-item">
                <div
                  className="chart-widget-legend-color"
                  style={{ backgroundColor: colors.primary }}
                ></div>
                <span>Recipes</span>
              </div>
              <div className="chart-widget-legend-item">
                <div
                  className="chart-widget-legend-color"
                  style={{ backgroundColor: colors.accent }}
                ></div>
                <span>Views</span>
              </div>
            </>
          )}
        </div>
        <button className="chart-widget-export-btn">
          <Icon name="Download" size={14} />
          <span>Export</span>
        </button>
      </div>
    </div>
  );
};

export default ChartWidget;
