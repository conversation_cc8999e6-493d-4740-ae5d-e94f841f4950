.statistics-tile {
  background-color: var(--color-white);
  border: var(--border-width-xs) solid var(--color-light-gray);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  transition: all 0.15s ease-out;
  position: relative;
  overflow: hidden;

  &:hover {
    box-shadow: var(--box-shadow-xs);
    transform: translateY(-2px);
  }

  // Clickable variant
  &--clickable {
    cursor: pointer;

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px var(--color-primary);
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  // Header Section
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
  }

  // Icon Section
  &__icon {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    // Color variants
    &--primary {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border: var(--border-width-xs) solid var(--color-primary);
    }

    &--success {
      background-color: var(--color-success-opacity);
      color: var(--color-success);
      border: var(--border-width-xs) solid var(--color-success);
    }

    &--warning {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
      border: var(--border-width-xs) solid var(--color-warning);
    }

    &--error {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);
      border: var(--border-width-xs) solid var(--color-danger);
    }

    &--accent {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
      border: var(--border-width-xs) solid var(--color-warning);
    }

    &--secondary {
      background-color: var(--color-off-white);
      color: var(--text-color-slate-gray);
      border: var(--border-width-xs) solid var(--color-light-gray);
    }
  }

  // Trend Section
  &__trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    &--up {
      color: var(--color-success);
    }

    &--down {
      color: var(--color-danger);
    }

    &--neutral {
      color: var(--text-color-slate-gray);
    }
  }

  &__trend-value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  // Content Section
  &__content {
    margin-bottom: var(--spacing-sm);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-tiny);
  }

  &__value-container {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-sm);
  }

  &__value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color-primary);
    line-height: 1.2;
  }

  &__update-indicator {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-success);
    animation: pulse 2s infinite;
  }

  // Footer Section
  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-sm);
  }

  &__description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    flex: 1;
  }

  // Sparkline Section
  &__sparkline-container {
    flex-shrink: 0;
  }

  &__sparkline {
    opacity: 0.6;
    transition: opacity 0.15s ease-out;

    .statistics-tile:hover & {
      opacity: 0.8;
    }
  }

  // Hover Indicator
  &__hover-indicator {
    margin-top: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.15s ease-out;

    .statistics-tile:hover & {
      opacity: 1;
    }
  }

  &__hover-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--color-primary);
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: var(--spacing-md);

    &__header {
      margin-bottom: var(--spacing-sm);
    }

    &__value {
      font-size: var(--font-size-2xl);
    }

    &__footer {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-sm);
    }

    &__sparkline-container {
      align-self: flex-end;
    }
  }

  // Animation for real-time updates
  &__value {
    transition: all 0.3s ease-out;
  }

  // Focus states for accessibility
  &--clickable:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

// Pulse animation for update indicator
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
