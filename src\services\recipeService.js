import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { RECIPE_URLS } from '@/helper/constants/urls';
import { isPublicRoute } from '@/helper/common/commonFunctions';

export const getIngredientList = async (search, page, filter, Rpp, Sort) => {
  const categoryFilter =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    if (filter?.status) params.append('status', filter.status);
    params.append('type', 'ingredient');
    if (categoryFilter !== '') {
      params.append('isSystemCategory', categoryFilter.toString());
    }
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_CATEGORIES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        ingredients: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      ingredients: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching ingredient list:', error);
    throw error;
  }
};

export const createIngredientCategory = async (categoryData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_CATEGORIES,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create ingredient category');
  } catch (error) {
    throw error;
  }
};

export const updateIngredientCategory = async (
  categoryId,
  categoryData,
  config
) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_CATEGORY}/${categoryId}`,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update ingredient category');
  } catch (error) {
    throw error;
  }
};

export const deleteIngredientCategory = async (categoryId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_CATEGORY}/${categoryId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete ingredient category');
  } catch (error) {
    throw error;
  }
};

export const getRecipeCategoryList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  const categoryFilter =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    if (filter?.status) params.append('status', filter?.status);
    params.append('type', 'recipe');
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (categoryFilter !== '') {
      params.append('isSystemCategory', categoryFilter.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_CATEGORIES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        categories: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      categories: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching recipe categories:', error);
    throw error;
  }
};

export const createRecipeCategory = async (categoryData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_CATEGORIES,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create recipe category');
  } catch (error) {
    throw error;
  }
};

export const updateRecipeCategory = async (
  categoryId,
  categoryData,
  config
) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_CATEGORY}/${categoryId}`,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe category');
  } catch (error) {
    throw error;
  }
};

export const deleteRecipeCategory = async (categoryId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_CATEGORY}/${categoryId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete recipe category');
  } catch (error) {
    throw error;
  }
};

export const getAllergenList = async (search, page, filter, Rpp, Sort) => {
  const allergenType =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'allergen');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (allergenType !== '') {
      params.append('isSystemAttribute', allergenType.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        allergens: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      allergens: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching allergen list:', error);
    throw error;
  }
};

export const createAllergen = async (allergenData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_ALLERGEN,
      allergenData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create allergen');
  } catch (error) {
    throw error;
  }
};

export const updateAllergen = async (allergenId, allergenData, config) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_ALLERGEN}/${allergenId}`,
      allergenData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update allergen');
  } catch (error) {
    throw error;
  }
};

export const deleteAllergen = async (allergenId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_ALLERGEN}/${allergenId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete allergen');
  } catch (error) {
    throw error;
  }
};

export const getIngredientItemsList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    // Basic parameters - always append page, handle empty values properly
    if (page) params.append('page', page.toString());
    if (Rpp) params.append('size', Rpp.toString());
    if (search) params.append('search', search);

    // Filter parameters - only append if they have values
    if (filter?.status) params.append('ingredient_status', filter?.status);
    if (filter?.category) params.append('category', filter?.category);
    if (filter?.allergen) params.append('allergy', filter?.allergen);
    if (filter?.dietary) params.append('dietary', filter?.dietary);
    if (filter?.organizationId)
      params.append('organization_id', filter?.organizationId);

    // Sort parameters - only append if both key and value exist
    if (Sort?.key && Sort?.value) {
      params.append('sort_by', Sort?.key);
      params.append('sort_order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_INGREDIENTS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        ingredients: data?.data || [],
        totalCount: data?.count || 0,
        import_sample_file: data?.import_sample_file || '',
      };
    }

    return {
      ingredients: [],
      totalCount: 0,
      import_sample_file: '',
    };
  } catch (error) {
    console.error('Error fetching ingredient items list:', error);
    throw error;
  }
};

// API function to get single ingredient by ID
export const getIngredientById = async (ingredientId) => {
  try {
    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_INGREDIENT_BY_ID}/${ingredientId}`
    );

    if (status === 200) {
      return {
        ingredient: data?.data || null,
      };
    }
    return {
      ingredient: null,
    };
  } catch (error) {
    throw error;
  }
};

export const createIngredient = async (ingredientData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_INGREDIENT,
      ingredientData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create ingredient');
  } catch (error) {
    throw error;
  }
};

export const updateIngredient = async (ingredientId, ingredientData) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_INGREDIENT}/${ingredientId}`,
      ingredientData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update ingredient');
  } catch (error) {
    throw error;
  }
};

// API function to delete ingredient by ID
export const deleteIngredient = async (ingredientId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_INGREDIENT}/${ingredientId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete ingredient');
  } catch (error) {
    throw error;
  }
};

// API function to export ingredient items
export const exportIngredient = async (format) => {
  try {
    // Build query string from filter

    const response = await axiosInstance.get(
      `${RECIPE_URLS.EXPORT_INGREDIENT}/${format}`,
      { responseType: 'blob' }
    );
    return response;
  } catch (error) {
    throw error;
  }
};

// API function to import ingredient categories
export const importIngredientCategory = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.IMPORT_INGREDIENT,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to import ingredient categories');
  } catch (error) {
    throw error;
  }
};

// API function to get nutrition data
export const getNutritionList = async () => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    params.append('page', '1');
    params.append('limit', '');
    params.append('search', '');
    params.append('type', 'nutrition');
    params.append('status', 'active');
    params.append('sort_by', 'attribute_title');
    params.append('sort_order', 'ASC');

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        nutrition: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      nutrition: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

// API function to get dietary data
export const getDietaryList = async (search, page, filter, Rpp, Sort) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'dietary');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        dietary: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      dietary: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching dietary list:', error);
    throw error;
  }
};

// API function to get recipe measures data
export const getRecipeMeasuresList = async () => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    params.append('page', '1');
    params.append('limit', '');
    params.append('search', '');
    params.append('status', 'active');
    params.append('sort_by', 'unit_title');
    params.append('sort_order', 'asc');

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.RECIPE_MEASURES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        measures: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      measures: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getRecipeList = async (search, page, Rpp, filter) => {
  try {
    // Build query parameters
    const params = new URLSearchParams();

    // Basic parameters
    // params.append('page', page || 1);
    if (page) params.append('page', page);
    if (Rpp) params.append('limit', Rpp);
    // params.append('limit', Rpp || 10);

    if (search) params.append('search', search);

    // Filter parameters
    if (filter) {
      // Categories filter (array of category IDs)
      if (filter.categories && filter.categories.length > 0) {
        params.append('categories', filter.categories.join(','));
      }

      // Allergens filter (exclude recipes with these allergens)
      if (filter.allergens && filter.allergens.length > 0) {
        params.append('exclude_allergen', filter.allergens.join(','));
      }

      // Dietary filter (include recipes with these dietary attributes)
      if (filter.dietary && filter.dietary.length > 0) {
        params.append('dietary', filter.dietary.join(','));
      }

      // Recipe status filter
      if (filter.status) {
        params.append('recipe_status', filter.status);
      }

      // Difficulty level filter
      if (filter.difficulty) {
        params.append('recipe_complexity_level', filter.difficulty);
      }

      // Cost range filter
      if (filter.cost_min !== undefined) {
        params.append('portion_cost_min', filter.cost_min);
      }
      if (filter.cost_max !== undefined) {
        params.append('portion_cost_max', filter.cost_max);
      }

      // Cooking time filter
      if (filter.cooking_time_min !== undefined) {
        params.append('cooking_time_min', filter.cooking_time_min);
      }
      if (filter.cooking_time_max !== undefined) {
        params.append('cooking_time_max', filter.cooking_time_max);
      }

      // Bookmark filter
      if (filter.bookmarked !== undefined) {
        params.append('bookmark', filter.bookmarked === 1 ? true : false);
      }

      // Ownership filter
      if (filter.ownership) {
        params.append('ownership', filter.ownership);
      }
    }

    // Sort parameters
    // if (Sort) {
    //   params.append('sort_by', Sort.key || 'updated_at');
    //   params.append('sort_order', Sort.value || 'DESC');
    // } else {
    //   params.append('sort_by', 'updated_at');
    //   params.append('sort_order', 'DESC');
    // }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_RECIPES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        recipesList: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      recipesList: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getRecipeSettings = async () => {
  try {
    const { status, data } = await axiosInstance.get(
      RECIPE_URLS.RECIPES_SETTINGS
    );

    if (status === 200) {
      return data;
    }
    return {
      data: null,
      message: 'No settings found',
    };
  } catch (error) {
    console.error('Error fetching recipe settings:', error);
    throw error;
  }
};

export const updateRecipeSettings = async (settingsData) => {
  try {
    const { status, data } = await axiosInstance.put(
      RECIPE_URLS.UPDATE_RECIPES_SETTINGS,
      settingsData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe settings');
  } catch (error) {
    console.error('Error updating recipe settings:', error);
    throw error;
  }
};

export const updateBookmark = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.post(
      `${RECIPE_URLS.UPDATE_BOOKMARK}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update bookmark');
  } catch (error) {
    throw error;
  }
};

export const duplicateRecipe = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.post(
      `${RECIPE_URLS.DUPLICATE_RECIPE}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to duplicate recipe');
  } catch (error) {
    throw error;
  }
};

export const deleteRecipe = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_RECIPE}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete recipe');
  } catch (error) {
    throw error;
  }
};

export const createRecipe = async (recipeData) => {
  try {
    // Configure headers for FormData
    const config = {};
    if (recipeData instanceof FormData) {
      config.headers = {
        'Content-Type': 'multipart/form-data',
      };
    }

    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_RECIPES,
      recipeData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to add recipe');
  } catch (error) {
    throw error;
  }
};

export const updateRecipe = async (recipeId, recipeData) => {
  try {
    // Configure headers for FormData
    const config = {};
    if (recipeData instanceof FormData) {
      config.headers = {
        'Content-Type': 'multipart/form-data',
      };
    }

    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_RECIPES}/${recipeId}`,
      recipeData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe');
  } catch (error) {
    throw error;
  }
};

export const getAttributeList = async (
  search,
  page,
  filter,
  limit,
  sort,
  type
) => {
  try {
    const params = new URLSearchParams();

    if (search) params.append('search', search);
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    if (type) params.append('type', type);

    // Add filter parameters
    if (filter?.status) params.append('status', filter.status);

    // Add sort parameters
    if (sort?.key) params.append('sort_by', sort.key);
    if (sort?.value) params.append('sort_order', sort.value);

    const url = `${RECIPE_URLS.GET_ATTRIBUTES}?${params.toString()}`;

    const { status, data } = await axiosInstance.get(url);

    if (status === 200) {
      return {
        attributes: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      attributes: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getPublicRecipeList = async (search, page, Rpp, filter) => {
  try {
    // Build query parameters
    const params = new URLSearchParams();

    // Basic parameters
    if (page) params.append('page', page);
    if (Rpp) params.append('limit', Rpp);
    if (search) params.append('search', search);

    // Filter parameters for public recipes
    if (filter) {
      // Categories filter (array of category IDs)
      if (filter.categories && filter.categories.length > 0) {
        params.append('categories', filter.categories.join(','));
      }

      // Allergens filter (exclude recipes with these allergens)
      if (filter.allergens && filter.allergens.length > 0) {
        params.append('exclude_allergen', filter.allergens.join(','));
      }

      // Dietary filter (include recipes with these dietary attributes)
      if (filter.dietary && filter.dietary.length > 0) {
        params.append('dietary', filter.dietary.join(','));
      }

      // Difficulty level filter
      if (filter.difficulty) {
        params.append('recipe_complexity_level', filter.difficulty);
      }

      // Cost range filter
      if (filter.cost_min !== undefined) {
        params.append('portion_cost_min', filter.cost_min);
      }
      if (filter.cost_max !== undefined) {
        params.append('portion_cost_max', filter.cost_max);
      }

      // Cooking time filter
      if (filter.cooking_time_min !== undefined) {
        params.append('cooking_time_min', filter.cooking_time_min);
      }
      if (filter.cooking_time_max !== undefined) {
        params.append('cooking_time_max', filter.cooking_time_max);
      }
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_RECIPE_LIST}?${params.toString()}`
    );

    if (status === 200) {
      return {
        recipesList: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      recipesList: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

// Get recipe preview data with conditional API call based on route type
export const getRecipePreviewData = async (slug, pathname) => {
  const isPublic = isPublicRoute(pathname);

  let apiUrl;
  if (isPublic) {
    // For public routes, use public recipe preview API
    apiUrl = `${RECIPE_URLS.GET_PUBLIC_RECIPE_PREVIEW}/${slug}`;
  } else {
    // For private routes, use private recipe preview API
    apiUrl = `${RECIPE_URLS.RECIPE_PREVIEW}/${slug}`;
  }

  const { status, data } = await axiosInstance.get(apiUrl);

  if (status === 200 && data?.data) {
    return data.data;
  } else {
    throw new Error('Recipe not found');
  }
};

// Increment recipe impression count for public views
export const incrementRecipeImpression = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.post(
      `${RECIPE_URLS.INCREMENT_RECIPE_IMPRESSION}/${recipeId}`
    );

    if (status === 200) {
      return data;
    }
    return null;
  } catch (error) {
    console.error('Error incrementing recipe impression:', error);
    // Don't throw error as this is not critical functionality
    return null;
  }
};

export const getRecipeHistory = async (recipeId, page, size) => {
  const params = new URLSearchParams();

  if (page) params.append('page', page);
  if (size) params.append('limit', size);

  try {
    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_RECIPE_HISTORY}/${recipeId}?${params.toString()}`
    );

    if (status === 200) {
      return {
        history: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      history: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching recipe history:', error);
    throw error;
  }
};
