'use client';

import React, { createContext, useState, useEffect, Suspense } from 'react';
import AuthContext from '@/helper/authcontext';
import CssBaseline from '@mui/material/CssBaseline';
import { ToastContainer } from 'react-toastify';
import OneSignalInit from '@/components/OneSignalInit';
import { saveToStorage, fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import axiosInstance from '@/helper/axios/axiosInstance';
import { usePathname, useRouter } from 'next/navigation';
import DialogBox from '@/components/UI/Modalbox';
import RestrictedModal from '@/components/UI/RestrictedModal';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';
import 'react-toastify/dist/ReactToastify.css';
import './globals.scss';
import './_globals.scss';

const Context = createContext();

export default function RootLayout({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  const authdata =
    typeof window !== 'undefined' && fetchFromStorage(identifiers?.AUTH_DATA);
  const userDetails =
    typeof window !== 'undefined' && fetchFromStorage(identifiers?.USER_DATA);
  const deviceDetails =
    typeof window !== 'undefined' && fetchFromStorage(identifiers?.DEVICEDATA);

  const [authState, setAuthState] = useState({
    username: '',
  });
  const [userdata, setUserdata] = useState({
    username: '',
  });
  const [folderdata, setfolderdata] = useState({
    data: '',
  });
  const [planDetail, setPlanDetail] = useState({
    data: '',
  });
  const [orgDetails, setOrgDetails] = useState({
    data: '',
  });
  let [reFetch, setReFetch] = useState(false);
  const [isActive, setIsActive] = useState('');
  const [IsDrawer, setIsDrawer] = useState('');
  const menuMessages = {
    en: {},
  };
  const [locale, setLocale] = useState('en');
  const [messages, setMessages] = useState(menuMessages['en']);
  const [AllListsData, setAllListsData] = useState({
    branchList: [],
    ActiveBranchList: [],
    SelectBranchList: [],
    ActiveDepartmentList: [],
    SelectDepartmentList: [],
  });
  const [restrictedModal, setRestrictedModal] = useState(false);
  const [restrictedLimitModal, setRestrictedLimitModal] = useState(null);

  const switchLanguage = (lang) => {
    setLocale(lang);
    setMessages(menuMessages[lang]);
  };
  // GET IP ADDRESS
  const getIPAddress = async () => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      if (response.ok) {
        const data = await response.json();
        const deviceData = { ip: data?.ip };

        const locationResp = await axiosInstance.get(
          `https://ipinfo.io/${data?.ip}/geo?token=f958fa1525009d`
        );
        if (locationResp?.data) {
          const locationData = locationResp?.data;
          deviceData.address =
            `${locationData?.city},${locationData?.region},${locationData?.country},${locationData?.postal}` ||
            '';
          deviceData.location = locationData?.loc || '';
        }
        saveToStorage(identifiers?.DEVICEDATA, deviceData);
      } else {
        console.error('Failed to fetch IP address:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching IP address:', error);
    }
  };

  useEffect(() => {
    if (userDetails && authdata) {
      const isNormalUser = fetchFromStorage(identifiers?.NORMAL_LOGIN);
      const LOGIN_ORG = fetchFromStorage(identifiers?.LOGIN_ORG);
      const isProtectedRoute =
        pathname.startsWith('/org') || pathname.startsWith('/sorg');

      const isOrgView = checkOrganizationRole('org_master') || false;
      const isRestricted = isNormalUser
        ? false
        : isOrgView
          ? !orgDetails?.attributes?.email ||
            !orgDetails?.attributes?.contact_person ||
            userDetails?.user?.purchase_plan === false
          : !userDetails?.profile_status;

      const hasAccess = !isProtectedRoute && isRestricted;

      if (hasAccess) {
        if (isOrgView) {
          if (pathname === '/org/organization') {
            setRestrictedModal({
              isOrgView: isOrgView,
              purchase_plan: userDetails?.user?.purchase_plan,
              user_status:
                !orgDetails?.attributes?.email ||
                !orgDetails?.attributes?.contact_person,
              profile_status: userDetails?.profile_status,
            });
          } else {
            router?.push('/org/organization');
          }
        } else {
          if (pathname === '/myprofile' && !LOGIN_ORG) {
            setRestrictedModal({
              isOrgView: isOrgView,
              purchase_plan: userDetails?.user?.purchase_plan,
              profile_status: userDetails?.profile_status,
            });
          } else if (!LOGIN_ORG) {
            router?.push('/myprofile');
          }
        }
      }
    }
  }, [pathname, userDetails?.id]); //authdata, userDetails,

  useEffect(() => {
    if (deviceDetails === undefined || deviceDetails === null) {
      getIPAddress();
    }
  }, []);

  const handleConfirmRestricted = () => {
    const isOrgView = checkOrganizationRole('org_master') || false;

    if (isOrgView) {
      if (pathname === '/org/organization') {
        setRestrictedModal(false);
      } else {
        router?.push('/org/organization');
      }
    } else {
      if (pathname === '/myprofile') {
        setRestrictedModal(false);
      } else {
        router?.push('/myprofile');
      }
    }
  };

  const handleConfirmRestrictedLimit = () => {
    const redirectTo = restrictedLimitModal === 'staff' ? '/staff' : '';
    if (redirectTo) {
      router?.push(redirectTo);
    }
    setRestrictedLimitModal(null);
  };
  return (
    <html lang="en">
      <head>
        <link
          rel="icon"
          type="image/svg"
          sizes="16x16"
          href="/images/favicon.svg"
        />
        <link rel="manifest" href="/images/site.webmanifest" />
        <meta name="robots" content="index, follow" />
        <meta name="googlebot" content="index, follow" />
      </head>
      <body>
        <Suspense>
          <Context.Provider value={{ locale, switchLanguage, messages }}>
            <AuthContext.Provider
              value={{
                authState,
                setAuthState,
                userdata,
                setUserdata,
                folderdata,
                setfolderdata,
                reFetch,
                setReFetch,
                isActive,
                AllListsData,
                setAllListsData,
                setIsActive,
                IsDrawer,
                setIsDrawer,
                restrictedModal,
                setRestrictedModal,
                restrictedLimitModal,
                setRestrictedLimitModal,
                planDetail,
                setPlanDetail,
                orgDetails,
                setOrgDetails,
              }}
            >
              <CssBaseline />
              {authState?.user_email && <OneSignalInit />}
              {children}
              <ToastContainer style={{ padding: '5px' }} limit={1} />
              {restrictedModal && (
                <DialogBox
                  open={restrictedModal}
                  handleClose={() => {
                    setRestrictedModal(false);
                  }}
                  className="restricted-modal"
                  title={
                    restrictedModal?.user_status &&
                    restrictedModal?.purchase_plan === false
                      ? 'Complete your profile & plan'
                      : restrictedModal?.purchase_plan === false
                        ? 'Update your plan'
                        : 'Complete your profile'
                  }
                  // onCloseStatus={true}
                  closeIconShow={true}
                  content={
                    <RestrictedModal
                      handleConfirm={() => {
                        handleConfirmRestricted();
                      }}
                      restrictedModal={restrictedModal}
                    />
                  }
                />
              )}
              {!!restrictedLimitModal && (
                <DialogBox
                  open={!!restrictedLimitModal}
                  handleClose={() => {
                    setRestrictedLimitModal(null);
                  }}
                  closeIconShow={true}
                  className="restricted-modal"
                  title="Limit exceed"
                  content={
                    <RestrictedModal
                      handleConfirm={() => {
                        handleConfirmRestrictedLimit();
                      }}
                      limit={true}
                    />
                  }
                />
              )}
            </AuthContext.Provider>
          </Context.Provider>
        </Suspense>
      </body>
    </html>
  );
}
export { Context as IntlContext };
