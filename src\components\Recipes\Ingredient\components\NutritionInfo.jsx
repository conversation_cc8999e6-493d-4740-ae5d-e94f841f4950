import { Box, Typography, Tooltip } from '@mui/material';
import { allowedOnlyNumbers } from '@/helper/common/commonFunctions';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import InfoIcon from '@mui/icons-material/Info';
import NoDataView from '@/components/UI/NoDataView';

const NutritionInfo = ({
  values,
  // errors,
  // touched,
  handleBlur,
  handleChange,
  setFieldValue,
  isDefault,
  nutritionFields,
  nutritionOptions,
}) => {
  return (
    <>
      <Box className="d-flex align-center gap-sm pt16 pb8">
        <Typography className="sub-header-text">Nutrition</Typography>
        <Tooltip
          arrow
          title={
            <Typography className="sub-title-text">
              Nutrition values per 100g.
            </Typography>
          }
          classes={{ tooltip: 'info-tooltip-container' }}
        >
          <InfoIcon className="info-icon cursor-pointer" />
        </Tooltip>
      </Box>
      {!nutritionFields || nutritionFields.length === 0 ? (
        <NoDataView
          title="No Nutrition Fields Available"
          description="There are no nutrition fields configured at the moment."
        />
      ) : (
        <Box className="ingredient-nutrition-main-grid-container">
          {nutritionFields?.map((field) => {
            return (
              <Box
                key={field?.id}
                className="ingredient-nutrition-grid-container"
              >
                <Box className="nutrition-value-input">
                  <CustomTextField
                    fullWidth
                    name={`nutrition_${field?.id}`}
                    value={values?.[`nutrition_${field?.id}`]}
                    label={field?.attribute_title}
                    placeholder={`${field?.attribute_title}`}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    disabled={isDefault}
                    onInput={(e) => allowedOnlyNumbers(e)}
                  />
                </Box>
                <Box className="nutrition-measure-select">
                  <CustomSelect
                    fullWidth
                    label="&nbsp;"
                    name={`nutrition_measure_${field?.id}`}
                    placeholder=""
                    options={nutritionOptions}
                    value={
                      nutritionOptions?.find(
                        (opt) =>
                          opt?.id === values?.[`nutrition_measure_${field?.id}`]
                      ) || ''
                    }
                    onChange={(selectedOption) =>
                      setFieldValue(
                        `nutrition_measure_${field?.id}`,
                        selectedOption?.id || 1
                      )
                    }
                    isDisabled={isDefault}
                    isClearable={false}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>
              </Box>
            );
          })}
        </Box>
      )}
    </>
  );
};

export default NutritionInfo;
