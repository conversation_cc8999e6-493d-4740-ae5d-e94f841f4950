@import '@/app/_globals.scss';

.form-wrap {
    .custom-select-wrap {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);

        @media(max-width: 767px) {
            grid-template-columns: repeat(1, 1fr);
            gap: 0;
        }
    }

    .custom-date-time {
        .MuiInputBase-root {
            .MuiInputBase-input {
                padding: var(--spacing-xxs) var(--spacing-base);
            }

            fieldset {
                height: 38px;
            }
        }
    }

    .followers-wrap {
        padding: var(--spacing-lg) 0px;
        gap: 50px;

        .followers-list-wrap {

            .followers-list {
                gap: var(--spacing-xxs);
                display: flex;
                flex-wrap: wrap;

                .follower-item {
                    position: relative;

                    .follower-img {
                        border-radius: var(--border-radius-full);
                        width: var(--spacing-2xl);
                        height: var(--spacing-2xl);
                        display: block;
                    }

                    &:hover::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, var(--opacity-5));
                        border-radius: var(--border-radius-full);
                        z-index: 1;
                    }

                    .close-icon-wrap {
                        position: absolute;
                        left: 51%;
                        bottom: 43%;
                        transform: translate(-50%, 50%);
                        opacity: 0;
                        z-index: 2;
                        transition: opacity 0.3s ease;

                        .close-icon {
                            fill: var(--color-white);
                            height: var(--icon-size-xs);
                            width: var(--icon-size-xs);
                        }
                    }

                    &:hover .close-icon-wrap {
                        opacity: 1;
                    }
                }
            }

            .add-follower-wrap {
                opacity: 0;
                transition: opacity 0.3s ease;

                .add-btn {
                    fill: var(--color-light-dark);
                    height: var(--icon-size-xsm);
                    width: var(--icon-size-xsm);
                    transition: fill 0.3s ease;
                }

                .add-follow-text {
                    color: var(--color-light-dark);
                    font-size: var(--font-size-sm);
                    font-family: var(--font-family-primary);
                    transition: color 0.3s ease;
                }

                &:hover {
                    opacity: 1;

                    .add-btn {
                        fill: var(--color-blue);
                    }

                    .add-follow-text {
                        color: var(--color-blue);
                    }
                }
            }

            .followers-menu {
                .followers-search {

                    .select__control {

                        padding: 0px;

                        .select__value-container {
                            padding: 0px;

                            .select__single-value {
                                margin-left: var(--spacing-base);
                                margin-top: var(--spacing-xxs);
                            }

                            .select__input-container {
                                margin: 0px 0px 0px var(--spacing-base);
                            }

                            .select__placeholder {
                                padding: var(--spacing-xxs) 0px 0px var(--spacing-base);
                                font-family: var(--font-family-primary);
                            }
                        }

                        .select__indicators {
                            .select__indicator {
                                svg {
                                    color: var(--color-dark);
                                }
                            }
                        }
                    }
                }

            }

        }
    }

    .buttons-wrap {

        .cancel-btn,
        .submit-btn {
            padding: var(--spacing-xs) var(--spacing-lg) !important;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            font-family: var(--font-family-primary);

            &:hover {
                color: var(--color-white) !important;
                box-shadow: none !important;
            }
        }

        .cancel-btn {
            background-color: var(--color-white) !important;
            color: var(--color-primary) !important;

            &:hover {
                color: var(--color-primary) !important;
            }
        }

    }

    .select {
        .MuiSelect-select {
            padding: var(--field-padding-sm);
        }
    }

    .select-assignee-wrap {
        .assignee-text {
            width: 100%;
            max-width: 200px;
            font-family: var(--font-family-primary);
        }

        .assignee-select {
            width: 100%;
            max-width: var(--search-max-width, 300px);
        }

        .slected-wrap {
            .select__control {

                padding: 0px;

                .select__value-container {
                    padding: 0px;

                    .select__single-value {
                        margin-left: var(--spacing-base);
                        margin-top: var(--spacing-xxs);
                    }

                    .select__input-container {
                        margin: 0px 0px 0px var(--spacing-base);
                    }

                    .select__placeholder {
                        padding: var(--spacing-xxs) 0px 0px var(--spacing-base);
                        font-family: var(--font-family-primary);
                    }
                }

                .select__indicators {
                    .select__indicator {
                        svg {
                            color: var(--color-dark);
                        }
                    }
                }
            }
        }
    }

}