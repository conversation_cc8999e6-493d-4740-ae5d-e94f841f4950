@import url('https://fonts.googleapis.com/css2?family=Red+Hat+Display:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap');

@font-face {
  font-family: 'PolySans Trial Bulky';
  src: url('/font/PolySansTrial-Bulky.otf');
}

@font-face {
  font-family: 'PolySans Trial Bulky Mono';
  src: url('/font/PolySansTrial-BulkyMono.otf');
}

@font-face {
  font-family: 'PolySans Trial Median';
  src: url('/font/PolySansTrial-Median.otf');
}

@font-face {
  font-family: 'PolySans Trial Neutral';
  src: url('/font/PolySansTrial-Neutral.otf');
}

@font-face {
  font-family: 'PolySans Trial Slim';
  src: url('/font/PolySansTrial-Slim.otf');
}

@font-face {
  font-family: 'Brown-Light';
  src: url('/font/Brown-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Brown-Regular';
  src: url('/font/Brown-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Brown-Bold';
  src: url('/font/Brown-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

:root {
  // Font Family
  --font-family-primary: 'Poppins', sans-serif;
  --font-family-poly-bulky: 'PolySans Trial Bulky';
  --font-family-poly-bulky-mono: 'PolySans Trial Bulky Mono';
  --font-family-poly-median: 'PolySans Trial Median';
  --font-family-poly-neutral: 'PolySans Trial Neutral';
  --font-family-poly-slim: 'PolySans Trial Slim';
  --font-family-brown-light: 'Brown-Light';
  --font-family-brown-regular: 'Brown-Regular';
  --font-family-brown-bold: 'Brown-Bold';

  // Colors
  --color-primary: #135e96; // #006bff;
  --color-secondary: #f7f7f7;
  --color-secondary-auth: #e8f6ff;
  --color-white: #ffffff;
  --color-success: #038d2a;
  --color-success-opacity: rgba(3, 141, 42, 0.1);
  --color-danger: #d32f2f;
  --color-danger-background: #fce9ea;
  --color-danger-opacity: rgba(211, 47, 47, 0.1);
  --color-primary-opacity: rgba(0, 107, 255, 0.1);
  --color-warning: #db972c;
  --color-warning-opacity: rgba(219, 151, 44, 0.1);
  --color-orange: #fd7e14;
  --color-light-success: #7daf1a;
  --color-light-success-opacity: rgba(125, 175, 26, 0.1);
  --list-dept-color: #845adf;
  --list-branch-color: #730000;
  --color-dark-blue: #394392;
  --color-blue: #006bff;
  // --color-info: #17a2b8;
  --color-light-gray: #cccccc;
  --color-dark: #45464e;
  --color-light-dark: #6e7079;
  --color-light-blue: #eef0fc;
  --color-black: #000000;
  --color-soft-lavender: #eef0fc;
  --color-dark-lavender: #c4cae8;
  --color-light-grayish-blue: #d9dae2;
  --color-alice-blue: #f5f8fa;
  --color-ice-blue: #f6fcfe;
  --color-green: #038d2a;
  --color-light-green: #f9ffed;
  --color-light-champagne: #fef4e6;
  --color-muted-mustard: #db972c;
  --color-muted-mustard-opacity: rgba(219, 151, 44, 0.1);
  --color-off-white: #fafafb;
  --color-light-success: #7daf1a;
  --color-dark-50: #53545c;
  --intergalactic-control-primary-info: #135e96; // #006bff;
  --color-muted: #adb5bd;
  --color-holiday: #029ac4;
  --color-light-holiday: #33d6fa;
  --color-bg-holiday: #99eafc;
  --color-chip: #89cefb4d;
  --color-soft-beige: #f6f4ee;
  --color-charcoal-gray: #333;
  --color-dark-medium-gray: #4d4d4d; // Text Colors
  --color-mint-light: #d6f4de;
  --color-pink: #e91e63;
  --color-purple: #9c27b0;

  --text-color-primary: #135e96; // #006bff;
  // --text-color-secondary: #f7f7f7;
  --text-color-muted: #adb5bd;
  --text-color-white: #ffffff;
  --text-color-black: #000000;
  --text-color-slate-gray: #8b8d97;
  --text-color-danger: #d32f2f;
  --text-bright-blue: #135e96; // #006bff;
  --text-blue: #006bff;
  --text-error: #d32f2f;
  --text-steel-blue: #8ba2b4;
  --text-green: #038d2a;
  --text-muted-mustard: #db972c;
  --text-dark: #45464e;
  --text-periwinkle-blue: #5570f1;
  --text-olive-green: '#7daf1a';
  --text-light-dark: #6e7079;
  --text-lavender-gray: #92929d;
  --text-slate-gray: #808d9e;
  --text-charcoal-gray: #333;
  --text-lime-green: #3cc960;

  // Button padding
  --btn-padding-sm: 7px 12px;
  --btn-icon-padding-sm: 8px 12px;
  // --btn-padding-md: 9px 12px;

  // Button Colors
  --btn-color-primary: #135e96; // #006bff;
  --btn-text-color-white: #ffffff;
  --btn-color-bright-blue: #135e96; //#006bff;
  --btn-color-secondary: #ebefec;
  --btn-color-danger: #d32f2f;
  --btn-border-color-danger: #d32f2f;
  --btn-color-dark: #45464e;
  --btn-icon-border-radius: 8px;

  // Font Sizes
  --font-size-tiny: 9px;
  --font-size-xxs: 11px;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-md: 18px;
  --font-size-lg: 20px;
  --font-size-xl: 24px;
  --font-size-2xl: 28px;
  --font-size-3xl: 32px;
  --font-size-4xl: 40px;
  --font-size-6xl: 42px;
  // --font-size-5xl: 48px;

  // Font Weights
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  // --font-weight-extrabold: 800;

  // Line Heights
  --line-height-xxs: 0.8rem;
  --line-height-xs: 1.25rem;
  --line-height-sm: 1.3;
  --line-height-sm-base: 1.4;
  --line-height-base: 1.5;
  --line-height-md: 1.6;
  --line-height-lg: 1.8;
  --line-height-xl: 2;

  // Letter Spacing
  // --letter-spacing-tight: -0.02em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.05em;

  // Padding & Margin Spacing
  --spacing-none: 0px;
  --spacing-tiny: 1px;
  --spacing-xxs: 2px;
  --spacing-xs: 4px;
  --spacing-xsm: 6px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-base: 14px;
  --spacing-lg: 16px;
  --spacing-xl: 18px;
  --spacing-xxl: 24px;
  --spacing-2xl: 32px;
  --spacing-3xl: 40px;
  --spacing-4xl: 48px;
  --spacing-5xl: 51px;
  --spacing-6xl: 60px;
  --spacing-7xl: 82px;

  // Border Redius
  --border-radius-none: 0px;
  --border-radius-xs: 4px;
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-2xl: 22px;
  --border-radius-xxl: 30px;
  --border-radius-full: 999px;
  --border-radius-sm-top: 6px 6px 0 0;
  --border-radius-sm-bottom: 0 0 6px 6px;

  // Icon Colors
  --icon-color-primary: #135e96; // #006bff;
  --icon-color-white: #ffffff;
  --icon-color-light-dark: #6e7079;
  --icon-color-black: #000000;
  --icon-color-slate-gray: #8b8d97;
  --icon-bold-red-color: #d32f2f;
  --icon-color-green: #038d2a;
  --icon-color-warning: #db972c;
  --icon-color-orange: #fd7e14;
  --icon-charcoal-gray: #333;

  --action-icon-bg-color: #ebebeb;
  --action-icon-size: 28px;

  // Icon Sizes
  --icon-size-xxs-min: 10px;
  --icon-size-xxs: 14px;
  --icon-size-xs: 16px;
  --icon-size-xsm: 18px;
  --icon-size-sm: 20px;
  --icon-size-md: 24px;
  --icon-size-lg: 32px;
  --field-icon-size: 18px;
  --icon-size-xl: 40px;

  // Box Shadow
  --box-shadow-xs: 0 2px 4px rgba(0, 0, 0, 0.1);
  // --box-shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  // --box-shadow-md: 0 8px 12px rgba(0, 0, 0, 0.1);
  // --box-shadow-lg: 0 12px 16px rgba(0, 0, 0, 0.1);
  --tab-box-shadow-xs: 0px 0px 2px #135e96; // #006bff;

  // Border Widths
  --border-width-xs: 1px;
  --border-width-sm: 2px;
  --border-width-md: 3px;
  --border-width-lg: 4px;

  // Border Styles
  --border-style-solid: solid;
  // --border-style-dashed: dashed;
  // --border-style-dotted: dotted;

  // Borders
  --normal-sec-border: 1px solid #d9dae2;
  --table-border: 1px solid #6e7079;
  --normal-dashed-sec-border: 1px dashed lightGray;

  // Border Colors
  --border-color-light-gray: #cccccc;
  --border-color-green: #038d2a;
  --border-color-red: #d32f2f;
  --border-color-white: #ffffff;
  // --border-color-dark: #343a40;
  --border-color-primary: #135e96; // #006bff;
  --border-color-secondary: #f7f7f7;
  --border-color-muted: #adb5bd;
  --border-color-white: #ffffff;
  --border-color-error: #d32f2f;
  --border-color-muted-mustard: #db972c;
  --border-color-warning: #db972c;
  --border-color-charcoal-gray: #333;
  --border-lime-green: #3cc960;

  // Radio Button
  --radio-btn-check-color-primary: #135e96; // #006bff;
  --radio-btn-uncheck-color-primary: #6e7079;
  --radio-btn-spacing: 5px;
  --radio-btn-icon-size: 20px;
  --radio-btn-gap: 2px;
  --radio-btn-font-size: 14px;
  --check-box-icon-size: 22px;

  // Field Style
  --field-border: 1px solid #adb5bd;
  --field-radius: 8px;
  --field-radius-left: 8px 0px 0px 8px;
  --field-radius-right: 0px 8px 8px 0px;
  --field-padding: 8px 12px;
  --field-padding-sm: 10px 12px;
  --field-border-primary: 1px solid #135e96; //#006bff;
  --filled-border: 1px solid #6e7079;
  --normal-border: 1px solid #cccccc;
  --field-background: #f7f7f7;
  --field-placeholder: #8b8d97;

  // Opacity
  --opacity-1: 0.1;
  --opacity-2: 0.2;
  --opacity-3: 0.3;
  --opacity-4: 0.4;
  --opacity-5: 0.5;
  --opacity-6: 0.6;
  --opacity-7: 0.7;
  --opacity-8: 0.8;
  --opacity-9: 0.9;
  --opacity-10: 1;
}

body {
  .sub-header-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-black);
    line-height: var(--line-height-base);
  }
  .sub-content-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-black);
    line-height: var(--line-height-base);
  }
  .content-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-regular);
    color: var(--text-color-slate-gray);
    line-height: var(--line-height-base);
  }
  .title-text {
    // use instead of p14
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
    color: var(--text-color-black);
    line-height: var(--line-height-base);
  }
  .sub-title-text {
    // use instead of p12
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-regular);
    color: var(--text-color-black);
    line-height: var(--line-height-base);
  }
  .title-md {
    font-size: var(--font-size-xl);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-md);
    color: var(--text-color-black);
  }
  .title-sm {
    // use instead of p20
    font-size: var(--font-size-lg);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-base);
    color: var(--text-color-black);
  }
  .body-text {
    // use instead of p16
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-base);
    color: var(--text-color-black);
  }
  .body-semibold {
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold); // 600 weight
    line-height: var(--line-height-base);
    color: var(--text-color-black);
  }
  .body-sm {
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-sm);
    color: var(--text-color-black);
  }
  .body-sm-regular {
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-sm);
    color: var(--text-color-black);
  }
  .caption-text {
    font-size: var(--font-size-xs);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-base);
    color: var(--text-color-black);
  }
  .content-text-sm {
    font-size: var(--font-size-xxs);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-base);
    color: var(--text-color-black);
  }

  // Other common styles
  .field-label {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    display: flex;
    align-items: center;
    &.error-label {
      color: var(--text-error);
    }
    .label-icon {
      width: var(--font-size-sm);
      height: var(--font-size-sm);
      cursor: pointer;
    }
    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
    .field-info {
      display: flex;
      align-items: center;
      max-width: max-content;
      .info-icon {
        width: var(--font-size-sm);
        height: var(--font-size-sm);
        cursor: pointer;
      }
    }
  }
  .create-cancel-button {
    display: flex;
    column-gap: var(--spacing-lg);

    @media (max-width: 399px) {
      display: block;

      button {
        width: 100% !important;
      }

      button {
        margin-top: 16px;
      }

      button:first-child {
        margin-top: 0 !important;
      }
    }
  }
  .table-layout {
    box-shadow: none;
    padding: var(--spacing-none);
    margin: var(--spacing-none);
    .MuiDataGrid-root {
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-md);
      padding-bottom: var(--spacing-none);
      margin-top: var(--spacing-xl);
      overflow: hidden;
      .MuiDataGrid-columnHeaders {
        .MuiDataGrid-row--borderBottom {
          background-color: var(--color-off-white);
        }
      }
    }
    .MuiDataGrid-cell {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      display: flex;
      align-items: center;
    }
    .MuiDataGrid-columnHeaderTitle,
    .MuiDataGrid-columnHeaderTitleContainerContent span {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .MuiDataGrid-columnHeaderTitleContainerContent {
      svg {
        height: var(--icon-size-xs);
        width: var(--icon-size-xs);
        fill: var(--icon-color-black);
      }
      .table-checkbox {
        padding-top: 0;
        padding-bottom: 0;
        svg {
          height: var(--icon-size-md);
          width: var(--icon-size-md);
          fill: var(--icon-color-black);
        }
      }
    }
  }
  .failed {
    border: 1px solid var(--color-danger) !important;
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-danger-background) !important;
    color: var(--color-danger) !important;
    text-transform: capitalize;
  }
  .cancelled {
    border: 1px solid var(--color-light-grayish-blue) !important;
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-danger-background) !important;
    color: var(--color-danger) !important;
    text-transform: capitalize;
  }
  .active-onboarding {
    border: 1px solid var(--color-green) !important;
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-light-green) !important;
    color: var(--text-green) !important;
    text-transform: capitalize;
  }
  .status-yellow {
    border: 1px solid var(--color-muted-mustard) !important;
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-white) !important;
    color: var(--color-muted-mustard) !important;
    text-transform: capitalize;
  }
  .success {
    border: 1px solid var(--color-light-grayish-blue) !important;
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-light-green) !important;
    color: var(--color-light-success) !important;
    text-transform: capitalize;
  }
  .draft {
    border: 1px solid var(--color-blue) !important; //color-Primary-100
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-soft-lavender) !important;
    color: var(--text-blue) !important; //color-Primary-100
    text-transform: capitalize;
  }
  .ongoing {
    border: 1px solid var(--color-blue) !important; //color-Primary-100
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-white) !important;
    color: var(--text-blue) !important; //color-Primary-100
    text-transform: capitalize;
  }
  .closed {
    border: 1px solid var(--color-dark-50);
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-light-grayish-blue);
    color: var(--color-dark-50);
    text-transform: capitalize;
  }
  .default-text {
    background-color: var(--color-warning);
    color: var(--text-color-white);
    border: 1px solid var(--border-color-warning);
    padding: 0px 5px;
    border-radius: var(--border-radius-xs);
  }

  .default-d-text {
    font-family: var(--font-family-primary);
    background-color: var(--color-warning);
    color: var(--text-color-white);
    border: 1px solid var(--border-color-warning);
    padding: 0px 3px;
    border-radius: var(--border-radius-xs);
    font-size: var(--font-size-tiny);
    line-height: var(--line-height-sm-base);
  }

  .annual-text {
    background-color: var(--color-primary);
    color: var(--text-color-white);
    border: var(--field-border-primary);
    padding: 2px 5px;
    border-radius: var(--border-radius-xs);
  }

  .status-active {
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--border-radius-xs);
    border: var(--normal-sec-border);
    background: var(--color-success-opacity);
    color: var(--color-success);
    text-transform: capitalize;
  }

  .status-inactive {
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--border-radius-xs);
    border: var(--normal-sec-border);
    color: var(--text-color-danger);
    background-color: var(--color-danger-background);
    text-transform: capitalize;
  }

  .label-inactive {
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    border: var(--normal-sec-border);
    border-color: var(--border-color-red);
    color: var(--text-color-danger);
    background-color: var(--color-danger-background);
    text-transform: capitalize;
  }
  .label-active {
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    border: var(--normal-sec-border);
    color: var(--text-lime-green);
    border-color: var(--border-lime-green);
    background-color: var(--color-mint-light);
    text-transform: capitalize;
  }

  .image-placeholder {
    fill: var(--icon-color-primary);
  }

  .edit-action-icon {
    path {
      stroke: var(--icon-color-green);
    }
  }
  .delete-action-icon {
    fill: var(--icon-bold-red-color);
  }
  .top-back-page-button-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    padding: var(--spacing-md);
    .back-button {
      display: flex;
      svg {
        width: var(--icon-size-md);
        height: var(--icon-size-md);
        cursor: pointer;
      }
    }
  }
  .filter-options-drawer {
    .MuiPaper-root {
      width: 300px !important;
      min-width: 300px !important;
      padding: var(--spacing-md) var(--spacing-base) var(--spacing-xl);
    }
    .drawer-header {
      align-items: center !important;
      .cross-icon {
        padding: var(--spacing-none);
        .svg-icon {
          font-size: var(--font-size-2xl);
          border: var(--normal-sec-border);
          border-radius: var(--border-radius-sm);
          padding: var(--spacing-xxs);
          background: var(--color-secondary);
          path {
            fill: var(--icon-color-light-dark);
          }
        }
      }
    }
    .staff-filter {
      .reset-wrap {
        &:hover {
          color: var(--text-color-primary);
          svg {
            path {
              fill: var(--icon-color-primary) !important;
            }
          }
        }
      }
    }
    .pin-icon {
      path {
        fill: var(--icon-color-primary);
      }
    }
  }
  .brown-text {
    font-family: var(--font-family-brown-regular);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.6;
    color: var(--text-color-black);
    letter-spacing: var(--letter-spacing-wide);
  }
  .brown-content-text {
    font-family: var(--font-family-brown-regular);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-regular);
    line-height: 1.6;
    color: var(--text-color-black);
    letter-spacing: var(--letter-spacing-wide);
  }
  .brown-content-small-text {
    font-family: var(--font-family-brown-regular);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-regular);
    color: var(--text-color-black);
    letter-spacing: var(--letter-spacing-wide);
  }
  .brown-title-text {
    font-family: var(--font-family-brown-regular);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-regular);
    line-height: 1.6;
    color: var(--text-color-black);
    letter-spacing: var(--letter-spacing-wide);
  }

  .message-button-wrap {
    .message-button {
      background-color: var(--color-charcoal-gray) !important;
      border-color: var(--border-color-charcoal-gray) !important;
      border-radius: 0px;
      padding: var(--spacing-md);
      &:hover {
        background-color: var(--color-dark-medium-gray) !important;
      }
    }
  }

}

.unAuth-page-container {
  padding-top: var(--spacing-2xl);
}
