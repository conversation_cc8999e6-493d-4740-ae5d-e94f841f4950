.recent-activity {
  background-color: var(--color-white);
  border: var(--border-width-xs) solid var(--color-light-gray);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-xs);
  height: 100%;
  display: flex;
  flex-direction: column;

  // Header Section
  &__header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid var(--color-light-grayish-blue);
  }

  &__header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-xl);
    color: var(--text-color-primary);
  }

  &__view-all-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    text-decoration: none;
    transition: color 0.15s ease-out;

    &:hover {
      color: var(--color-dark-blue);
    }
  }

  // Filter Tabs
  &__filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  &__filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border: none;
    background: transparent;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-slate-gray);
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
    }
  }

  // Content Section
  &__content {
    padding: var(--spacing-lg);
    flex: 1;
    overflow: hidden;
  }

  // Loading State
  &__loading {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__skeleton {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    animation: pulse 1.5s ease-in-out infinite;
  }

  &__skeleton-avatar {
    width: var(--spacing-3xl);
    height: var(--spacing-3xl);
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-full);
    flex-shrink: 0;
  }

  &__skeleton-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__skeleton-line {
    height: var(--spacing-lg);
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-xs);

    &--long {
      width: 75%;
    }

    &--short {
      width: 50%;
    }
  }

  // Empty State
  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl) var(--spacing-lg);
    text-align: center;
  }

  &__empty-icon {
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-md);
  }

  &__empty-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
  }

  // Activity List
  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    max-height: 24rem;
    overflow-y: auto;
    padding-right: var(--spacing-xs);

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: var(--color-off-white);
      border-radius: var(--border-radius-xs);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-light-gray);
      border-radius: var(--border-radius-xs);

      &:hover {
        background: var(--color-primary);
      }
    }
  }

  // Activity Item
  &__item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    transition: all 0.15s ease-out;

    &:hover {
      transform: translateX(2px);
    }
  }

  // Activity Icon
  &__icon {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-full);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    // Color variants
    &--success {
      background-color: var(--color-success-opacity);
      color: var(--color-success);
    }

    &--primary {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--accent {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
    }

    &--warning {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
    }

    &--secondary {
      background-color: var(--color-off-white);
      color: var(--text-color-slate-gray);
    }

    &--error {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);
    }

    &--neutral {
      background-color: var(--color-off-white);
      color: var(--text-color-slate-gray);
    }
  }

  // Item Content
  &__item-content {
    flex: 1;
    min-width: 0;
  }

  &__item-main {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: var(--spacing-md);
  }

  &__item-details {
    flex: 1;
    min-width: 0;
  }

  // User Info
  &__user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-tiny);
  }

  &__avatar {
    width: var(--spacing-lg);
    height: var(--spacing-lg);
    border-radius: var(--border-radius-full);
    overflow: hidden;
    flex-shrink: 0;

    &--placeholder {
      background-color: var(--color-light-gray);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-color-slate-gray);
    }
  }

  &__avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__user-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__user-role {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  // Activity Description
  &__description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-tiny);
  }

  &__target {
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  // Metadata
  &__metadata {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  // Timestamp
  &__timestamp {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    white-space: nowrap;
    flex-shrink: 0;
  }

  // Footer Section
  &__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: var(--border-width-xs) solid var(--color-light-grayish-blue);
    background-color: var(--color-off-white);
  }

  &__footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 480px) {
      flex-direction: column;
      gap: var(--spacing-sm);
      align-items: flex-start;
    }
  }

  &__footer-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  &__refresh-btn {
    background: none;
    border: none;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    cursor: pointer;
    transition: color 0.15s ease-out;

    &:hover {
      color: var(--color-dark-blue);
    }
  }
}

// Pulse animation for loading skeleton
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
