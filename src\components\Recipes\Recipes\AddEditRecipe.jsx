'use client';

import React, { useContext, useEffect, useReducer, useState } from 'react';
import { Box } from '@mui/material';
import { useRouter, usePathname } from 'next/navigation';
import TopBackPageButton from '@/components/TopBackPage/TopBackPage';
import FormProgressIndicator from './components/FormProgressIndicator/FormProgressIndicator';
import BasicInfoSection from './components/BasicInfoSection/BasicInfoSection';
import MediaUploadSection from './components/MediaUploadSection/MediaUploadSection';
import IngredientsSection from './components/IngredientsSection/IngredientsSection';
import InstructionsSection from './components/InstructionsSection/InstructionsSection';
import NutritionSection from './components/NutritionSection/NutritionSection';
import ServingDetailsSection from './components/ServingDetailsSection/ServingDetailsSection';
import SectionNavigationSidebar from './components/SectionNavigationSidebar/SectionNavigationSidebar';
import MainFormContent from './components/MainFormContent/MainFormContent';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import AuthContext from '@/helper/authcontext';
import {
  getCurrencySymbol,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import {
  createRecipe,
  updateRecipe,
  getRecipePreviewData,
} from '@/services/recipeService';
import PreLoader from '@/components/UI/Loader';
import './recipes.scss';

// Recipe form reducer
const recipeFormReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FORM_DATA':
      return { ...state, ...action.payload };
    case 'UPDATE_BASIC_INFO':
      return { ...state, basicInfo: { ...state.basicInfo, ...action.payload } };
    case 'UPDATE_MEDIA':
      return { ...state, media: { ...state.media, ...action.payload } };
    case 'UPDATE_INGREDIENTS':
      return { ...state, ingredients: action.payload };
    case 'UPDATE_INSTRUCTIONS':
      return { ...state, instructions: action.payload };
    case 'UPDATE_NUTRITION':
      return { ...state, nutrition: { ...state.nutrition, ...action.payload } };
    case 'UPDATE_SERVING':
      return { ...state, serving: { ...state.serving, ...action.payload } };
    case 'SET_ACTIVE_SECTION':
      return { ...state, activeSection: action.payload };
    case 'CALCULATE_TOTALS':
      const totalCost = state.ingredients.reduce(
        (sum, ing) => sum + (ing.finalCost || 0),
        0
      );
      const totalTime =
        (state.basicInfo.prepTime || 0) + (state.basicInfo.cookTime || 0);
      const portionCost =
        state.serving.totalPortions > 0
          ? totalCost / state.serving.totalPortions
          : 0;
      return {
        ...state,
        calculations: {
          totalCost,
          totalTime,
          portionCost,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  activeSection: 'basic-info',
  basicInfo: {
    recipeName: '',
    publicDisplayName: '',
    recipeDescription: '',
    categories: [],
    dietaries: [],
    prepTime: 0,
    cookTime: 0,
    visibility: ['private'],
    complexity_level: 'low',
  },
  media: {
    mainImage: null,
    additionalImages: [],
    documents: [],
    audio: [],
    links: [],
  },
  isCookingMethod: false,
  isPreparationMethod: false,
  ingredients: [],
  instructions: [],
  nutrition: {
    nutrition: [],
    commonAllergens: [],
    mayContainAllergens: [],
  },
  serving: {
    yield: { value: 0, unit: 'servings' },
    totalPortions: 0,
    singlePortionSize: 0,
    servingMethod: '',
    serveIn: '',
    garnish: '',
    fohTips: '',
    chefTips: '',
  },
  calculations: {
    totalCost: 0,
    totalTime: 0,
    portionCost: 0,
  },
  recipeStatus: '',
  lastUpdated: '',
  recipeId: null,
};

export default function AddEditRecipe({ isUpdate, slug }) {
  const router = useRouter();
  const pathname = usePathname();
  const { authState } = useContext(AuthContext);
  const [formState, dispatch] = useReducer(recipeFormReducer, initialState);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const currency = getCurrencySymbol(authState?.currency_details);

  const handleRedirect = () => {
    router.back();
  };

  // Load recipe data for update mode
  useEffect(() => {
    const loadRecipeData = async () => {
      if (isUpdate && slug) {
        try {
          setIsLoading(true);
          const recipeData = await getRecipePreviewData(slug, pathname);

          const transformedData = {
            activeSection: 'basic-info',
            basicInfo: {
              recipeName: recipeData?.recipe_title || '',
              publicDisplayName: recipeData?.recipe_public_title || '',
              recipeDescription: recipeData?.recipe_description || '',
              categories: recipeData?.categories?.map((cat) => cat?.id) || [],
              dietaries:
                recipeData?.dietary_attributes?.map((diet) => diet?.id) || [],
              prepTime: recipeData?.recipe_preparation_time || 0,
              cookTime: recipeData?.recipe_cook_time || 0,
              complexity_level: recipeData?.recipe_complexity_level || 'low',
              visibility: [
                ...(recipeData?.has_recipe_public_visibility ? ['public'] : []),
                ...(recipeData?.has_recipe_private_visibility
                  ? ['private']
                  : []),
              ],
            },
            media: {
              mainImage:
                {
                  url: recipeData?.item_detail?.item_link,
                  id: recipeData?.item_detail?.item_id,
                  name: recipeData?.recipe_title,
                } || null,
              additionalImages: [
                ...(recipeData?.recipeFiles || []),
                ...(recipeData?.resources
                  ?.filter(
                    (res) =>
                      res?.type === 'item' &&
                      (res?.item_detail?.item_type?.startsWith('image/') ||
                        res?.item_detail?.item_type?.startsWith('video/'))
                  )
                  ?.map((item) => ({
                    url: item?.item_detail?.item_link,
                    id: item?.item_detail?.item_id,
                    type: item?.item_detail?.item_type,
                    name: item?.item_detail?.item_link?.split('/').pop() || '',
                  })) || []),
              ],
              documents:
                recipeData?.resources
                  ?.filter(
                    (res) =>
                      res?.type === 'item' &&
                      (res?.item_detail?.item_type?.startsWith(
                        'application/'
                      ) ||
                        res?.item_detail?.item_type?.includes('document'))
                  )
                  ?.map((doc) => ({
                    url: doc?.item_detail?.item_link,
                    id: doc?.item_detail?.item_id,
                    type: doc?.item_detail?.item_type,
                    name: doc?.item_detail?.item_link?.split('/').pop() || '',
                  })) || [],
              audio:
                recipeData?.resources
                  ?.filter(
                    (res) =>
                      res?.type === 'item' &&
                      res?.item_detail?.item_type?.startsWith('audio/')
                  )
                  ?.map((audio) => ({
                    url: audio?.item_detail?.item_link,
                    id: audio?.item_detail?.item_id,
                    type: audio?.item_detail?.item_type,
                    name: audio?.item_detail?.item_link?.split('/').pop() || '',
                  })) || [],
              links:
                recipeData?.resources
                  ?.filter((res) => res?.type === 'link')
                  ?.map((link) => ({
                    ...link,
                    item_link_type: link?.item_detail?.item_type,
                    url: link?.item_detail?.item_link,
                    id: link?.item_id,
                  })) || [],
            },

            ingredients:
              recipeData?.ingredients?.map((ing) => {
                const wastageMultiplier =
                  1 + (ing?.waste_percentage || 0) / 100;
                const baseCost = ing?.ingredient_cost || 0;
                const finalCost =
                  (ing?.ingredient_quantity || 0) *
                  baseCost *
                  wastageMultiplier;

                return {
                  ...ing,
                  quantity: ing?.ingredient_quantity || 0,
                  cost: ing?.ingredient_cost || 0,
                  measure_of_cost: {
                    id: ing?.measure_id,
                    unit_title: ing?.measure_title,
                  },
                  preparationMethod: ing?.preparation_method || null,
                  cookingMethod: ing?.ingredient_cooking_method || null,
                  wastagePercentage: ing?.waste_percentage || 0,
                  finalCost,
                };
              }) || [],
            instructions:
              recipeData?.steps?.map((step) => {
                return {
                  ...step,
                  stepNumber: step?.recipe_step_order || 0,
                  description: step?.recipe_step_description || '',
                  image: { url: step?.item_detail?.item_link || null },
                };
              }) || [],
            nutrition: {
              nutrition: recipeData?.nutrition_attributes || [],
              commonAllergens:
                recipeData?.allergen_attributes?.contains?.map(
                  (allergen) => allergen?.id
                ) || [],
              mayContainAllergens:
                recipeData?.allergen_attributes?.may_contain?.map(
                  (allergen) => allergen?.id
                ) || [],
            },
            serving: {
              yield: {
                value: recipeData?.recipe_yield || 0,
                unit: recipeData?.recipe_yield_unit || '',
              },
              totalPortions: recipeData?.recipe_total_portions || 0,
              singlePortionSize: recipeData?.recipe_single_portion_size || 0,
              servingMethod: recipeData?.recipe_serving_method || '',
              serveIn: recipeData?.recipe_serve_in || '',
              garnish: recipeData?.recipe_garnish || '',
              fohTips: recipeData?.recipe_foh_tips || '',
              chefTips: recipeData?.recipe_head_chef_tips || '',
            },
            isCookingMethod: recipeData?.is_cooking_method || false,
            isPreparationMethod: recipeData?.is_preparation_method || false,
            // calculations: {
            //   totalCost:
            //     recipeData?.ingredients?.reduce((sum, ing) => {
            //       const wastageMultiplier =
            //         1 + (ing?.waste_percentage || 0) / 100;
            //       const baseCost = ing?.ingredient_cost || 0;
            //       const finalCost =
            //         (ing?.ingredient_quantity || 0) *
            //         baseCost *
            //         wastageMultiplier;
            //       return sum + finalCost;
            //     }, 0) || 0,
            //   totalTime:
            //     (recipeData?.recipe_preparation_time || 0) +
            //     (recipeData?.recipe_cook_time || 0),
            //   portionCost:
            //     recipeData?.recipe_total_portions > 0
            //       ? (recipeData?.ingredients?.reduce((sum, ing) => {
            //           const wastageMultiplier =
            //             1 + (ing?.waste_percentage || 0) / 100;
            //           const baseCost = ing?.ingredient_cost || 0;
            //           const finalCost =
            //             (ing?.ingredient_quantity || 0) *
            //             baseCost *
            //             wastageMultiplier;
            //           return sum + finalCost;
            //         }, 0) || 0) / recipeData?.recipe_total_portions
            //       : 0,
            // },
            recipeStatus: recipeData?.recipe_status || '',
            lastUpdated: recipeData?.updated_at || '',
            recipeId: recipeData?.id || null,
          };
          // console.log('recipeData0', recipeData, transformedData);

          // Set the transformed data to form state
          dispatch({ type: 'SET_FORM_DATA', payload: transformedData });
        } catch (error) {
          setApiMessage(
            'error',
            error?.message || 'Failed to load recipe data'
          );
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadRecipeData();
  }, [isUpdate, slug, pathname]);

  // Check for mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  // Auto-calculate totals when dependencies change
  useEffect(() => {
    dispatch({ type: 'CALCULATE_TOTALS' });
  }, [
    formState.ingredients,
    formState.basicInfo.prepTime,
    formState.basicInfo.cookTime,
    formState.serving.totalPortions,
  ]);

  const formSections = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      icon: 'FileText',
      component: BasicInfoSection,
      required: true,
    },
    {
      id: 'media',
      title: 'Media & Photos',
      icon: 'Camera',
      component: MediaUploadSection,
      required: true,
    },
    {
      id: 'ingredients',
      title: 'Ingredients',
      icon: 'ShoppingCart',
      component: IngredientsSection,
      required: true,
    },
    {
      id: 'instructions',
      title: 'Instructions',
      icon: 'List',
      component: InstructionsSection,
      required: false,
    },
    {
      id: 'nutrition',
      title: 'Nutrition & Allergens',
      icon: 'Heart',
      component: NutritionSection,
      required: false,
    },
    {
      id: 'serving',
      title: 'Serving Details',
      icon: 'Users',
      component: ServingDetailsSection,
      required: false,
    },
  ];

  const handleSectionChange = (sectionId) => {
    dispatch({ type: 'SET_ACTIVE_SECTION', payload: sectionId });
  };

  const validateForm = () => {
    const errors = {};

    // Basic Info validation
    if (!formState.basicInfo.recipeName?.trim()) {
      errors['basic-info'] = true;
      errors.recipeName = 'Recipe name is required';
    }

    // Media validation
    if (!formState.media.mainImage) {
      errors['media'] = true;
      errors.mainImage = 'Main image is required';
    }

    // Ingredients validation
    if (formState.ingredients?.length === 0) {
      errors.ingredients = 'At least one ingredient is required';
    }

    // Instructions validation
    // if (formState.instructions?.length === 0) {
    //   errors.instructions = 'At least one instruction step is required';
    // }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePreview = () => {
    if (validateForm()) {
      // Navigate to preview with form data
      // router.push({
      //   pathname: '/recipes/recipe-preview',
      //   query: { previewDetails: JSON.stringify(formState) },
      // });
      router.push('/recipes/recipe-preview');
    }
  };

  // Function to prepare recipe data for API
  const prepareRecipeData = () => {
    const allMediaFiles = [
      ...formState.media.additionalImages,
      ...formState.media.documents,
      ...formState.media.audio,
    ];

    const mediaLinks = formState?.media?.links?.map((link) => {
      return {
        type: link?.type,
        item_link: link?.url,
        item_link_type: link.item_link_type,
      };
    });

    const ingredientsList = formState?.ingredients
      ?.map((ing) => {
        return {
          id: ing?.id,
          quantity: ing?.quantity,
          measure: ing?.measure_of_cost?.id,
          wastage: ing?.wastagePercentage,
          cost: ing?.cost,
          cooking_method: formState?.isCookingMethod
            ? ing?.cookingMethod
            : null,
          preparation_method: formState?.isPreparationMethod
            ? ing?.preparationMethod
            : null,
        };
      })
      ?.filter((ing) => ing?.id !== 'nutrition-container');

    const instructionsList = formState?.instructions?.map((step) => {
      return {
        order: step?.stepNumber,
        description: step?.description,
        item_id: step?.item_id,
      };
    });

    const instrucationImages = formState?.instructions?.map((step) => {
      return step?.image?.file;
    });
    const nutritionList = formState?.nutrition?.nutrition?.map((nut) => {
      return {
        id: nut?.id,
        unit: nut?.value,
        unit_of_measure: nut?.unit_of_measure,
        description: nut?.description || '',
      };
    });

    // Process recipe files - ensure we're getting the actual files
    const recipeFiles = allMediaFiles
      ?.filter((file) => file?.file) // Only include files that have the file property
      ?.map((file) => file?.file); // Extract the actual file object

    const availableMedia = allMediaFiles
      ?.filter((file) => !file?.file)
      ?.map((item) => ({
        item_id: item?.id,
        type: 'item',
      }));

    return {
      recipe_title: formState.basicInfo.recipeName,
      recipe_public_title: formState.basicInfo.publicDisplayName,
      recipe_description: formState.basicInfo.recipeDescription,
      categories:
        formState.basicInfo.categories?.map((cat) => cat.id || cat) || [],
      dietary_attributes:
        formState.basicInfo.dietaries?.map((diet) => diet.id || diet) || [],
      recipe_preparation_time: formState.basicInfo.prepTime,
      recipe_cook_time: formState.basicInfo.cookTime,
      has_recipe_public_visibility:
        formState.basicInfo.visibility.includes('public'),
      has_recipe_private_visibility:
        formState.basicInfo.visibility.includes('private'),
      recipePlaceholder: formState?.media?.mainImage?.file || null,
      recipeFiles: recipeFiles || [], // Use the processed recipeFiles
      resources: [...mediaLinks, ...availableMedia] || [],
      recipe_yield: formState.serving.yield.value,
      recipe_yield_unit: formState.serving.yield.unit,
      recipe_total_portions: formState.serving.totalPortions,
      recipe_single_portion_size: formState.serving.singlePortionSize,
      recipe_serving_method: formState.serving.servingMethod,
      recipe_serve_in: formState.serving.serveIn,
      recipe_garnish: formState.serving.garnish,
      recipe_foh_tips: formState.serving.fohTips,
      recipe_head_chef_tips: formState.serving.chefTips,
      ingredients: ingredientsList || [],
      instructions: formState.instructions || [],
      is_cooking_method: formState.isCookingMethod || false,
      is_preparation_method: formState.isPreparationMethod || false,
      recipe_complexity_level: formState.basicInfo.complexity_level || 'low',
      steps: instructionsList || [],
      stepImages: instrucationImages || [],
      nutrition_attributes: nutritionList || [],
      allergen_attributes: {
        contains: formState?.nutrition?.commonAllergens || [],
        may_contain: formState?.nutrition?.mayContainAllergens || [],
      },
    };
  };

  const handleOpenSaveAndExitModal = async () => {
    if (validateForm()) {
      setShowExitModal(true);
    }
  };

  const handleCloseSaveAndExitModal = () => {
    setShowExitModal(false);
  };
  // Helper functions for FormData preparation
  const appendArrayToFormData = (formData, key, array) => {
    if (array && Array.isArray(array)) {
      array.forEach((item, index) => {
        formData.append(`${key}[${index}]`, item);
      });
    }
  };

  const appendFileToFormData = (formData, key, fileData) => {
    // Handle your file object structure: { id, name, size, type, url, uploadedAt, file: File }
    if (fileData instanceof File) {
      // Direct File object
      formData.append(key, fileData);
    } else if (typeof fileData === 'string') {
      // String URL
      formData.append(key, fileData);
    } else if (
      fileData &&
      typeof fileData === 'object' &&
      fileData.file instanceof File
    ) {
      // Your file object structure - extract the actual File
      formData.append(key, fileData.file, fileData.name);
    }
  };

  // Refactored FormData preparation
  const prepareFormData = (responseData) => {
    const formData = new FormData();

    // Helper function to check if a value is empty
    const isEmpty = (value) => {
      if (value === null || value === undefined) return true;
      if (typeof value === 'string' && value.trim() === '') return true;
      if (Array.isArray(value) && value.length === 0) return true;
      if (typeof value === 'object' && Object.keys(value).length === 0)
        return true;
      return false;
    };

    // Basic fields
    const basicFields = {
      recipe_title: responseData?.recipe_title,
      recipe_public_title: responseData?.recipe_public_title,
      recipe_description: responseData?.recipe_description,
      recipe_complexity_level: responseData?.recipe_complexity_level,
      recipe_preparation_time: responseData?.recipe_preparation_time,
      recipe_cook_time: responseData?.recipe_cook_time,
      has_recipe_public_visibility: responseData?.has_recipe_public_visibility,
      has_recipe_private_visibility:
        responseData?.has_recipe_private_visibility,
      recipe_yield: responseData?.recipe_yield,
      recipe_yield_unit: responseData?.recipe_yield_unit,
      recipe_total_portions: responseData?.recipe_total_portions,
      recipe_single_portion_size: responseData?.recipe_single_portion_size,
      recipe_serving_method: responseData?.recipe_serving_method,
      recipe_serve_in: responseData?.recipe_serve_in,
      recipe_garnish: responseData?.recipe_garnish,
      recipe_foh_tips: responseData?.recipe_foh_tips,
      recipe_head_chef_tips: responseData?.recipe_head_chef_tips,
      is_cooking_method: responseData?.is_cooking_method,
      is_preparation_method: responseData?.is_preparation_method,
    };

    // Append basic fields only if they are not empty
    Object.entries(basicFields).forEach(([key, value]) => {
      if (!isEmpty(value)) {
        formData.append(key, value);
      }
    });

    // Arrays - only append if not empty
    if (!isEmpty(responseData?.categories)) {
      appendArrayToFormData(formData, 'categories', responseData.categories);
    }
    if (!isEmpty(responseData?.dietary_attributes)) {
      appendArrayToFormData(
        formData,
        'dietary_attributes',
        responseData?.dietary_attributes
      );
    }

    // Files - only append if not empty
    if (!isEmpty(responseData?.recipePlaceholder)) {
      appendFileToFormData(
        formData,
        'recipePlaceholder',
        responseData?.recipePlaceholder
      );
    }

    // Handle recipeFiles - ensure each file is properly appended
    if (!isEmpty(responseData?.recipeFiles)) {
      responseData.recipeFiles.forEach((file) => {
        if (file instanceof File) {
          formData.append(`recipeFiles`, file);
        }
      });
    }

    // Handle stepImages
    if (!isEmpty(responseData?.stepImages)) {
      responseData.stepImages.forEach((file) => {
        if (file instanceof File) {
          formData.append(`stepImages`, file);
        }
      });
    }

    // JSON data - only append if not empty
    const jsonFields = {
      ingredients: responseData?.ingredients,
      // instructions: responseData?.instructions,
      steps: responseData?.steps,
      nutrition_attributes: responseData?.nutrition_attributes,
      allergen_attributes: responseData?.allergen_attributes,
      resources: responseData?.resources,
    };

    Object.entries(jsonFields).forEach(([key, value]) => {
      if (!isEmpty(value)) {
        formData.append(key, JSON.stringify(value));
      }
    });

    return formData;
  };

  const handleSaveAndExit = async (recipeStatus) => {
    try {
      setIsSaving(true);
      const recipeData = prepareRecipeData();
      const formData = prepareFormData(recipeData); // Convert to FormData
      formData.append('recipe_status', recipeStatus);

      let response;
      if (isUpdate) {
        // Update existing recipe
        response = await updateRecipe(formState.recipeId, formData);
      } else {
        // Create new recipe with FormData
        response = await createRecipe(formData);
      }
      if (response) {
        setShowExitModal(false);
        setApiMessage(
          'success',
          response?.message || 'Recipe saved successfully!'
        );
        // Navigate back to recipes list
        router.push('/recipes');
      }
    } catch (error) {
      setShowExitModal(false);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to save recipe'
      );
    } finally {
      setShowExitModal(false);
      setIsSaving(false);
    }
  };

  const currentSection = formSections.find(
    (section) => section.id === formState.activeSection
  );
  const CurrentSectionComponent = currentSection?.component;

  return (
    <>
      {(isSaving || isLoading) && <PreLoader />}
      <TopBackPageButton
        title={`${isUpdate ? 'Update' : 'Create'} Recipe`}
        onBackClick={handleRedirect}
      />
      {/* Form Progress Indicator */}
      <Box className="section-right-content add-edit-recipe-container">
        <FormProgressIndicator
          formState={formState}
          validationErrors={validationErrors}
        />

        {/* Main Content */}
        <div className="add-edit-recipe__layout">
          {/* Desktop: Section Navigation Sidebar */}
          <SectionNavigationSidebar
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            validationErrors={validationErrors}
            calculations={formState.calculations}
            currency={currency}
            isMobileView={isMobileView}
          />

          {/* Main Form Content */}
          <MainFormContent
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            currentSection={currentSection}
            CurrentSectionComponent={CurrentSectionComponent}
            formState={formState}
            dispatch={dispatch}
            validationErrors={validationErrors}
            isMobileView={isMobileView}
            currency={currency}
            isUpdate={isUpdate}
            onPreview={handlePreview}
            onSaveAndExit={handleOpenSaveAndExitModal}
          />
        </div>
      </Box>
      <DialogBox
        open={showExitModal}
        handleClose={handleCloseSaveAndExitModal}
        title="Confirmation"
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <ConfirmationModal
            handleCancel={() => handleSaveAndExit('draft')}
            handleConfirm={() => handleSaveAndExit('publish')}
            text="Do you want to save your recipe before exiting? Your progress will be preserved."
            cancelText="Save & Exit"
            confirmText="Published & Exit"
            isLoading={isSaving}
          />
        }
      />
    </>
  );
}
