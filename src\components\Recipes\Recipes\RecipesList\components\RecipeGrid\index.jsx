import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import RecipeCard from '../RecipeCard';
import { useRouter } from 'next/navigation';
import './recipegrid.scss';

const RecipeGrid = ({
  recipes,
  isLoading,
  viewMode,
  isPublicPage = false, // New prop to determine if this is a public page
  onBookmarkToggle,
  onDuplicate,
  onDelete,
  onShare,
  onExport,
  onEdit,
  onView,
  onViewHistory,
}) => {
  const router = useRouter();
  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="recipe-grid__skeleton">
      {viewMode === 'grid' ? (
        <div className="recipe-grid__skeleton-card">
          <div className="recipe-grid__skeleton-image"></div>
          <div className="recipe-grid__skeleton-content">
            <div className="recipe-grid__skeleton-title"></div>
            <div className="recipe-grid__skeleton-description"></div>
            <div className="recipe-grid__skeleton-meta">
              <div className="recipe-grid__skeleton-meta-item"></div>
              <div className="recipe-grid__skeleton-meta-item"></div>
            </div>
          </div>
        </div>
      ) : (
        <div className="recipe-grid__skeleton-list">
          <div className="recipe-grid__skeleton-list-image"></div>
          <div className="recipe-grid__skeleton-list-content">
            <div className="recipe-grid__skeleton-title"></div>
            <div className="recipe-grid__skeleton-description"></div>
          </div>
          <div className="recipe-grid__skeleton-actions"></div>
        </div>
      )}
    </div>
  );

  if (isLoading) {
    return (
      <div
        className={`recipe-grid ${viewMode === 'grid' ? 'recipe-grid--grid' : 'recipe-grid--list'}`}
      >
        {Array.from({ length: 8 }).map((_, index) => (
          <LoadingSkeleton key={index} />
        ))}
      </div>
    );
  }

  if (recipes?.length === 0) {
    return (
      <div className="recipe-grid__empty">
        <div className="recipe-grid__empty-icon">
          <Icon name="ChefHat" size={48} />
        </div>
        <h3 className="recipe-grid__empty-title">No recipes found</h3>
        <p className="recipe-grid__empty-description">
          Try adjusting your filters or search terms, or create your first
          recipe.
        </p>
        <CustomButton
          variant="contained"
          startIcon={<Icon name="Plus" size={18} />}
          onClick={() => router.push('/recipes/recipe-create')}
          title="Create Recipe"
          className="recipe-grid__empty-button"
        />
      </div>
    );
  }

  return (
    <div
      className={`recipe-grid ${viewMode === 'grid' ? 'recipe-grid--grid' : 'recipe-grid--list'}`}
    >
      {recipes?.map((recipe) => (
        <RecipeCard
          key={recipe?.id}
          recipe={recipe}
          viewMode={viewMode}
          isSelected={false}
          isPublicPage={isPublicPage} // Pass the public page prop
          onBookmarkToggle={onBookmarkToggle}
          onDuplicate={onDuplicate}
          onDelete={onDelete}
          onShare={onShare}
          onExport={onExport}
          onEdit={onEdit}
          onView={onView}
          onViewHistory={onViewHistory}
        />
      ))}
    </div>
  );
};

export default RecipeGrid;
