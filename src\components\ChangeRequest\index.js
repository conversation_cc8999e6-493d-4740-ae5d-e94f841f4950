'use client';

import React, { useState, useContext, useEffect } from 'react';
import AuthContext from '@/helper/authcontext';
import { Box } from '@mui/material';
import ChangeRequestStaffPage from '@/components/ChangeRequest/ChangeReqStaff';
import { fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import CustomTabs from '@/components/UI/CustomTabs';
import './requestchanges.scss';

const RequestChangesPage = () => {
  const { authState } = useContext(AuthContext);
  const { userdata } = useContext(AuthContext);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [rowsPerPageOwn, setRowsPerPageOwn] = useState(10);

  const [activeTab, setActiveTab] = useState(0);
  const dsr_tabs = [
    { id: 0, name: 'Change requests' },
    { id: 1, name: 'Own change requests' },
  ];

  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      fetchFromStorage(identifiers?.RedirectData)?.changeReq
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      fdata?.tabValue && setActiveTab(fdata?.tabValue - 1);
    } else if (userdata && userdata?.IsFromUser && userdata?.changeReq) {
      const fdata = userdata;
      fdata?.tabValue && setActiveTab(fdata?.tabValue - 1);
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);

  const getCurrentTabs = () => {
    return dsr_tabs?.map((tab) => ({
      id: tab?.id,
      label: tab?.name,
    }));
  };

  const getCurrentContent = () => {
    switch (activeTab) {
      case 0:
        return (
          <ChangeRequestStaffPage
            tab="staff"
            tabValue={1}
            setRowsPerPage={setRowsPerPage}
            rowsPerPage={rowsPerPage}
          />
        );
      case 1:
        return (
          <ChangeRequestStaffPage
            tab="own"
            tabValue={2}
            setRowsPerPage={setRowsPerPageOwn}
            rowsPerPage={rowsPerPageOwn}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      {authState?.web_user_active_role_id === 1 ? (
        <>
          <ChangeRequestStaffPage
            tab="staff"
            isSuperAdmin={true}
            setRowsPerPage={setRowsPerPage}
            rowsPerPage={rowsPerPage}
          />
        </>
      ) : authState?.UserPermission?.change_request === 2 ? (
        <>
          <Box className="change-request-tab-container">
            <Box className="section-right-content">
              <Box className="section-right-tab-header">
                <CustomTabs
                  tabs={getCurrentTabs()}
                  initialTab={activeTab}
                  onTabChange={handleTabChange}
                />
              </Box>
              {getCurrentContent()}
            </Box>
          </Box>
        </>
      ) : (
        <>
          <ChangeRequestStaffPage
            isOwn={true}
            setRowsPerPage={setRowsPerPage}
            rowsPerPage={rowsPerPage}
          />
        </>
      )}
    </>
  );
};

export default RequestChangesPage;
