import React from 'react';
import './TrafficLightNutrition.scss';

export function NutritionCard({ recipeData }) {
  // Default nutrition data if not provided
  const foodNutritionPer100g = recipeData?.nutrition || {
    calories: 250,
    protein: 12,
    carbs: 20,
    fat: 15,
    saturates: 6,
    fiber: 3,
    sugar: 5,
    sodium: 80,
    cholesterol: 55,
  };

  const nutritionFields = [
    {
      key: 'calories',
      label: 'Calories',
      unit: 'kcal',
      icon: 'Zap',
      color: 'nutrition-section__icon--orange',
    },
    {
      key: 'protein',
      label: 'Protein',
      unit: 'g',
      icon: 'Dumbbell',
      color: 'nutrition-section__icon--red',
    },
    {
      key: 'carbs',
      label: 'Carbs',
      unit: 'g',
      icon: 'Wheat',
      color: 'nutrition-section__icon--yellow',
    },
    {
      key: 'fat',
      label: 'Fat',
      unit: 'g',
      icon: 'Droplets',
      color: 'nutrition-section__icon--blue',
    },
    {
      key: 'saturates',
      label: 'Saturates',
      unit: 'g',
      icon: 'DropletHalf',
      color: 'nutrition-section__icon--lightblue',
    },
    {
      key: 'fiber',
      label: 'Fiber',
      unit: 'g',
      icon: 'Leaf',
      color: 'nutrition-section__icon--green',
    },
    {
      key: 'sugar',
      label: 'Sugar',
      unit: 'g',
      icon: 'Candy',
      color: 'nutrition-section__icon--pink',
    },
    {
      key: 'sodium',
      label: 'Sodium',
      unit: 'mg',
      icon: 'Salt',
      color: 'nutrition-section__icon--gray',
    },
    {
      key: 'cholesterol',
      label: 'Cholesterol',
      unit: 'mg',
      icon: 'Heart',
      color: 'nutrition-section__icon--purple',
    },
  ];

  return (
    <div className="nutrition-card">
      <TrafficLightNutrition
        nutritionDataPer100g={foodNutritionPer100g}
        nutritionFields={nutritionFields}
        servingSize={recipeData?.singlePortionSize?.value || 100}
        isPerServing={true}
      />
    </div>
  );
}

// Reference Intakes (RI) and Thresholds
const REFERENCE_INTAKES = {
  calories: 2000,
  protein: 50,
  carbs: 260,
  fat: 70,
  saturates: 20,
  fiber: 30,
  sugar: 90,
  sodium: 2400,
  cholesterol: 300,
};

// Thresholds for traffic light colors (per 100g/ml)
const THRESHOLDS_PER_100G = {
  fat: { low: 3, medium: 17.5 },
  saturates: { low: 1.5, medium: 5 },
  sugar: { low: 5, medium: 22.5 },
  salt: { low: 0.3, medium: 1.5 },
};

// Helper Functions
const calculateRIPercentage = (value, nutrientKey) => {
  if (
    value === null ||
    value === undefined ||
    !REFERENCE_INTAKES[nutrientKey]
  ) {
    return null;
  }
  const percentage = (value / REFERENCE_INTAKES[nutrientKey]) * 100;
  return Math.round(percentage);
};

const getTrafficLightColorClass = (value, nutrientKey, per100gData) => {
  if (value === null || value === undefined) return 'grey';

  let valueForColoring = per100gData[nutrientKey];
  let thresholdsKey = nutrientKey;

  // Special handling for sodium to use salt thresholds
  if (nutrientKey === 'sodium') {
    thresholdsKey = 'salt';
    valueForColoring =
      per100gData.sodium !== undefined
        ? (per100gData.sodium / 1000) * 2.5
        : null;
  }

  // Special handling for calories
  if (nutrientKey === 'calories') {
    const ri = calculateRIPercentage(value, 'calories');
    if (ri === null) return 'grey';
    if (ri <= 15) return 'green';
    if (ri <= 40) return 'amber';
    return 'red';
  }

  const thresholds = THRESHOLDS_PER_100G[thresholdsKey];

  if (!thresholds) {
    return 'grey';
  }
  if (valueForColoring === null || valueForColoring === undefined)
    return 'grey';

  if (valueForColoring <= thresholds.low) return 'green';
  if (valueForColoring <= thresholds.medium) return 'amber';
  return 'red';
};

const TrafficLightNutritionItem = ({ field, value, riPercent, colorClass }) => (
  <div className={`nutrition-item nutrition-item--${colorClass}`}>
    <span className="nutrition-item__label">{field.label}</span>
    <span className="nutrition-item__value">
      {value}
      {field.unit}
    </span>
    {riPercent !== null && (
      <span className="nutrition-item__ri">{riPercent}%</span>
    )}
  </div>
);

const TrafficLightNutrition = ({
  nutritionDataPer100g,
  nutritionFields,
  servingSize = 200,
  isPerServing = false,
}) => {
  const factor = isPerServing ? servingSize / 100 : 1;

  const formatValue = (val, decimals = 1) => {
    if (val === null || val === undefined) return 'N/A';
    if (val < 1 && val > 0 && (decimals === 1 || decimals === 0)) {
      return parseFloat(val.toFixed(2));
    }
    return parseFloat(val.toFixed(decimals));
  };

  return (
    <div className="traffic-light-nutrition">
      <div className="nutrition-header">
        {isPerServing
          ? `Nutrition Per ${formatValue(servingSize, 0)}g Serving`
          : 'Nutrition Values Per 100g/ml'}
      </div>
      <div className="nutrition-items-container">
        {nutritionFields.map((field) => {
          const valuePer100g = nutritionDataPer100g[field.key];
          const displayValue =
            valuePer100g !== undefined ? valuePer100g * factor : null;
          const riPercent = calculateRIPercentage(displayValue, field.key);

          let colorClass;
          if (
            ['fat', 'saturates', 'sugar', 'sodium', 'calories'].includes(
              field.key
            )
          ) {
            colorClass = getTrafficLightColorClass(
              displayValue,
              field.key,
              nutritionDataPer100g
            );
          } else {
            colorClass = 'grey';
          }

          return (
            <TrafficLightNutritionItem
              key={field.key}
              field={field}
              value={formatValue(
                displayValue,
                field.unit === 'mg' || field.key === 'salt'
                  ? 1
                  : displayValue < 10
                    ? 1
                    : 0
              )}
              riPercent={riPercent}
              colorClass={colorClass}
            />
          );
        })}
      </div>
    </div>
  );
};

export default NutritionCard;
