'use client';

import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './quickactionpanel.scss';
import Link from 'next/link';

const QuickActionPanel = ({ actions = [] }) => {
  const getActionColorClass = (color) => {
    return `quick-action-panel__action--${color}`;
  };

  return (
    <div className="quick-action-panel">
      {/* Panel Header */}
      <div className="quick-action-panel__header">
        <div className="quick-action-panel__header-content">
          <div className="quick-action-panel__header-info">
            <h3 className="quick-action-panel__title">Quick Actions</h3>
            <p className="quick-action-panel__subtitle">
              Common administrative tasks requiring attention
            </p>
          </div>
          <Icon
            name="Zap"
            size={20}
            className="quick-action-panel__header-icon"
          />
        </div>
      </div>

      {/* Actions Grid */}
      <div className="quick-action-panel__content">
        <div className="quick-action-panel__grid">
          {actions.map((action) => (
            <Link
              key={action.id}
              href={action.action || '#'}
              className={`quick-action-panel__action ${getActionColorClass(action.color)}`}
            >
              <div className="quick-action-panel__action-content">
                <div className="quick-action-panel__action-icon">
                  <Icon name={action.icon} size={20} />
                </div>

                <div className="quick-action-panel__action-info">
                  <h4 className="quick-action-panel__action-title">
                    {action.title}
                  </h4>

                  <div className="quick-action-panel__action-footer">
                    <span className="quick-action-panel__action-count">
                      {action.count} pending
                    </span>
                    <Icon
                      name="ArrowRight"
                      size={16}
                      className="quick-action-panel__action-arrow"
                    />
                  </div>
                </div>
              </div>

              {/* Count Badge */}
              {action.count > 0 && (
                <div className="quick-action-panel__badge">
                  {action.count > 99 ? '99+' : action.count}
                </div>
              )}
            </Link>
          ))}
        </div>
      </div>

      {/* Panel Footer */}
      <div className="quick-action-panel__footer">
        <div className="quick-action-panel__footer-content">
          <span className="quick-action-panel__footer-text">
            Total pending actions:{' '}
            {actions.reduce((sum, action) => sum + action.count, 0)}
          </span>
          <Link
            href="/admin/all-actions"
            className="quick-action-panel__footer-link"
          >
            <span>View all actions</span>
            <Icon name="ExternalLink" size={14} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default QuickActionPanel;
