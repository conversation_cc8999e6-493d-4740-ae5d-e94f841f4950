import React from 'react';
import { Link } from 'react-router-dom';
import Icon from 'components/AppIcon';

const QuickActionPanel = ({ actions }) => {
  const colorClasses = {
    primary: 'bg-primary-50 text-primary border-primary-200 hover:bg-primary-100',
    success: 'bg-success-50 text-success border-success-200 hover:bg-success-100',
    warning: 'bg-warning-50 text-warning border-warning-200 hover:bg-warning-100',
    error: 'bg-error-50 text-error border-error-200 hover:bg-error-100',
    accent: 'bg-accent-50 text-accent border-accent-200 hover:bg-accent-100'
  };

  return (
    <div className="bg-surface border border-gray-200 rounded-lg shadow-card">
      {/* Panel Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-heading font-semibold text-lg text-text-primary">
              Quick Actions
            </h3>
            <p className="text-sm text-text-secondary">
              Common administrative tasks requiring attention
            </p>
          </div>
          <Icon name="Zap" size={20} className="text-accent" />
        </div>
      </div>

      {/* Actions Grid */}
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {actions.map((action) => (
            <Link
              key={action.id}
              to={action.action}
              className={`relative p-4 border rounded-lg transition-smooth hover-lift group ${
                colorClasses[action.color]
              }`}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg bg-white shadow-sm`}>
                  <Icon name={action.icon} size={20} />
                </div>
                
                <div className="flex-1">
                  <h4 className="font-medium text-text-primary mb-1 group-hover:text-current">
                    {action.title}
                  </h4>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm opacity-80">
                      {action.count} pending
                    </span>
                    <Icon 
                      name="ArrowRight" 
                      size={16} 
                      className="opacity-0 group-hover:opacity-100 transition-smooth transform translate-x-0 group-hover:translate-x-1" 
                    />
                  </div>
                </div>
              </div>

              {/* Count Badge */}
              {action.count > 0 && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-error text-white text-xs rounded-full flex items-center justify-center font-bold">
                  {action.count > 99 ? '99+' : action.count}
                </div>
              )}
            </Link>
          ))}
        </div>
      </div>

      {/* Panel Footer */}
      <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between">
          <span className="text-sm text-text-secondary">
            Total pending actions: {actions.reduce((sum, action) => sum + action.count, 0)}
          </span>
          <Link
            to="/admin/all-actions"
            className="text-sm text-primary hover:text-primary-600 transition-smooth flex items-center space-x-1"
          >
            <span>View all actions</span>
            <Icon name="ExternalLink" size={14} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default QuickActionPanel;