'use client';
import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  IconButton,
  InputAdornment,
  Tooltip,
  // FormControlLabel,
  // Checkbox,
} from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/CustomButton';
import {
  Email,
  Phone,
  Business,
  Edit,
  Save,
  Language,
  Facebook,
  Twitter,
  LinkedIn,
  Public,
  AccessTime,
  LocationOn,
} from '@mui/icons-material';
// import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import {
  setApiMessage,
  TextFieldMaxLength,
} from '@/helper/common/commonFunctions';
import HeaderImage from '@/components/UI/ImageSecurity';
import { useRouter } from 'next/navigation';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import { identifiers } from '@/helper/constants/identifier';
import useLocationData from '@/hooks/useLocationData';
import AuthContext from '@/helper/authcontext';
import './organizationform.scss';

export default function Organization({
  OrgId,
  superAdmin,
  previewImage,
  setPreviewImage,
  setOrganizationDetails,
}) {
  const { folderdata, setfolderdata, setOrgDetails } = useContext(AuthContext);
  const [currencyData, setCurrencyData] = useState();
  const [timezoneData, setTimezoneData] = useState();
  const [OrgData, setOrgData] = useState();
  const {
    countries,
    counties,
    cities,
    setCities,
    setCounties,
    setSelectedCountry,
    setSelectedCounty,
  } = useLocationData();
  const router = useRouter();
  const formikRef = useRef(null);
  // const urlPattern = /^(ftp|http|https):\/\/[^ "]+$/;0
  const urlPattern =
    /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/;

  const getCurrency = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_CURRENCIES
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          let currencyOptions = data?.data?.map((currency) => {
            return {
              label: currency?.code,
              value: currency?.code,
            };
          });
          setCurrencyData(currencyOptions);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getTimezone = async () => {
    try {
      const { status, data } = await axiosInstance.get(ORG_URLS?.GET_TIMEZONE);
      if (status === 200 || status === 201) {
        if (data?.status) {
          let timezoneOptions = data?.data?.map((timezone) => {
            return {
              label: timezone,
              value: timezone,
            };
          });
          setTimezoneData(timezoneOptions);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getOrgbyID = async (id) => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ORGANIZATION_BY_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          setOrgData(data?.data);
          setOrganizationDetails({
            name: data?.data?.name,
            website: data?.data?.attributes?.website,
          });
          setOrgDetails(data?.data);

          if (data?.data) {
            data?.data?.attributes?.organization_logo &&
              data?.data?.attributes?.organization_logo !== '""' &&
              setPreviewImage({
                url: data?.data?.attributes?.organization_logo,
              });
            setSelectedCountry(
              data?.data?.attributes && data?.data?.attributes?.geo_country
                ? data?.data?.attributes?.geo_country
                : ''
            );
            setSelectedCounty(
              data?.data?.attributes && data?.data?.attributes?.geo_state
                ? data?.data?.attributes?.geo_state
                : ''
            );
            setfolderdata({ ...folderdata, organizationData: data?.data });
            formikRef.current.setFieldValue(
              'name',
              data?.data?.name ? data?.data?.name : ''
            );
            formikRef.current.setFieldValue(
              'website',
              data?.data?.attributes && data?.data?.attributes?.website
                ? data?.data?.attributes?.website
                : ''
            );
            formikRef.current.setFieldValue(
              'email',
              data?.data?.attributes && data?.data?.attributes?.email
                ? data?.data?.attributes?.email
                : ''
            );
            formikRef.current.setFieldValue(
              'isMultipleLocations',
              data?.data?.attributes &&
                data?.data?.attributes?.multiple_location
                ? true
                : false
            );
            formikRef.current.setFieldValue(
              'locations',
              data?.data?.attributes &&
                data?.data?.attributes?.multiple_location
                ? Number(data?.data?.attributes?.multiple_location)
                : ''
            );
            formikRef.current.setFieldValue(
              'contact_number',
              data?.data?.attributes && data?.data?.attributes?.contact_person
                ? data?.data?.attributes?.contact_person
                : ''
            );
            formikRef.current.setFieldValue(
              'addressLine1',
              data?.data?.attributes && data?.data?.attributes?.address
                ? data?.data?.attributes?.address
                : ''
            );
            formikRef.current.setFieldValue(
              'addressLine2',
              data?.data?.attributes && data?.data?.attributes?.address1
                ? data?.data?.attributes?.address1
                : ''
            );

            formikRef.current.setFieldValue(
              'postalCode',
              data?.data?.attributes && data?.data?.attributes?.pin_code
                ? data?.data?.attributes?.pin_code
                : ''
            );
            formikRef.current.setFieldValue(
              'vat_number',
              data?.data?.attributes && data?.data?.attributes?.vat_number
                ? data?.data?.attributes?.vat_number
                : ''
            );
            formikRef.current.setFieldValue(
              'currency',
              data?.data?.attributes && data?.data?.attributes?.currency
                ? data?.data?.attributes?.currency
                : 'GBP'
            );
            formikRef.current.setFieldValue(
              'country',
              data?.data?.attributes && data?.data?.attributes?.geo_country
                ? data?.data?.attributes?.geo_country
                : ''
            );
            formikRef.current.setFieldValue(
              'addressState',
              data?.data?.attributes && data?.data?.attributes?.geo_state
                ? data?.data?.attributes?.geo_state
                : ''
            );
            formikRef.current.setFieldValue(
              'addressCity',
              data?.data?.attributes && data?.data?.attributes?.geo_city
                ? data?.data?.attributes?.geo_city
                : ''
            );
            formikRef.current.setFieldValue(
              'linkedin_link',
              data?.data?.attributes && data?.data?.attributes?.linkdin_url
                ? data?.data?.attributes?.linkdin_url
                : ''
            );
            formikRef.current.setFieldValue(
              'facebook_link',
              data?.data?.attributes && data?.data?.attributes?.facebook_url
                ? data?.data?.attributes?.facebook_url
                : ''
            );
            formikRef.current.setFieldValue(
              'twitter_link',
              data?.data?.attributes && data?.data?.attributes?.twitter_url
                ? data?.data?.attributes?.twitter_url
                : ''
            );
            formikRef.current.setFieldValue(
              'timezone',
              data?.data?.attributes && data?.data?.attributes?.timezone
                ? data?.data?.attributes?.timezone
                : 'Europe/London'
            );
            formikRef.current.setFieldValue(
              'status',
              data?.data?.attributes && data?.data?.attributes?.status
                ? data?.data?.attributes?.status
                : ''
            );
            formikRef.current.setFieldValue(
              'customer_id',
              data?.data?.attributes && data?.data?.attributes?.customer_id
                ? data?.data?.attributes?.customer_id
                : ''
            );
            formikRef.current.setFieldValue(
              'support_pin',
              data?.data?.attributes && data?.data?.attributes?.support_pin
                ? data?.data?.attributes?.support_pin
                : ''
            );
          }
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getCurrency();
    // getCountry();
    getTimezone();
  }, []);
  useEffect(() => {
    OrgId && getOrgbyID(OrgId);
  }, [OrgId]);

  const handleLogoChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage({ url: reader.result, file });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Box className="profile-form-container">
      <Formik
        innerRef={formikRef}
        initialValues={{
          email: '',
          name: '',
          vat_number: '',
          country: ' ',
          contact_number: '',
          website: '',
          timezone: '',
          currency: '',
          linkedin_link: '',
          facebook_link: '',
          twitter_link: '',
          addressLine1: '',
          addressLine2: '',
          addressCity: '',
          addressState: '',
          postalCode: '',
          isMultipleLocations: false,
          status: '',
        }}
        validationSchema={Yup.object().shape({
          email: Yup.string()
            .required('This field is required')
            .email('Invalid email address'),
          name: Yup.string()
            .required('This field is required')
            .matches(
              /^[a-zA-Z0-9 ]*$/,
              'Only letters, numbers, and spaces are allowed'
            )
            .min(3, 'Organization name must be at least 3 characters'),
          // vat_number: Yup.string().required('This field is required'),
          vat_number: Yup.string()
            .required('This field is required')
            .matches(
              /^(?:[A-Z]{2}\d{9}|\d{9})$/,
              'Enter a valid VAT number (e.g., AB123456789 or 123456789)'
            ),
          status: Yup.string().required('This field is required'),
          addressLine1: Yup.string().trim().required('This field is required'),
          postalCode: Yup.string().trim().required('This field is required'),
          contact_number: Yup.string()
            .trim()
            .required('This field is required')
            .matches(/^[0-9]{10,11}$/, 'Invalid phone number'),
          website: Yup.string()
            .matches(urlPattern, 'Invalid URL')
            .required('This field is required'),
          linkedin_link: Yup.string().matches(urlPattern, 'Invalid URL'),
          facebook_link: Yup.string().matches(urlPattern, 'Invalid URL'),
          twitter_link: Yup.string().matches(urlPattern, 'Invalid URL'),
          locations: Yup.string().required('This field is required'),
          isMultipleLocations: Yup.bool().oneOf(
            [true],
            'This field is required'
          ),
          country: Yup.string().trim().required('This field is required'),
          addressState:
            counties &&
            counties?.length !== 0 &&
            Yup.string().trim().required('This field is required'),
          addressCity:
            cities &&
            cities?.length !== 0 &&
            Yup.string().trim().required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          let body = new FormData();
          body.append('id', OrgId);
          body.append('name', requestData?.name);
          body.append('description', requestData?.name);
          requestData?.isMultipleLocations &&
            body.append('multiple_location', Number(requestData?.locations));
          body.append('email', requestData?.email);
          body.append('contact_person', requestData?.contact_number);
          body.append('website', requestData?.website);
          body.append('address', requestData?.addressLine1);
          requestData?.addressLine2 &&
            body.append('address1', requestData?.addressLine2);
          body.append('vat_number', requestData?.vat_number);
          body.append('geo_country', requestData?.country);
          body.append('geo_state', requestData?.addressState);
          body.append('geo_city', requestData?.addressCity);
          body.append('pin_code', requestData?.postalCode);
          body.append('currency', requestData?.currency);
          body.append('timezone', requestData?.timezone);
          body.append('facebook_url', requestData?.facebook_link);
          body.append('linkdin_url', requestData?.linkedin_link);
          body.append('twitter_url', requestData?.twitter_link);
          body.append('status', requestData?.status);
          body.append('created_at', OrgData?.attributes?.created_at);
          body.append('customer_id', OrgData?.attributes?.customer_id);
          body.append('support_pin', OrgData?.attributes?.support_pin);
          OrgData?.attributes?.createdBy &&
            body.append('createdBy', OrgData?.attributes?.createdBy);
          // body.append('redirectUrl', '');
          previewImage?.file
            ? body.append('organization_logo', previewImage?.file)
            : OrgData?.attributes?.organization_logo
              ? body.append(
                  'organization_logo',
                  OrgData?.attributes?.organization_logo.substring(
                    OrgData?.attributes?.organization_logo.lastIndexOf('/') + 1
                  )
                )
              : '';
          const config = {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          };
          const method = !superAdmin ? 'put' : 'put';
          const ApiUrl = !superAdmin
            ? ORG_URLS.UPDATE_ORG_DATA
            : ORG_URLS.UPDATE_ORG_DATA;
          try {
            const { status, data } = await axiosInstance[method](
              ApiUrl,
              body,
              config
            );
            if (status === 200) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                if (superAdmin) {
                  getOrgbyID(OrgId);
                  router.push('/sorg/organization');
                } else {
                  getOrgbyID(OrgId);
                }
              } else {
                setApiMessage('error', data?.message);
              }
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
        }) => {
          return (
            <Form onSubmit={handleSubmit}>
              <Card className="profile-card">
                <CardContent>
                  <Box className="profile-header">
                    <Box className="profile-avatar-section">
                      {previewImage ? (
                        <HeaderImage
                          imageUrl={previewImage?.url}
                          alt="Profile"
                          className="profile-avatar"
                          type="avtar"
                        />
                      ) : (
                        <Avatar className="profile-avatar">
                          <Business />
                        </Avatar>
                      )}
                      <Tooltip title="Change Logo">
                        <IconButton
                          className="edit-avatar-button"
                          component="label"
                        >
                          <input
                            type="file"
                            hidden
                            accept="image/*"
                            onChange={handleLogoChange}
                          />
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    {/* <Box className="profile-info">
                      <Typography variant="h5" className="profile-name">
                        {values.name || 'Organization Name'}
                      </Typography>
                      <Typography variant="body2" className="profile-subtitle">
                        {values.website && (
                          <a
                            href={values.website}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {values.website}
                          </a>
                        )}
                      </Typography>
                    </Box> */}
                  </Box>

                  <Typography variant="h6" className="org-form-title">
                    Organization Details
                  </Typography>
                  <Box className="form-grid">
                    <Box>
                      <CustomTextField
                        fullWidth
                        name="name"
                        label="Organization Name"
                        placeholder="Enter organization name"
                        value={values.name}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.name && Boolean(errors.name)}
                        helperText={touched.name && errors.name}
                        required
                        inputProps={{ maxLength: TextFieldMaxLength }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Business />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>

                    <Box>
                      <CustomTextField
                        fullWidth
                        name="website"
                        label="Website"
                        placeholder="Enter website URL"
                        value={values.website}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.website && Boolean(errors.website)}
                        helperText={touched.website && errors.website}
                        required
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Language />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  </Box>
                  <Typography variant="h6" className="org-form-title">
                    Contact details
                  </Typography>
                  <Box className="form-grid">
                    <Box>
                      <CustomTextField
                        fullWidth
                        name="email"
                        label="Email"
                        placeholder="Enter email"
                        value={values.email}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.email && Boolean(errors.email)}
                        helperText={touched.email && errors.email}
                        required
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Email />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>

                    <Box>
                      <CustomTextField
                        fullWidth
                        name="contact_number"
                        label="Contact Number"
                        placeholder="Enter contact number"
                        value={values.contact_number}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.contact_number &&
                          Boolean(errors.contact_number)
                        }
                        helperText={
                          touched.contact_number && errors.contact_number
                        }
                        required
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(
                            /[^0-9]/g,
                            ''
                          ); // Remove non-numeric characters
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Phone />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>

                    <Box>
                      <CustomTextField
                        fullWidth
                        required
                        name="addressLine1"
                        label="Address Line 1"
                        placeholder="Enter Address Line 1"
                        value={values.addressLine1}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.addressLine1 && Boolean(errors.addressLine1)
                        }
                        helperText={touched.addressLine1 && errors.addressLine1}
                      />
                    </Box>
                    <Box>
                      <CustomTextField
                        fullWidth
                        name="addressLine2"
                        label="Address Line 2"
                        placeholder="Enter Address Line 2"
                        value={values.addressLine2}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.addressLine2 && Boolean(errors.addressLine2)
                        }
                        helperText={touched.addressLine2 && errors.addressLine2}
                      />
                    </Box>
                    {/* <Box>
                      <CustomTextField
                        fullWidth
                        name="addressCity"
                        label="City"
                        placeholder="Enter City"
                        value={values.addressCity}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.addressCity && Boolean(errors.addressCity)
                        }
                        helperText={touched.addressCity && errors.addressCity}
                      />
                    </Box>
                    <Box>
                      <CustomTextField
                        fullWidth
                        name="addressState"
                        label="State"
                        placeholder="Enter State"
                        value={values.addressState}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.addressState && Boolean(errors.addressState)
                        }
                        helperText={touched.addressState && errors.addressState}
                      />
                    </Box> */}
                    <Box>
                      <CustomSelect
                        required
                        label="Country"
                        name="country"
                        placeholder="Select Country"
                        options={countries}
                        value={
                          countries?.find((opt) => {
                            return opt?.value === values?.country;
                          }) || ''
                        }
                        onChange={(selectedOption) => {
                          setFieldValue('country', selectedOption?.value || '');
                          // getCountry(e?.value);
                          setCities([]);
                          setCounties([]);
                          setFieldValue('addressCity', '');
                          setFieldValue('addressState', '');
                          setSelectedCountry(selectedOption?.value);
                        }}
                        error={touched?.country && errors?.country}
                        helperText={touched?.country && errors?.country}
                        // isDisabled
                        startAdornment={<LocationOn />}
                      />
                    </Box>

                    <Box>
                      <CustomSelect
                        label="State"
                        required={counties && counties?.length !== 0}
                        name="addressState"
                        placeholder="Select State"
                        options={counties}
                        value={
                          counties?.find((opt) => {
                            return opt?.value === values?.addressState;
                          }) || ''
                        }
                        onChange={(selectedOption) => {
                          setFieldValue(
                            'addressState',
                            selectedOption?.value || ''
                          );
                          setFieldValue('addressCity', '');
                          // getCityList(e?.value);
                          setCities([]);
                          setSelectedCounty(selectedOption?.value);
                        }}
                        error={touched?.addressState && errors?.addressState}
                        helperText={
                          touched?.addressState && errors?.addressState
                        }
                        // isDisabled
                        startAdornment={<LocationOn />}
                      />
                    </Box>

                    <Box>
                      <CustomSelect
                        required={cities && cities?.length !== 0}
                        label="City"
                        name="addressCity"
                        placeholder="Select City"
                        options={cities}
                        value={
                          cities?.find((opt) => {
                            return opt?.value === values?.addressCity;
                          }) || ''
                        }
                        onChange={(selectedOption) => {
                          setFieldValue(
                            'addressCity',
                            selectedOption?.value || ''
                          );
                        }}
                        error={touched?.addressCity && errors?.addressCity}
                        helperText={touched?.addressCity && errors?.addressCity}
                        // isDisabled
                        startAdornment={<LocationOn />}
                      />
                    </Box>

                    <Box>
                      <CustomTextField
                        required
                        fullWidth
                        name="postalCode"
                        label="Postal Code"
                        placeholder="Enter Postal Code"
                        value={values?.postalCode}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.postalCode && Boolean(errors.postalCode)}
                        helperText={touched.postalCode && errors.postalCode}
                      />
                    </Box>
                  </Box>
                  <Typography variant="h6" className="org-form-title">
                    Other settings
                  </Typography>
                  <Box className="form-grid">
                    <Box>
                      <CustomTextField
                        fullWidth
                        name="vat_number"
                        label="VAT Number"
                        placeholder="Enter VAT Number"
                        value={values.vat_number}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.vat_number && Boolean(errors.vat_number)}
                        helperText={touched.vat_number && errors.vat_number}
                        required
                      />
                    </Box>
                    {/* <Box className="align-content-center">
                      <Box className="label-wrap">
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={values?.isMultipleLocations}
                              onChange={(e) => {
                                setFieldValue(
                                  'isMultipleLocations',
                                  e.target.checked
                                );
                                setFieldValue('locations', '');
                              }}
                              name="isMultipleLocations"
                              color="primary"
                            />
                          }
                          label="Is it multiple locations* ?"
                        />
                      </Box>
                    </Box>
                    {values?.isMultipleLocations && (
                      <Box>
                        <CustomTextField
                          fullWidth
                          name="locations"
                          label="Number of Location"
                          placeholder="Enter number of locations"
                          value={values.locations}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.locations && Boolean(errors.locations)}
                          helperText={touched.locations && errors.locations}
                          required
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <LocationOnOutlinedIcon />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Box>
                    )} */}
                    {superAdmin && (
                      <Box>
                        <CustomSelect
                          placeholder="Select Status"
                          options={identifiers?.ORG_STATUS}
                          value={
                            identifiers?.ORG_STATUS?.find(
                              (opt) => opt?.value === values?.status
                            ) || ''
                          }
                          onChange={(selectedOption) =>
                            setFieldValue('status', selectedOption?.value || '')
                          }
                          label="Status"
                          error={touched?.status && errors?.status}
                          helperText={touched?.status && errors?.status}
                          required
                          startAdornment={<Business />}
                        />
                      </Box>
                    )}
                  </Box>
                  <Typography variant="h6" className="org-form-title">
                    Localization settings
                  </Typography>
                  <Box className="form-grid">
                    <Box>
                      <CustomSelect
                        label="Currency"
                        name="currency"
                        placeholder="Select Currency"
                        options={currencyData}
                        value={
                          currencyData?.find((opt) => {
                            return opt?.value === values?.currency;
                          }) || ''
                        }
                        onChange={(selectedOption) =>
                          setFieldValue('currency', selectedOption?.value || '')
                        }
                        error={touched?.currency && errors?.currency}
                        helperText={touched?.currency && errors?.currency}
                        isDisabled
                        startAdornment={<Public />}
                      />
                    </Box>
                    <Box>
                      <CustomSelect
                        label="Timezone"
                        name="timezone"
                        placeholder="Select Timezone"
                        options={timezoneData}
                        value={
                          timezoneData?.find(
                            (opt) => opt?.value === values?.timezone
                          ) || ''
                        }
                        onChange={(selectedOption) =>
                          setFieldValue('timezone', selectedOption?.value || '')
                        }
                        error={touched?.timezone && errors?.timezone}
                        helperText={touched?.timezone && errors?.timezone}
                        isDisabled
                        startAdornment={<AccessTime />}
                      />
                    </Box>
                  </Box>
                  <Typography variant="h6" className="org-form-title">
                    Social links
                  </Typography>
                  <Box className="form-grid">
                    <Box>
                      <CustomTextField
                        fullWidth
                        name="linkedin_link"
                        label="LinkedIn"
                        placeholder="Enter LinkedIn URL"
                        value={values.linkedin_link}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.linkedin_link && Boolean(errors.linkedin_link)
                        }
                        helperText={
                          touched.linkedin_link && errors.linkedin_link
                        }
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LinkedIn />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>

                    <Box>
                      <CustomTextField
                        fullWidth
                        name="facebook_link"
                        label="Facebook"
                        placeholder="Enter Facebook URL"
                        value={values.facebook_link}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.facebook_link && Boolean(errors.facebook_link)
                        }
                        helperText={
                          touched.facebook_link && errors.facebook_link
                        }
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Facebook />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>

                    <Box>
                      <CustomTextField
                        fullWidth
                        name="twitter_link"
                        label="Twitter"
                        placeholder="Enter Twitter URL"
                        value={values.twitter_link}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.twitter_link && Boolean(errors.twitter_link)
                        }
                        helperText={touched.twitter_link && errors.twitter_link}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Twitter />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  </Box>

                  <Box className="form-actions-btn">
                    {/* <CustomButton
                      variant="outlined"
                      className=""
                      startIcon={<CloseIcon />}
                      title="Cancel"
                    /> */}
                    <CustomButton
                      variant="contained"
                      type="submit"
                      className=""
                      startIcon={<Save />}
                      title="Save"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Form>
          );
        }}
      </Formik>
    </Box>
  );
}
