import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import CustomTextField from '@/components/UI/CustomTextField';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import './ContactCard.scss';

const validationSchema = Yup.object().shape({
  name: Yup.string().trim().required('Name is required'),
  email: Yup.string()
    .trim()
    .email('Invalid email')
    .required('Email is required'),
  phone: Yup.string().trim(),
  message: Yup.string().trim().required('Message is required'),
});

const initialValues = {
  name: '',
  email: '',
  phone: '',
  message: '',
};

const ContactForm = ({ handleFormSubmit }) => (
  <Formik
    initialValues={initialValues}
    validationSchema={validationSchema}
    onSubmit={handleFormSubmit}
  >
    {({ values, errors, touched, handleChange, handleBlur, isSubmitting }) => (
      <div className="contact-card">
        <div className="contact-card__header">
          <p className="contact-card__title">
            <Icon name="MessageSquare" size={20} color="currentColor" />
            <span>Contact Us</span>
          </p>
        </div>
        <div className="contact-card__content">
          <Form className="contact-card__form">
            {/* First Row - Name and Email */}
            <div className="contact-card__form-row contact-card__form-row--two-columns">
              <CustomTextField
                label="Name"
                name="name"
                value={values.name}
                onChange={handleChange}
                onBlur={handleBlur}
                fullWidth
                required
                placeholder="Enter name"
                error={touched.name && Boolean(errors.name)}
                helperText={touched.name && errors.name}
              />
              <CustomTextField
                label="Email"
                name="email"
                type="email"
                value={values.email}
                onChange={handleChange}
                onBlur={handleBlur}
                fullWidth
                required
                placeholder="Enter email"
                error={touched.email && Boolean(errors.email)}
                helperText={touched.email && errors.email}
              />
            </div>
            {/* Second Row - Phone */}
            <div className="contact-card__form-row">
              <CustomTextField
                label="Phone"
                name="phone"
                type="tel"
                value={values.phone}
                onChange={handleChange}
                onBlur={handleBlur}
                fullWidth
                placeholder="Enter phone"
                error={touched.phone && Boolean(errors.phone)}
                helperText={touched.phone && errors.phone}
              />
            </div>
            {/* Third Row - Message */}
            <div className="contact-card__form-row">
              <CustomTextField
                label="Message"
                name="message"
                multiline
                rows={4}
                value={values.message}
                onChange={handleChange}
                onBlur={handleBlur}
                fullWidth
                required
                placeholder="Enter message"
                error={touched.message && Boolean(errors.message)}
                helperText={touched.message && errors.message}
              />
            </div>
            <div className="message-button-wrap send-message-btn-wrap">
              <CustomButton
                className="message-button send-message-btn"
                type="submit"
                variant="contained"
                title={isSubmitting ? 'Sending...' : 'Send Message'}
                leftIcon={<Icon name="Send" size={16} />}
                disabled={isSubmitting}
              />
            </div>
          </Form>
        </div>
      </div>
    )}
  </Formik>
);

export default ContactForm;
