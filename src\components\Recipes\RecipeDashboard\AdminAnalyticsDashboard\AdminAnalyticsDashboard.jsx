'use client';

import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import ContextualHeader from '@/components/ui/ContextualHeader';
import AdminSidebar from '@/components/ui/AdminSidebar';
import StatisticsTile from '@/components/Recipes/RecipeDashboard/StatisticsTile';
import ChartWidget from '@/components/Recipes/RecipeDashboard/ChartWidget';
import QuickActionPanel from '@/components/Recipes/RecipeDashboard/QuickActionPanel';
import DateRangePicker from '@/components/Recipes/RecipeDashboard/DateRangePicker';
import FilterControls from '@/components/Recipes/RecipeDashboard/FilterControls';
import RecentActivity from '@/components/Recipes/RecipeDashboard/RecentActivity';
import './adminanalyticsdashboard.scss';

const AdminAnalyticsDashboard = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [selectedDateRange, setSelectedDateRange] = useState('30days');
  const [activeFilters, setActiveFilters] = useState({
    category: '',
    userType: '',
    region: '',
    device: '',
  });
  const [isExporting, setIsExporting] = useState(false);
  const [realTimeData, setRealTimeData] = useState({});

  // Mock statistics data
  const statisticsData = [
    {
      id: 'total-recipes',
      title: 'Total Recipes',
      value: '1,234',
      change: '+12.5%',
      trend: 'up',
      sparklineData: [45, 52, 48, 61, 55, 67, 73, 69, 78, 82],
      icon: 'ChefHat',
      color: 'primary',
      description: 'Published recipes across all categories',
    },
    {
      id: 'active-users',
      title: 'Active Users',
      value: '856',
      change: '+8.3%',
      trend: 'up',
      sparklineData: [32, 38, 35, 42, 48, 45, 52, 58, 55, 61],
      icon: 'Users',
      color: 'success',
      description: 'Monthly active users',
    },
    {
      id: 'popular-categories',
      title: 'Top Category',
      value: 'Desserts',
      change: '+15.2%',
      trend: 'up',
      sparklineData: [28, 32, 35, 38, 42, 45, 48, 52, 55, 58],
      icon: 'TrendingUp',
      color: 'accent',
      description: 'Most viewed recipe category',
    },
    {
      id: 'revenue-metrics',
      title: 'Revenue',
      value: '$12,450',
      change: '+22.1%',
      trend: 'up',
      sparklineData: [15, 18, 22, 25, 28, 32, 35, 38, 42, 45],
      icon: 'DollarSign',
      color: 'warning',
      description: 'Monthly subscription revenue',
    },
    {
      id: 'growth-indicators',
      title: 'Growth Rate',
      value: '18.5%',
      change: '+3.2%',
      trend: 'up',
      sparklineData: [12, 15, 18, 21, 24, 27, 30, 33, 36, 39],
      icon: 'BarChart3',
      color: 'secondary',
      description: 'User acquisition growth',
    },
    {
      id: 'engagement-rate',
      title: 'Engagement',
      value: '74.2%',
      change: '+5.8%',
      trend: 'up',
      sparklineData: [65, 68, 70, 72, 71, 73, 75, 74, 76, 78],
      icon: 'Heart',
      color: 'error',
      description: 'User engagement rate',
    },
  ];

  // Mock chart data
  const chartWidgets = [
    {
      id: 'recipe-views',
      title: 'Recipe Views Trend',
      type: 'line',
      data: [
        { name: 'Jan', views: 4000, likes: 2400 },
        { name: 'Feb', views: 3000, likes: 1398 },
        { name: 'Mar', views: 2000, likes: 9800 },
        { name: 'Apr', views: 2780, likes: 3908 },
        { name: 'May', views: 1890, likes: 4800 },
        { name: 'Jun', views: 2390, likes: 3800 },
      ],
    },
    {
      id: 'category-performance',
      title: 'Category Performance',
      type: 'bar',
      data: [
        { name: 'Desserts', recipes: 245, views: 12500 },
        { name: 'Main Course', recipes: 189, views: 9800 },
        { name: 'Appetizers', recipes: 156, views: 7200 },
        { name: 'Beverages', recipes: 98, views: 4500 },
        { name: 'Snacks', recipes: 87, views: 3200 },
      ],
    },
    {
      id: 'user-engagement',
      title: 'User Engagement Heatmap',
      type: 'heatmap',
      data: [
        { hour: '00:00', day: 'Mon', value: 12 },
        { hour: '06:00', day: 'Mon', value: 45 },
        { hour: '12:00', day: 'Mon', value: 78 },
        { hour: '18:00', day: 'Mon', value: 92 },
        { hour: '00:00', day: 'Tue', value: 15 },
        { hour: '06:00', day: 'Tue', value: 52 },
        { hour: '12:00', day: 'Tue', value: 85 },
        { hour: '18:00', day: 'Tue', value: 98 },
      ],
    },
    {
      id: 'conversion-analytics',
      title: 'Conversion Analytics',
      type: 'funnel',
      data: [
        { stage: 'Visitors', count: 10000, percentage: 100 },
        { stage: 'Recipe Views', count: 7500, percentage: 75 },
        { stage: 'Bookmarks', count: 3200, percentage: 32 },
        { stage: 'Shares', count: 1800, percentage: 18 },
        { stage: 'Subscriptions', count: 450, percentage: 4.5 },
      ],
    },
  ];

  // Mock quick actions data
  const quickActions = [
    {
      id: 'approve-recipes',
      title: 'Approve Pending Recipes',
      count: 23,
      icon: 'CheckCircle',
      color: 'success',
      action: '/admin/pending-recipes',
    },
    {
      id: 'manage-featured',
      title: 'Manage Featured Content',
      count: 8,
      icon: 'Star',
      color: 'warning',
      action: '/admin/featured-content',
    },
    {
      id: 'user-feedback',
      title: 'Respond to Feedback',
      count: 15,
      icon: 'MessageCircle',
      color: 'primary',
      action: '/contact-form-management',
    },
    {
      id: 'content-reports',
      title: 'Review Content Reports',
      count: 5,
      icon: 'AlertTriangle',
      color: 'error',
      action: '/admin/reports',
    },
  ];

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeData({
        timestamp: new Date().toLocaleTimeString(),
        activeUsers: Math.floor(Math.random() * 100) + 800,
        newRecipes: Math.floor(Math.random() * 5),
        pendingReviews: Math.floor(Math.random() * 10) + 20,
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const handleStatisticClick = (statisticId) => {
    console.log('Statistic clicked:', statisticId);
    // Navigate to detailed report
  };

  const handleDateRangeChange = (range) => {
    setSelectedDateRange(range);
    console.log('Date range changed:', range);
  };

  const handleFilterChange = (filters) => {
    setActiveFilters(filters);
    console.log('Filters changed:', filters);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Simulate export process
      await new Promise((resolve) => setTimeout(resolve, 2000));
      console.log(`Exporting data in ${format} format`);
      // Implement actual export logic here
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleSidebarToggle = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className="admin-analytics-dashboard">
      <ContextualHeader />
      <AdminSidebar
        isCollapsed={isSidebarCollapsed}
        onToggleCollapse={handleSidebarToggle}
      />

      <main
        className={`admin-analytics-dashboard__main${
          isSidebarCollapsed
            ? ' admin-analytics-dashboard__main--collapsed'
            : ''
        }`}
      >
        <div className="admin-analytics-dashboard__container">
          {/* Header Section */}
          <div className="admin-analytics-dashboard__header">
            <div className="admin-analytics-dashboard__header-content">
              <div className="admin-analytics-dashboard__header-info">
                <h1 className="admin-analytics-dashboard__title">
                  Analytics Dashboard
                </h1>
                <p className="admin-analytics-dashboard__subtitle">
                  Comprehensive insights into recipe performance and user
                  engagement
                </p>
                {realTimeData.timestamp && (
                  <div className="admin-analytics-dashboard__live-indicator">
                    <div className="admin-analytics-dashboard__live-dot"></div>
                    <span className="admin-analytics-dashboard__live-text">
                      Live data • Last updated {realTimeData.timestamp}
                    </span>
                  </div>
                )}
              </div>

              <div className="admin-analytics-dashboard__header-actions">
                <DateRangePicker
                  value={selectedDateRange}
                  onChange={handleDateRangeChange}
                />
                <div className="admin-analytics-dashboard__export-buttons">
                  <button
                    onClick={() => handleExport('pdf')}
                    disabled={isExporting}
                    className="admin-analytics-dashboard__export-btn admin-analytics-dashboard__export-btn--primary"
                    type="button"
                  >
                    <Icon name="FileText" size={16} />
                    <span>Export PDF</span>
                  </button>
                  <button
                    onClick={() => handleExport('csv')}
                    disabled={isExporting}
                    className="admin-analytics-dashboard__export-btn admin-analytics-dashboard__export-btn--secondary"
                    type="button"
                  >
                    <Icon name="Download" size={16} />
                    <span>Export CSV</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Filter Controls */}
            <FilterControls
              filters={activeFilters}
              onChange={handleFilterChange}
            />
          </div>

          {/* Statistics Tiles Grid */}
          <div className="admin-analytics-dashboard__stats-grid">
            {statisticsData.map((stat) => (
              <StatisticsTile
                key={stat.id}
                {...stat}
                onClick={() => handleStatisticClick(stat.id)}
                realTimeValue={realTimeData[stat.id]}
              />
            ))}
          </div>

          {/* Chart Widgets Grid */}
          <div className="admin-analytics-dashboard__charts-grid">
            {chartWidgets.map((widget) => (
              <ChartWidget
                key={widget.id}
                {...widget}
                dateRange={selectedDateRange}
                filters={activeFilters}
              />
            ))}
          </div>

          {/* Bottom Section */}
          <div className="admin-analytics-dashboard__bottom-section">
            {/* Quick Action Panels */}
            <div className="admin-analytics-dashboard__quick-actions">
              <QuickActionPanel actions={quickActions} />
            </div>

            {/* Recent Activity */}
            <div className="admin-analytics-dashboard__recent-activity">
              <RecentActivity />
            </div>
          </div>
        </div>
      </main>

      {/* Export Loading Overlay */}
      {isExporting && (
        <div className="admin-analytics-dashboard__export-overlay">
          <div className="admin-analytics-dashboard__export-modal">
            <div className="admin-analytics-dashboard__export-spinner">
              <Icon name="Loader2" size={24} />
            </div>
            <span className="admin-analytics-dashboard__export-text">
              Exporting data...
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminAnalyticsDashboard;

/*
Usage Example:

import AdminAnalyticsDashboard from '@/components/Recipes/RecipeDashboard/AdminAnalyticsDashboard';

function AdminPage() {
  return (
    <AdminAnalyticsDashboard />
  );
}

Features:
- Real-time data updates every 30 seconds
- Interactive statistics tiles with sparklines
- Multiple chart types (line, bar, heatmap, funnel)
- Date range filtering
- Export functionality (PDF/CSV)
- Quick action panels
- Recent activity feed
- Responsive sidebar layout
- Loading states and animations
- Filter controls integration

Note: This component requires the following components to be available:
- ContextualHeader
- AdminSidebar
- FilterControls (needs to be created)
- All the dashboard sub-components we've created
*/
