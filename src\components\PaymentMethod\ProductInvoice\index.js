'use client';
import React, { useEffect, useState } from 'react';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import { Box, Chip, Tooltip, Typography } from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import HeaderImage from '@/components/UI/ImageSecurity';
import './productinvoice.scss'; // Import SCSS file

const ProductInvoice = ({ invoiceData }) => {
  const [currencyData, setCurrencyData] = useState();

  let tableData = invoiceData;
  const getCurrency = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_CURRENCIES
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          let currencyOptions = data?.data?.map((currency) => {
            return {
              label: currency?.code,
              value: currency?.code,
              symbol: currency?.symbol,
            };
          });
          setCurrencyData(currencyOptions);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getCurrency();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    if (!currencyCode || !currencyData) return '';
    const currencySymbol = currencyData?.find((currency) => {
      return currency?.value === currencyCode?.toUpperCase();
    });
    return currencySymbol?.symbol || '';
  };

  const columns = [
    {
      field: 'invoice_id',
      headerName: '#',
      width: 70,
      minWidth: 70,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        const invoiceId = params?.row?.invoice_details?.invoice_id;
        return (
          <Typography className="text-ellipsis table-body-text">
            {invoiceId ?? '-'}
          </Typography>
        );
      },
    },
    {
      field: 'plan_name',
      headerName: 'Activity',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        const planName = params?.row?.subscription_details?.plan_name;
        return (
          <Tooltip arrow title={planName ?? '-'}>
            <Typography className="text-ellipsis table-body-text">
              {planName ?? '-'}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: 'description',
      headerName: 'Description',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const startDate =
          params?.row?.subscription_details?.subscription_start_date;
        const endDate =
          params?.row?.subscription_details?.subscription_end_date;
        const description =
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged.";

        if (startDate && endDate) {
          return (
            <Box>
              <Typography className="text-ellipsis table-body-text">
                {startDate}
              </Typography>
              <Typography className="text-ellipsis table-body-text">
                {endDate}
              </Typography>
            </Box>
          );
        } else if (description) {
          return (
            <Box className="descriptio-text-wrap">
              <Typography className="table-body-text descriptio-text">
                {description.substring(0, 40)}
              </Typography>
            </Box>
          );
        }
        return (
          <Typography className="text-ellipsis table-body-text">-</Typography>
        );
      },
    },
    {
      field: 'qty',
      headerName: 'Qty',
      width: 50,
      minWidth: 50,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: () => {
        // const totalDays = params?.row?.total_days;
        return (
          <Typography className="text-ellipsis table-body-text">
            {/* {params?.row?.total_days || '-'} */}1
            {/* <Typography className="text-ellipsis table-body-text">
              {totalDays !== null && totalDays !== undefined ? totalDays : '-'}
            </Typography> */}
          </Typography>
        );
      },
    },
    {
      field: 'Rate',
      headerName: 'Rate',
      width: 60,
      minWidth: 60,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const cost = params?.row?.subscription_details?.plan_cost;
        const currency = params?.row?.subscription_details?.plan_currency;
        const symbol = getCurrencySymbol(currency);

        return (
          <Tooltip arrow title={`${symbol}${cost ?? 0}`}>
            <Typography className="text-ellipsis table-body-text">
              {`${symbol}${cost ?? 0}`}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: 'vat_percentage',
      headerName: 'Vat',
      width: 60,
      minWidth: 60,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const currency = params?.row?.subscription_details?.plan_currency;
        const vat = params?.row?.payment_details?.vat_amount;
        const symbol = getCurrencySymbol(currency);

        return (
          <Tooltip arrow title={`${symbol}${vat ?? 0}`}>
            <Typography className="text-ellipsis table-body-text">
              {`${symbol}${vat ?? 0}`}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: 'plan_cost',
      headerName: 'Amount',
      width: 80,
      minWidth: 80,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const symbol = getCurrencySymbol(
          params?.row?.subscription_details?.plan_currency
        );
        const PlanAmount = params.row.subscription_details.plan_cost;
        return (
          <Tooltip arrow title={`${symbol}${PlanAmount ?? 0}`}>
            <Typography className="text-ellipsis table-body-text">
              {symbol}
              {PlanAmount ?? 0}
            </Typography>
          </Tooltip>
        );
      },
    },
  ];
  const vatSummary = [
    {
      field: 'rate',
      headerName: 'Rate ',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const rate = params?.row?.vat_summary?.rate;
        return (
          <Typography className="text-ellipsis table-body-text">
            {rate ?? 'VAT@0%'}
          </Typography>
        );
      },
    },
    {
      field: 'vat',
      headerName: 'Vat',
      width: 50,
      minWidth: 50,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const symbol = getCurrencySymbol(
          params?.row?.subscription_details?.plan_currency
        );
        const vat = params?.row?.vat_summary?.vat;

        return (
          <Typography className="text-ellipsis table-body-text">
            {symbol}
            {vat ?? 0}
          </Typography>
        );
      },
    },
    {
      field: 'net',
      headerName: 'Net',
      width: 80,
      minWidth: 80,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const symbol = getCurrencySymbol(
          params?.row?.subscription_details?.plan_currency
        );
        const net = params?.row?.vat_summary?.net;
        return (
          <Typography className="text-ellipsis table-body-text">
            {symbol}
            {net ?? 0}
          </Typography>
        );
      },
    },
  ];

  return (
    <>
      {invoiceData?.map((invoiceData) => {
        const currencySymbol = getCurrencySymbol(
          invoiceData?.subscription_details?.plan_currency
        );
        return (
          <Box className="invoice-container">
            <Box className="invoice-header-wrap">
              <Chip
                className={`status-chip ${invoiceData?.invoice_details?.payment_status === 'PAID' ? 'active-status' : 'fail-status'}`}
                label={invoiceData?.invoice_details?.payment_status}
                size="small"
                icon={
                  invoiceData?.invoice_details?.payment_status === 'PAID' ? (
                    <CheckCircleOutlineIcon />
                  ) : (
                    <HighlightOffIcon />
                  )
                }
              />
              <Box className="d-flex align-center justify-end company-logo-wrap">
                <HeaderImage
                  imageUrl={invoiceData?.company_details?.logo}
                  alt="Company logo"
                  width={75}
                  height={65}
                  type="lazyload"
                />
              </Box>
              <Box className="d-flex justify-space-between">
                <Box className="invoice-number-wrap">
                  <Typography className="invoice-number-text pb8">
                    Invoice
                  </Typography>
                  <Typography className="body-sm invoice-number-text-wrap">
                    Invoice Number
                  </Typography>
                  <Typography className="caption-text">
                    {invoiceData?.invoice_details?.invoice_id}
                  </Typography>
                </Box>
                <Box className="address-wrap text-align-end">
                  <Typography className="caption-text ">
                    <span className="body-sm">
                      {invoiceData?.company_details?.name}
                    </span>
                    <br />
                    <span>{invoiceData?.company_details?.address1}</span>
                    <br />
                    <span>{invoiceData?.company_details?.address2}</span>
                    <br />
                    <span>{invoiceData?.company_details?.address3}</span>
                    <br />
                    <span className="company-email">
                      <a
                        href={`mailto:${invoiceData?.company_details?.email}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-link"
                      >
                        {invoiceData?.company_details?.email}
                      </a>
                    </span>
                  </Typography>
                  <Typography className="caption-text">
                    VAT No.: {invoiceData?.company_details?.vat_number}
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Invoice Details */}
            <Box className="invoice-details d-flex justify-space-between">
              <Box className="client-info">
                <Typography className="invoice-number client-text">
                  Invoice To
                </Typography>
                <Typography className="skynet-digital-text client-text">
                  {invoiceData?.organization_details?.name}
                </Typography>
                <Typography className="invoice-number client-address-info">
                  {invoiceData?.organization_details?.address}{' '}
                  {invoiceData?.organization_details?.address1}
                </Typography>
              </Box>
              <Box className="balance-due-wrap text-align-end">
                <Typography className="invoice-number">Issue Date</Typography>
                <Typography className="body-sm">
                  {invoiceData?.invoice_details?.payment_deduction_date}
                </Typography>
              </Box>
            </Box>

            {/* Table */}
            <Box className="table-container invoice-table-wrap">
              <DataGrid
                rows={tableData}
                columns={columns}
                checkboxSelection={false}
                disableSelectionOnClick
                hideMenuIcon
                disableColumnSorting
                getRowHeight={() => 'auto'}
                sx={{
                  transition: 'none',
                  [`& .${gridClasses.cell}`]: {
                    py: 1,
                  },
                }}
              />
            </Box>

            {/* Invoice Summary */}
            <Box className="sub-total-wrap d-flex justify-end">
              <Box className="summery-total-wrap">
                {/* <Typography className="summery-text">Discount (0%)</Typography> */}
                <Box className="summery-text-wrap d-flex align-center">
                  <Typography className="summery-text text-align-end">
                    Sub Total
                  </Typography>
                  <Typography className="summery-text text-align-end cost-text-wrap">
                    {currencySymbol}
                    {invoiceData.payment_details.sub_total ?? 0}
                  </Typography>
                </Box>
                <Box className="summery-text-wrap d-flex align-center">
                  <Typography className="summery-text text-align-end">
                    Transaction charge
                  </Typography>
                  <Typography className="summery-text text-align-end cost-text-wrap">
                    {currencySymbol}
                    {invoiceData?.payment_details?.transaction_fees ?? 0}
                  </Typography>
                </Box>
                <Box className="summery-text-wrap d-flex align-center">
                  <Typography className="summery-text text-align-end">
                    {' '}
                    VAT({invoiceData?.payment_details?.vat_percentage ?? 0}%)
                  </Typography>
                  <Typography className="summery-text cost-text-wrap text-align-end">
                    {currencySymbol}
                    {invoiceData?.payment_details?.vat_amount ?? 0}
                  </Typography>
                </Box>

                <Box className="summery-text-wrap d-flex align-center">
                  <Typography className="summery-text text-align-end">
                    {' '}
                    Total
                  </Typography>
                  <Typography className="summery-text text-align-end cost-text-wrap">
                    {currencySymbol}
                    {invoiceData?.payment_details?.total_amount ?? 0}
                  </Typography>
                </Box>
              </Box>
            </Box>
            <Box>
              <Typography className="invoice-number-text pt8">
                Vat Summary
              </Typography>
              <Box className="table-container invoice-table-wrap vat-summary-table">
                <DataGrid
                  rows={tableData}
                  columns={vatSummary}
                  checkboxSelection={false}
                  disableSelectionOnClick
                  hideMenuIcon
                  disableColumnSorting
                  rowHeight={40}
                  // getRowHeight={() => 'auto'}
                  sx={{
                    transition: 'none',
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                />
              </Box>
            </Box>
            <Box className="pt16">
              <Typography className="invoice-number-text pb4">
                Bank Details :
              </Typography>
              <Typography className="body-sm-regular">
                Account Holder: {invoiceData?.bank_details?.account_holder_name}
              </Typography>
              <Typography className="body-sm-regular">
                Sort Code: {invoiceData?.bank_details?.sort_code}
              </Typography>
              <Typography className="body-sm-regular">
                Account No: {invoiceData?.bank_details?.account_number}
              </Typography>
            </Box>
          </Box>
        );
      })}
    </>
  );
};

export default ProductInvoice;
