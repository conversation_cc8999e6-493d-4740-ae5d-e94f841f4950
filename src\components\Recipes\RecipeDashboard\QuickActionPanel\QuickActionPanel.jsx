'use client';

import React from 'react';
import Link from 'next/link';
import Icon from '@/components/AppIcon';
import './quickactionpanel.scss';

const QuickActionPanel = ({ actions = [] }) => {
  const getColorClass = (color) => {
    const colorClasses = {
      primary: 'quick-action-panel__action-card--primary',
      success: 'quick-action-panel__action-card--success',
      warning: 'quick-action-panel__action-card--warning',
      error: 'quick-action-panel__action-card--error',
      accent: 'quick-action-panel__action-card--accent',
    };
    return colorClasses[color] || colorClasses.primary;
  };

  const getTotalPendingActions = () => {
    return actions.reduce((sum, action) => sum + (action.count || 0), 0);
  };

  return (
    <div className="quick-action-panel">
      {/* Panel Header */}
      <div className="quick-action-panel__header">
        <div className="quick-action-panel__header-content">
          <div className="quick-action-panel__header-info">
            <h3 className="quick-action-panel__title">Quick Actions</h3>
            <p className="quick-action-panel__subtitle">
              Common administrative tasks requiring attention
            </p>
          </div>
          <Icon
            name="Zap"
            size={20}
            className="quick-action-panel__header-icon"
          />
        </div>
      </div>

      {/* Actions Grid */}
      <div className="quick-action-panel__content">
        <div className="quick-action-panel__grid">
          {actions.map((action) => (
            <Link
              key={action.id}
              href={action.action || '#'}
              className={`quick-action-panel__action-card ${getColorClass(action.color)}`}
            >
              <div className="quick-action-panel__action-content">
                <div className="quick-action-panel__action-icon-wrapper">
                  <Icon
                    name={action.icon}
                    size={20}
                    className="quick-action-panel__action-icon"
                  />
                </div>

                <div className="quick-action-panel__action-details">
                  <h4 className="quick-action-panel__action-title">
                    {action.title}
                  </h4>

                  <div className="quick-action-panel__action-meta">
                    <span className="quick-action-panel__action-count">
                      {action.count || 0} pending
                    </span>
                    <Icon
                      name="ArrowRight"
                      size={16}
                      className="quick-action-panel__action-arrow"
                    />
                  </div>
                </div>
              </div>

              {/* Count Badge */}
              {action.count > 0 && (
                <div className="quick-action-panel__badge">
                  {action.count > 99 ? '99+' : action.count}
                </div>
              )}
            </Link>
          ))}
        </div>
      </div>

      {/* Panel Footer */}
      <div className="quick-action-panel__footer">
        <div className="quick-action-panel__footer-content">
          <span className="quick-action-panel__footer-text">
            Total pending actions: {getTotalPendingActions()}
          </span>
          <Link
            href="/admin/all-actions"
            className="quick-action-panel__footer-link"
          >
            <span>View all actions</span>
            <Icon name="ExternalLink" size={14} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default QuickActionPanel;

/*
Usage Example:

import QuickActionPanel from '@/components/Recipes/RecipeDashboard/QuickActionPanel';

const sampleActions = [
  {
    id: 1,
    title: 'Pending Approvals',
    icon: 'CheckCircle',
    count: 5,
    color: 'warning',
    action: '/admin/approvals'
  },
  {
    id: 2,
    title: 'New Messages',
    icon: 'MessageSquare',
    count: 12,
    color: 'primary',
    action: '/admin/messages'
  }
];

function MyComponent() {
  return (
    <QuickActionPanel actions={sampleActions} />
  );
}
*/
