.admin-analytics-dashboard {
  min-height: 100vh;
  background-color: var(--color-off-white);

  // Main Content Area
  &__main {
    transition: all 0.3s ease-out;
    margin-left: 16rem; // 64 * 4 = 256px (sidebar width)
    padding-top: 4rem; // 16 * 4 = 64px (header height)

    &--collapsed {
      margin-left: 4rem; // 16px (collapsed sidebar width)
    }

    @media (max-width: 1024px) {
      margin-left: 0;
      padding-top: 4rem;
    }
  }

  // Container
  &__container {
    padding: var(--spacing-xxl);

    @media (max-width: 768px) {
      padding: var(--spacing-lg);
    }
  }

  // Header Section
  &__header {
    margin-bottom: var(--spacing-3xl);
  }

  &__header-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);

    @media (min-width: 1024px) {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-4xl);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);

    @media (max-width: 768px) {
      font-size: var(--font-size-3xl);
    }
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-sm);
  }

  // Live Indicator
  &__live-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
  }

  &__live-dot {
    width: var(--spacing-sm);
    height: var(--spacing-sm);
    background-color: var(--color-success);
    border-radius: var(--border-radius-full);
    animation: pulse 2s infinite;
  }

  &__live-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-success);
  }

  // Header Actions
  &__header-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;

    @media (min-width: 640px) {
      flex-direction: row;
      align-items: center;
    }

    @media (min-width: 1024px) {
      margin-top: 0;
    }
  }

  &__export-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__export-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.15s ease-out;
    border: var(--border-width-xs) solid;

    &:hover {
      transform: scale(1.02);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    &--primary {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      border-color: var(--color-primary);

      &:hover:not(:disabled) {
        background-color: var(--color-dark-blue);
        border-color: var(--color-dark-blue);
      }
    }

    &--secondary {
      background-color: transparent;
      color: var(--color-primary);
      border-color: var(--color-primary);

      &:hover:not(:disabled) {
        background-color: var(--color-primary-opacity);
      }
    }
  }

  // Statistics Grid
  &__stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xxl);
    margin-bottom: var(--spacing-3xl);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 1536px) {
      grid-template-columns: repeat(6, 1fr);
    }
  }

  // Charts Grid
  &__charts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xxl);
    margin-bottom: var(--spacing-3xl);

    @media (min-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // Bottom Section
  &__bottom-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xxl);

    @media (min-width: 1024px) {
      grid-template-columns: 2fr 1fr;
    }
  }

  &__quick-actions {
    // Styles handled by QuickActionPanel component
  }

  &__recent-activity {
    // Styles handled by RecentActivity component
  }

  // Export Overlay
  &__export-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
  }

  &__export-modal {
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xxl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    box-shadow: var(--box-shadow-xs);
  }

  &__export-spinner {
    animation: spin 1s linear infinite;
    color: var(--color-primary);
  }

  &__export-text {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  // Responsive Design
  @media (max-width: 640px) {
    &__header-actions {
      width: 100%;
    }

    &__export-buttons {
      width: 100%;
      justify-content: space-between;
    }

    &__export-btn {
      flex: 1;
      justify-content: center;
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
