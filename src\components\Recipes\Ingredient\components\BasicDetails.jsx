import React from 'react';
import { Box, Typography, InputAdornment } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import { staticOptions } from '@/helper/common/staticOptions';
import { allowedOnlyNumbers } from '@/helper/common/commonFunctions';

const BasicDetails = ({
  values,
  errors,
  touched,
  handleBlur,
  handleChange,
  setFieldValue,
  isDefault,
  unitsOfMeasureOptions,
  ingredientCategories,
  currency,
}) => {
  return (
    <>
      <Box className="ingredient-grid-container">
        <Box>
          <CustomTextField
            fullWidth
            name="ingredient_name"
            value={values?.ingredient_name}
            label="Name"
            placeholder="Ingredient name"
            error={Boolean(touched?.ingredient_name && errors?.ingredient_name)}
            helperText={touched?.ingredient_name && errors?.ingredient_name}
            onBlur={handleBlur}
            onChange={(e) => {
              if (e.target.value.length <= 90) {
                handleChange(e);
              }
            }}
            required
            disabled={isDefault}
            labelIcon={
              <Typography className="sub-title-text">
                {values?.ingredient_name?.length + '/ 90'}
              </Typography>
            }
          />
        </Box>
        <Box className="d-flex w100 gap-sm">
          <Box className="w100">
            <CustomSelect
              fullWidth
              label="Status"
              name="ingredient_status"
              placeholder="Status"
              options={staticOptions?.ORG_STATUS}
              value={
                staticOptions?.ORG_STATUS?.find(
                  (opt) => opt?.value === values?.ingredient_status
                ) || ''
              }
              onChange={(selectedOption) =>
                setFieldValue('ingredient_status', selectedOption?.value || '')
              }
              error={Boolean(
                touched?.ingredient_status && errors?.ingredient_status
              )}
              helperText={
                touched?.ingredient_status && errors?.ingredient_status
              }
              isDisabled={isDefault}
              isClearable={false}
              required
              menuPortalTarget={document.body}
              styles={{
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                }),
              }}
            />
          </Box>
          <Box className="w100">
            <CustomSelect
              fullWidth
              label="Category/Tags"
              name="category_tags"
              placeholder="Category"
              options={ingredientCategories}
              value={
                ingredientCategories?.find(
                  (opt) => opt?.value === values?.category_tags
                ) || ''
              }
              onChange={(selectedOption) =>
                setFieldValue('category_tags', selectedOption?.value || '')
              }
              error={Boolean(touched?.category_tags && errors?.category_tags)}
              helperText={touched?.category_tags && errors?.category_tags}
              isDisabled={isDefault}
              isClearable={false}
              required
              menuPortalTarget={document.body}
              styles={{
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                }),
              }}
            />
          </Box>
        </Box>
      </Box>
      <Box className="ingredient-textarea-container mt16">
        <CustomTextField
          fullWidth
          name="ingredient_description"
          value={values?.ingredient_description}
          label="Description"
          placeholder="Ingredient description"
          error={Boolean(
            touched?.ingredient_description && errors?.ingredient_description
          )}
          helperText={
            touched?.ingredient_description && errors?.ingredient_description
          }
          onBlur={handleBlur}
          onChange={(e) => {
            if (e.target.value.length <= 160) {
              handleChange(e);
            }
          }}
          disabled={isDefault}
          multiline
          rows={3}
          labelIcon={
            <Typography className="sub-title-text">
              {values?.ingredient_description?.length + '/ 160'}
            </Typography>
          }
        />
      </Box>

      <Box className="ingredient-grid-container mt16">
        <Box className="d-flex w100 gap-sm">
          <Box className="w100">
            <CustomSelect
              fullWidth
              label="Units of Measure"
              name="units_of_measure"
              placeholder="Units of Measure"
              options={unitsOfMeasureOptions}
              value={
                unitsOfMeasureOptions?.find((opt) => {
                  return opt?.value === values?.units_of_measure;
                }) || ''
              }
              onChange={(selectedOption) =>
                setFieldValue('units_of_measure', selectedOption?.value || '')
              }
              error={Boolean(
                touched?.units_of_measure && errors?.units_of_measure
              )}
              helperText={touched?.units_of_measure && errors?.units_of_measure}
              isDisabled={isDefault}
              isClearable={false}
              required
              menuPortalTarget={document.body}
              styles={{
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                }),
              }}
            />
          </Box>
          <Box className="w100">
            <CustomTextField
              fullWidth
              name="ingredient_cost"
              value={values?.ingredient_cost}
              label="Cost"
              placeholder="cost"
              error={Boolean(
                touched?.ingredient_cost && errors?.ingredient_cost
              )}
              helperText={touched?.ingredient_cost && errors?.ingredient_cost}
              onBlur={handleBlur}
              onChange={(e) => {
                handleChange(e);
              }}
              onInput={(e) => allowedOnlyNumbers(e)}
              required
              disabled={isDefault}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Typography className="title-text">{currency}</Typography>
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Typography className="title-text">
                      /{values?.units_of_measure}
                    </Typography>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        </Box>
        <Box className="ingredient-half-field">
          <CustomTextField
            fullWidth
            name="ingredient_wastage"
            value={values?.ingredient_wastage}
            label="Wastage"
            placeholder="wastage in %"
            onBlur={handleBlur}
            onChange={(e) => {
              handleChange(e);
            }}
            onInput={(e) => allowedOnlyNumbers(e)}
            disabled={isDefault}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Typography className="title-text">%</Typography>
                </InputAdornment>
              ),
            }}
          />
        </Box>
      </Box>
    </>
  );
};

export default BasicDetails;
