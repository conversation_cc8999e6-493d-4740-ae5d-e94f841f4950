// Header section styles
.recipe-category-filter-wrap {
    padding: var(--spacing-lg) var(--spacing-xxl);
    background-color: var(--color-white);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    border-bottom: 1px solid var(--color-light-gray);

    @media(max-width: 1500px) {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    @media(max-width: 767px) {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start !important;

        .d-flex {
            flex-direction: column;
            width: 100%;
        }
    }
}

.support-ticket-wrap {
    padding: var(--spacing-lg) var(--spacing-xxl);
    background-color: var(--color-white);
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    margin-bottom: var(--spacing-lg);

    @media(max-width: 1500px) {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    @media(max-width: 767px) {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .new-ticket-wrap {
        color: var(--color-primary);
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-semibold);
    }

    .select-field-wrap {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: var(--spacing-lg);
        row-gap: var(--spacing-lg);

        @media(max-width: 767px) {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .slected-wrap {
        width: 100%;
    }

    .text-input-wrap {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: var(--spacing-lg);
        row-gap: var(--spacing-lg);

        @media(max-width: 992px) {
            grid-template-columns: repeat(2, 1fr);
            row-gap: var(--spacing-2xl);
        }

        @media(max-width: 767px) {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .ticket-form-grid-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: var(--spacing-lg);
        row-gap: var(--spacing-lg);
        padding-top: var(--spacing-lg);

        @media(max-width: 1500px) {
            grid-template-columns: repeat(1, 1fr);
        }

        @media(max-width: 767px) {
            grid-template-columns: repeat(1, 1fr);
        }

        &.mobile-layout {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .MuiInputBase-root {
        min-height: 37px !important;

        .MuiInputBase-input {
            padding: var(--field-padding) !important;
        }
    }

    .discription-text {
        margin-bottom: var(--spacing-xxs) !important;
        font-family: var(--font-family-primary);
        color: var(--text-color-primary);
        font-weight: var(--font-weight-medium);
    }

    .upload-sec {
        width: 100%;
        border: var(--normal-sec-border);
        height: 110px;
        border-radius: var(--border-radius-lg);
        display: flex;
        justify-content: center;
        align-items: center;

        .upload-area {
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .upload-text {
                color: var(--color-primary);
                padding: var(--spacing-md);
                font-family: var(--font-family-primary);
            }
        }

        svg {
            width: var(--spacing-2xl);
            height: var(--spacing-2xl);
            fill: var(--color-primary) !important;
        }

        .upload-text {
            color: var(--color-primary);
        }
    }

    .media-previews {
        margin-top: var(--spacing-lg);

        .preview-container {
            display: inline-block;
            margin-right: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            position: relative;
            border: var(--normal-sec-border);

            .close-icon-wrap {
                position: absolute;
                line-height: 0px;
                top: 0px;
                right: 0px;
            }

            .image-container,
            .video-container {
                box-shadow: var(--box-shadow-xs);
                line-height: 0px;
            }

            .preview-img {
                width: 100px;
                height: 100px;
                object-fit: cover;
                cursor: pointer;
                border-radius: var(--border-radius-md);

                &:hover {
                    opacity: var(--opacity-8);
                }
            }
        }
    }

    .form-actions-btn {
        display: flex;
        gap: var(--spacing-md);
        padding-top: var(--spacing-lg);

        @media(max-width: 1500px) {
            flex-direction: column;
        }

        @media(max-width: 575px){
            justify-content: center !important;
        }
    }

    .create-ticket-wrap {

        .create-ticket-btn,
        .cancel-ticket-btn {
            padding: var(--spacing-xsm) var(--spacing-xl) !important;
            font-size: var(--font-size-base) !important;
            font-weight: var(--font-weight-medium);
            font-family: var(--font-family-primary);
        }

        .create-ticket-btn {
            &:hover {
                color: var(--color-white) !important;
                box-shadow: none !important;
            }
        }

        .cancel-ticket-btn {
            background-color: var(--color-secondary) !important;
            color: var(--color-primary) !important;

            &:hover {
                box-shadow: none !important;
            }
        }

        @media(max-width: 575px){
            justify-content: center !important;
        }
    }

    .error {
        color: var(--color-danger) !important;
        font-family: var(--font-family-primary) !important;
        font-size: var(--font-size-xs) !important;
        line-height: var(--line-height-base) !important;
        font-weight: var(--font-weight-semibold) !important;

        fieldset {
            border: 1px solid var(--color-danger) !important;
        }
    }
}