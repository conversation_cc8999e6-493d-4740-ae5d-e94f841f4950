import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import './ContactCard.scss';

const CustomCtaLink = ({ ctaLink, handleCustomCtaClick }) => (
  <div className="contact-card">
    <div className="contact-card__header">
      <p className="contact-card__title">
        <Icon name="MessageSquare" size={20} color="currentColor" />
        <span>Get in Touch</span>
      </p>
    </div>
    <div className="contact-card__content">
      <div className="contact-card__cta">
        <p className="contact-card__cta-text">
          Ready to try this recipe? Contact us for more information or to place
          an order.
        </p>
        <div className="message-button-wrap send-message-btn-wrap">
          <CustomButton
            className="message-button send-message-btn"
            variant="contained"
            title={ctaLink.text || 'Send us a message'}
            leftIcon={<Icon name="Send" size={16} />}
            onClick={handleCustomCtaClick}
            fullWidth
          />
        </div>
      </div>
    </div>
  </div>
);

export default CustomCtaLink;
