
.ticket-wrap {
    width: 100%;
    max-width: var(--search-max-width, 300px);
    box-shadow: 0px 0px 2px var(--color-dark-50);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    position: relative;

    .id-name-wrap {
        gap: var(--spacing-lg);
        cursor: pointer;
        padding: var(--spacing-md) 0px;

        .id-wrap {
            font-size: var(--font-size-sm);
            color: var(--color-dark-50);
            border-bottom: 1px dashed var(--color-light-grayish-blue);
            line-height: var(--line-height-base);
            font-family: var(--font-family-primary) !important;

            &:hover {
                border-bottom: 1px dashed var(--color-black);
                color: var(--color-black);
            }
        }
    }

    .heading-text-wrap {
        font-family: var(--font-family-primary) !important;
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
    }

    .name-wrap {
        font-family: var(--font-family-primary) !important;
        color: var(--color-dark-50);
        max-width: 155px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        &:hover {
            color: var(--color-blue);
        }
    }

    .time-wrap {
        color: var(--color-dark-50);

        .timer-icon {
            height: var(--font-size-sm);
            width: var(--font-size-sm);
            fill: var(--color-primary);
        }

        .time-text {
            font-family: var(--font-family-primary) !important;
        }
    }

    .profile-wrap {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-lg);
        line-height: 0px;

        .profile-image {
            height: 35px;
            width: 35px;
            border-radius: var(--border-radius-full);
        }
    }

    .ticket-status-select {
        padding-top: var(--spacing-md);

        .MuiFormControl-root {
            width: auto;

            .MuiSvgIcon-root {
                height: var(--icon-size-sm);
                width: var(--icon-size-sm);
                margin-top: var(--spacing-xs);
            }
        }

        .MuiInputBase-root {
            font-family: var(--font-family-primary) !important;
        }

        .MuiSelect-select {
            padding: var(--spacing-tiny) 0px 0px var(--spacing-md);
            font-size: var(--font-size-sm) !important;
        }

        fieldset {
            height: 25px !important;
            border-radius: var(--border-radius-xs);
        }
    }
}