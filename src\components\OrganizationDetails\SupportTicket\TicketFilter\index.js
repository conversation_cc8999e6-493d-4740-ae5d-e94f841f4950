import React from 'react';
import { Box, Button } from '@mui/material';
import { Formik, Form } from 'formik';
import CustomSelect from '@/components/UI/CustomSelect';
import './ticketfilter.scss';
import CustomButton from '@/components/UI/button';
export default function TicketFilter() {
  const priorityOptions = [
    { value: 'none', label: 'None' },
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
  ];

  const AssigneeOptions = [
    { label: '<PERSON>', value: 'john' },
    { label: '<PERSON>', value: 'jane' },
    { label: 'Doe <PERSON>', value: 'doe' },
  ];

  const DateOptions = [
    { label: 'Today', value: 'today' },
    { label: 'This Week', value: 'this-week' },
    { label: 'This Month', value: 'this-month' },
  ];

  const OwnerOptions = [
    { label: 'Alice', value: 'alice' },
    { label: 'Bob', value: 'bob' },
    { label: 'Charlie', value: 'charlie' },
  ];

  const initialValues = {
    priority: '',
    assignee: '',
    date: '',
    owner: '',
  };

  const handleSubmit = (values) => {
    console.log('Filters applied:', values);
  };

  return (
    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
      {({ values, setFieldValue, resetForm }) => (
        <Form className="ticket-filter-wrap">
          <Box>
            {/* Priority */}
            <Box className="pt32">
              <CustomSelect
                label="Priority"
                name="priority"
                placeholder="Select priority"
                options={priorityOptions}
                value={
                  priorityOptions?.find(
                    (opt) => opt?.value === values?.priority
                  ) || ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('priority', selectedOption?.value || '')
                }
              />
            </Box>

            {/* Assignee */}
            <Box className="pt32">
              <CustomSelect
                label="Assignee"
                name="assignee"
                placeholder="Select Assignee"
                options={AssigneeOptions}
                value={
                  AssigneeOptions?.find(
                    (opt) => opt?.value === values?.assignee
                  ) || ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('assignee', selectedOption?.value || '')
                }
              />
            </Box>

            {/* Date */}
            <Box className="pt32">
              <CustomSelect
                label="Date"
                name="date"
                placeholder="Select Date"
                options={DateOptions}
                value={
                  DateOptions?.find((opt) => opt?.value === values?.date) || ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('date', selectedOption?.value || '')
                }
              />
            </Box>

            {/* Owner */}
            <Box className="pt32">
              <CustomSelect
                label="Owner"
                name="owner"
                placeholder="Select Owner"
                options={OwnerOptions}
                value={
                  OwnerOptions?.find((opt) => opt?.value === values?.owner) ||
                  ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('owner', selectedOption?.value || '')
                }
              />
            </Box>

            {/* Buttons */}
            <Box className="ticket-filter-btns pt32">
              <Box className="btns-wrap d-flex gap-5 justify-end">
                <CustomButton
                  variant="contained"
                  background="#39596e"
                  className="apply-btn-wrap"
                  // onClick={handleApplyFilter}
                  title="Apply"
                />
                <CustomButton
                  variant="contained"
                  background="#FFFFFF"
                  className="cancel-btn-wrap"
                  // onClick={handleCancelFilter}
                  title="Clear"
                />
              </Box>
            </Box>
          </Box>
        </Form>
      )}
    </Formik>
  );
}
