'use client';
import React, { useState, useEffect } from 'react';
import SideMenuList from '@/components/UI/SideMenuList';
import { recipeMenuList } from '@/helper/common/commonMenus';
import { Divider, Typography } from '@mui/material';
import { useRouter, usePathname } from 'next/navigation';

export default function RecipeSidemenu() {
  const router = useRouter();
  const pathname = usePathname();
  const [activeMenuItem, setActiveMenuItem] = useState(1);

  useEffect(() => {
    // Find the menu item that matches the current pathname
    const currentMenuItem = recipeMenuList.find(
      (item) => pathname === item.slug
    );
    if (currentMenuItem) {
      setActiveMenuItem(currentMenuItem.id);
    }
  }, [pathname]);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
    const menuItem = recipeMenuList?.find(
      (menuItem) => menuItem?.id === item?.id
    );

    if (menuItem) {
      router.push(`${menuItem?.slug}`);
    }
  };
  return (
    <>
      <Typography className="sub-header-text section-left-title">
        Recipe
      </Typography>
      <Divider />
      <SideMenuList
        menuItem={recipeMenuList}
        activeId={activeMenuItem}
        onSelect={handleActiveMenuItem}
      />
    </>
  );
}
