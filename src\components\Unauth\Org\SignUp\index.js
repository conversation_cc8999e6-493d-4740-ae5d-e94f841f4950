'use client';
import React, { useState } from 'react';
import {
  Box,
  InputAdornment,
  IconButton,
  Typography,
  TextField,
} from '@mui/material';
import { Formik, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/button';
import {
  Buildingicon,
  EmailIcon,
  EyeCloseIcon,
  EyeShowIcon,
  LeftSignInVector,
  LocationIcon,
  PhoneIcon,
  ProfileRoundIcon,
  RightSignInVector,
  WebsiteIcon,
} from '@/helper/common/images';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { INFO_LINKS, ORG_URLS } from '@/helper/constants/urls';
import {
  setApiMessage,
  TextFieldMaxLength,
} from '@/helper/common/commonFunctions';
import PhoneNumberWithCountryCode from '@/components/UI/CustomCountryCodeField';
import LightDarkSwitch from '@/components/UI/Switch';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import './registration.scss';

export default function Registration() {
  const [showPassword, setShowPassword] = useState(false);
  const [showVerifyPassword, setShowVerifyPassword] = useState(false);
  const [passwordTouched, setPasswordTouched] = useState(false);
  // const [loader, setLoader] = useState(false);
  const [selectedCountryId, setSelectedCountryId] = useState(null);
  const [isOn, setIsOn] = useState(false);
  const urlPattern = /^(ftp|http|https):\/\/[^ "]+$/;
  const [validationFeedback, setValidationFeedback] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    specialChar: false,
  });

  const router = useRouter();

  const validatePassword = (pwd) => {
    const feedback = {
      length: pwd.length >= 8,
      lowercase: /[a-z]/.test(pwd),
      uppercase: /[A-Z]/.test(pwd),
      number: /\d/.test(pwd),
      specialChar: /[@$!%*?&]/.test(pwd),
    };

    setValidationFeedback(feedback);
    return feedback;
  };

  const handleSwitchChange = (event) => {
    setIsOn(event.target.checked);
  };

  const handleNavigate = () => {
    router.push('/org/login');
  };

  // function removePlusSign(input) {
  //   return input.replaceAll('+', '');
  // }

  return (
    <Box className="registration-page-container">
      <Box className="registration-page-wrap">
        <Box className="registration-page">
          <Formik
            initialValues={{
              first_name: '',
              last_name: '',
              user_name: '',
              country_code: selectedCountryId,
              phone_number: '',
              email: '',
              name: '',
              website: '',
              password: '',
              confirm_password: '',
              locations: '',
              // terms_condition: false,
            }}
            validationSchema={Yup.object().shape({
              first_name: Yup.string().required('First name is required'),
              last_name: Yup.string().required('Last name is required'),
              user_name: Yup.string()
                .required('This field is required')
                .matches(
                  /^[a-zA-Z0-9._ ]*$/,
                  'Only letters, numbers, underscores, dots, and spaces are allowed'
                )
                .min(3, 'Username must be at least 3 characters'),
              country_code: Yup.string().required('Country code is required'),

              phone_number: Yup.string()
                .trim()
                .required('Phone number is required')
                .matches(/^[0-9]{10,11}$/, 'Please enter valid phone number'),
              email: Yup.string()
                .required('Email is required')
                .email('Invalid email address'),
              name: Yup.string()
                .required('Organization name is required')
                .matches(
                  /^[a-zA-Z0-9 ]*$/,
                  'Only letters, numbers, and spaces are allowed'
                )
                .min(3, 'Organization name must be at least 3 characters'),
              website: Yup.string()
                .matches(urlPattern, 'Invalid URL')
                .required('Website is required'),
              password: Yup.string()
                .required('Password is required')
                .min(8, 'Password must be at least 8 characters')
                .matches(
                  /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*?&]+$/,
                  'Password must include one uppercase, one lowercase, one number, and one special character'
                ),
              confirm_password: Yup.string()
                .required('Re-Enter your password')
                .oneOf([Yup.ref('password'), null], 'Passwords must match'),
              locations: Yup.string()
                .matches(/^\d+$/, 'Location must be a number')
                .required('location is required'),
            })}
            onSubmit={async (requestData) => {
              let sendData = {
                user: {
                  firstName: requestData?.first_name,
                  lastName: requestData?.last_name,
                  userPhoneNumber: requestData?.phone_number,
                  userCountryCode: selectedCountryId,
                  username: requestData?.user_name,
                  password: requestData?.password,
                  email: requestData?.email,
                },
                organization: {
                  name: requestData?.name,
                  website: requestData?.website, //domain
                  // domain: requestData?.website.replace(/^https?:\/\//, ''),
                  multiple_location: requestData?.locations
                    ? requestData?.locations
                    : 0,
                },
              };
              try {
                // setLoader(true);
                const { status, data } = await axiosInstance.post(
                  ORG_URLS?.ORG_REGISTER,
                  sendData
                );

                if (status === 200 || status === 201) {
                  setApiMessage('success', data?.message);
                  // router.push(
                  //   `/email-verification?data=${data?.data?.id}&email=${data?.data?.email}`
                  // );
                  router.push(
                    `/org/create-account?data=${data?.data?.id}&email=${data?.data?.email}`
                  );
                  // router.push(`/org/create-account?email=${data?.data?.email}`);
                }
                // setLoader(false);
              } catch (error) {
                setApiMessage('error', error?.response?.data?.message);
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              setFieldValue,
            }) => (
              <Form onSubmit={handleSubmit} className="form-wrap">
                <Typography
                  className="registration-wrap heading-text"
                  variant="h3"
                >
                  Create a New Account
                </Typography>

                <Box className="input-field-wrap">
                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      First Name
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{ shrink: true }}
                      id="first_name"
                      name="first_name"
                      className="w100"
                      variant="standard"
                      placeholder="First name"
                      value={values.first_name}
                      error={Boolean(touched.first_name && errors.first_name)}
                      helperText={touched.first_name && errors.first_name}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{ maxLength: TextFieldMaxLength }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton>
                              <Box className="eye-wrap">
                                <ProfileRoundIcon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className=" input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Last Name
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{ shrink: true }}
                      id="last_name"
                      name="last_name"
                      placeholder="Last name"
                      value={values.last_name}
                      error={Boolean(touched.last_name && errors.last_name)}
                      helperText={touched.last_name && errors.last_name}
                      className="w100"
                      variant="standard"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{ maxLength: TextFieldMaxLength }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton>
                              <Box className="eye-wrap">
                                <ProfileRoundIcon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className=" input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Username
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{ shrink: true }}
                      id="user_name"
                      name="user_name"
                      placeholder="Username"
                      value={values.user_name}
                      error={Boolean(touched?.user_name && errors?.user_name)}
                      helperText={touched?.user_name && errors?.user_name}
                      className="w100"
                      variant="standard"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{ maxLength: TextFieldMaxLength }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton>
                              <Box className="eye-wrap">
                                <ProfileRoundIcon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className="input-wrap">
                    <Typography className="un-auth-label-wrap" variant="h6">
                      Mobile Number
                      <span className="primary-color">*</span>
                    </Typography>
                    <Box
                      className={`d-flex align-center phone-input-wrap ${
                        touched.phone_number && errors.phone_number
                          ? 'error'
                          : ''
                      }`}
                    >
                      <PhoneNumberWithCountryCode
                        label=""
                        placeholder="Phone number"
                        onBlur={handleBlur}
                        onChange={handleChange}
                        fullWidth
                        values={values}
                        setFieldValue={setFieldValue}
                        handleBlur={handleBlur}
                        handleChange={handleChange}
                        selectedCountryId={selectedCountryId}
                        setSelectedCountryId={setSelectedCountryId}
                      />
                      <Box
                        className={`${
                          touched.phone_number && errors.phone_number
                            ? 'phone-icon-wrap'
                            : 'phone-icon'
                        }`}
                      >
                        <PhoneIcon />
                      </Box>
                    </Box>
                    {touched.phone_number && errors.phone_number && (
                      <Typography
                        className="error-message"
                        variant="body2"
                        color="error"
                      >
                        {errors.phone_number}
                      </Typography>
                    )}
                  </Box>

                  <Box className=" input-wrap">
                    <Typography className="un-auth-label-wrap" variant="h6">
                      Email Address
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{ shrink: true }}
                      id="email"
                      name="email"
                      placeholder="Email address"
                      value={values.email}
                      error={Boolean(touched.email && errors.email)}
                      helperText={touched.email && errors.email}
                      className="w100"
                      variant="standard"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton>
                              <Box className="eye-wrap">
                                <EmailIcon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className=" input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Organization Name
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      id="name"
                      name="name"
                      placeholder="Organization name"
                      value={values?.name}
                      error={Boolean(touched?.name && errors?.name)}
                      helperText={touched?.name && errors?.name}
                      className="w100"
                      variant="standard"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{ maxLength: TextFieldMaxLength }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton>
                              <Box className="eye-wrap">
                                <Buildingicon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className="location-input-wrap input-wrap  pb8">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Number Of Location
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{ shrink: true }}
                      id="locations"
                      name="locations"
                      placeholder="Number of location"
                      value={values?.locations}
                      error={Boolean(touched?.locations && errors?.locations)}
                      helperText={touched?.locations && errors?.locations}
                      className="w100"
                      variant="standard"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
                      }}
                      fullWidth
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton>
                              <Box className="eye-wrap">
                                <LocationIcon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className=" input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Website
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      id="website"
                      name="website"
                      placeholder="Website URL"
                      value={values?.website}
                      error={Boolean(touched?.website && errors?.website)}
                      helperText={touched?.website && errors?.website}
                      className="w100"
                      variant="standard"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton>
                              <Box className="eye-wrap">
                                <WebsiteIcon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className=" input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Password
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{ shrink: true }}
                      id="password"
                      name="password"
                      placeholder="Password"
                      type={showPassword ? 'text' : 'password'}
                      value={values.password}
                      error={Boolean(touched.password && errors.password)}
                      helperText={touched.password && errors.password}
                      variant="standard"
                      className={
                        touched.password && errors.password
                          ? 'w100 password-textfield password-error'
                          : 'w100 password-textfield'
                      }
                      onBlur={handleBlur}
                      onChange={(e) => {
                        handleChange(e);
                        validatePassword(e.target.value);
                        setPasswordTouched(true);
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end" className="eye-icon">
                            <IconButton>
                              <Box
                                className="eye-wrap"
                                onClick={() => setShowPassword(!showPassword)}
                              >
                                {!showPassword ? (
                                  <EyeShowIcon className="eye-icon-wrap" />
                                ) : (
                                  <EyeCloseIcon className="eye-icon-wrap" />
                                )}
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Re-Enter Password
                      <span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{ shrink: true }}
                      id="confirm_password"
                      name="confirm_password"
                      placeholder="Re-enter password"
                      type={showVerifyPassword ? 'text' : 'password'}
                      value={values.confirm_password}
                      variant="standard"
                      error={Boolean(
                        touched.confirm_password && errors.confirm_password
                      )}
                      helperText={
                        touched.confirm_password && errors.confirm_password
                      }
                      className={
                        touched.confirm_password && errors.confirm_password
                          ? 'w100 password-textfield password-error'
                          : 'w100 password-textfield'
                      }
                      onBlur={handleBlur}
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end" className="eye-icon">
                            <IconButton>
                              <Box
                                className="eye-wrap"
                                onClick={() =>
                                  setShowVerifyPassword(!showVerifyPassword)
                                }
                              >
                                {!showVerifyPassword ? (
                                  <EyeShowIcon
                                    className="eye-icon-wrap"
                                    sx={{ cursor: 'pointer' }}
                                  />
                                ) : (
                                  <EyeCloseIcon
                                    className="eye-icon-wrap"
                                    sx={{ cursor: 'pointer' }}
                                  />
                                )}
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </Box>

                <Box className="terms-condition-wrap">
                  <Box className="un-auth-label-wrap d-flex align-center">
                    <Box className="switch-wrap">
                      <LightDarkSwitch
                        checked={isOn}
                        onChange={handleSwitchChange}
                      />
                    </Box>
                    <Typography
                      className="terms-condition sub-heading-text"
                      component="p"
                    >
                      Agree with{' '}
                      <span className="text-wrap">
                        <Link
                          href={INFO_LINKS?.TermsConditions}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-link"
                        >
                          Terms & Condition
                        </Link>
                      </span>{' '}
                      and{' '}
                      <span className="text-wrap">
                        <Link
                          href={INFO_LINKS?.PrivacyPolicy}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-link"
                        >
                          Privacy Policy
                        </Link>
                      </span>
                    </Typography>
                  </Box>
                  <ErrorMessage
                    name="terms_condition"
                    component="div"
                    className="error"
                  />
                </Box>
                {touched.password && errors.password && isOn && (
                  <Box className="">
                    <Box className="password-feedback-container">
                      {passwordTouched && (
                        <Box className="pt8 pb8">
                          <Typography
                            className="p12"
                            style={{
                              color: validationFeedback.length
                                ? 'green'
                                : '#d32f2f',
                            }}
                          >
                            {validationFeedback.length ? '✔' : '✖'} At least 8
                            characters long
                          </Typography>
                          <Typography
                            className="p12"
                            style={{
                              color: validationFeedback.lowercase
                                ? 'green'
                                : '#d32f2f',
                            }}
                          >
                            {validationFeedback.lowercase ? '✔' : '✖'} At
                            least one lowercase character
                          </Typography>
                          <Typography
                            className="p12"
                            style={{
                              color: validationFeedback.uppercase
                                ? 'green'
                                : '#d32f2f',
                            }}
                          >
                            {validationFeedback.uppercase ? '✔' : '✖'} At
                            least one uppercase character
                          </Typography>
                          <Typography
                            className="p12"
                            style={{
                              color: validationFeedback.number
                                ? 'green'
                                : '#d32f2f',
                            }}
                          >
                            {validationFeedback.number ? '✔' : '✖'} At least
                            one number
                          </Typography>
                          <Typography
                            className="p12"
                            style={{
                              color: validationFeedback.specialChar
                                ? 'green'
                                : '#d32f2f',
                            }}
                          >
                            {validationFeedback.specialChar ? '✔' : '✖'} At
                            least one special character
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                )}

                <Box className="registration-btn-wrap" textAlign="center">
                  <CustomButton
                    fullWidth
                    className="sign-up-btn"
                    type="submit"
                    variant="contained"
                    title="Sign Up"
                    disabled={!isOn}
                  />
                </Box>
                <Typography className="already-have-text" component="p">
                  Already have an account?
                  <span className="login-wrap" onClick={() => handleNavigate()}>
                    Log In
                  </span>
                </Typography>
              </Form>
            )}
          </Formik>
        </Box>
      </Box>
      <Box className="left-vector">
        <LeftSignInVector className="sign-up-left-vector" />
      </Box>
      <Box className="right-vector">
        <RightSignInVector />
      </Box>
    </Box>
  );
}
