import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Line, <PERSON>Chart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import Icon from 'components/AppIcon';

const ChartWidget = ({ id, title, type, data, dateRange, filters }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartOptions, setChartOptions] = useState({
    showGrid: true,
    showTooltip: true,
    animate: true
  });

  const colors = {
    primary: '#2D5A3D',
    secondary: '#8B4513',
    accent: '#E67E22',
    success: '#27AE60',
    warning: '#F39C12',
    error: '#E74C3C'
  };

  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data}>
        {chartOptions.showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
        <XAxis 
          dataKey="name" 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
        />
        <YAxis 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip 
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          />
        )}
        <Line 
          type="monotone" 
          dataKey="views" 
          stroke={colors.primary} 
          strokeWidth={2}
          dot={{ fill: colors.primary, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.primary, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Line 
          type="monotone" 
          dataKey="likes" 
          stroke={colors.accent} 
          strokeWidth={2}
          dot={{ fill: colors.accent, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.accent, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data}>
        {chartOptions.showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
        <XAxis 
          dataKey="name" 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
        />
        <YAxis 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip 
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          />
        )}
        <Bar 
          dataKey="recipes" 
          fill={colors.primary}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Bar 
          dataKey="views" 
          fill={colors.accent}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderHeatmap = () => (
    <div className="grid grid-cols-4 gap-2 h-full">
      {data.map((item, index) => (
        <div
          key={index}
          className="flex flex-col items-center justify-center p-2 rounded-lg transition-smooth hover:scale-105"
          style={{
            backgroundColor: `rgba(45, 90, 61, ${item.value / 100})`,
            color: item.value > 50 ? '#ffffff' : '#2C3E50'
          }}
        >
          <span className="text-xs font-medium">{item.hour}</span>
          <span className="text-xs">{item.day}</span>
          <span className="text-sm font-bold">{item.value}</span>
        </div>
      ))}
    </div>
  );

  const renderFunnelChart = () => (
    <div className="space-y-3 h-full flex flex-col justify-center">
      {data.map((stage, index) => (
        <div key={index} className="relative">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-text-primary">{stage.stage}</span>
            <span className="text-sm text-text-secondary">{stage.percentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="h-3 rounded-full transition-all duration-1000"
              style={{
                width: `${stage.percentage}%`,
                backgroundColor: colors.primary,
                opacity: 1 - (index * 0.15)
              }}
            />
          </div>
          <span className="text-xs text-text-secondary mt-1">{stage.count.toLocaleString()} users</span>
        </div>
      ))}
    </div>
  );

  const renderChart = () => {
    switch (type) {
      case 'line':
        return renderLineChart();
      case 'bar':
        return renderBarChart();
      case 'heatmap':
        return renderHeatmap();
      case 'funnel':
        return renderFunnelChart();
      default:
        return renderLineChart();
    }
  };

  const toggleChartOption = (option) => {
    setChartOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  return (
    <div className={`bg-surface border border-gray-200 rounded-lg shadow-card ${
      isFullscreen ? 'fixed inset-4 z-50' : ''
    }`}>
      {/* Widget Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <div>
          <h3 className="font-heading font-semibold text-lg text-text-primary">{title}</h3>
          <p className="text-sm text-text-secondary">
            {dateRange === '7days' ? 'Last 7 days' : 
             dateRange === '30days' ? 'Last 30 days' : 
             dateRange === 'quarter' ? 'Last quarter' : 'Last year'}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Chart Options */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => toggleChartOption('showGrid')}
              className={`p-1 rounded transition-smooth ${
                chartOptions.showGrid ? 'text-primary bg-primary-50' : 'text-text-secondary hover:text-primary'
              }`}
              title="Toggle grid"
            >
              <Icon name="Grid3X3" size={16} />
            </button>
            <button
              onClick={() => toggleChartOption('animate')}
              className={`p-1 rounded transition-smooth ${
                chartOptions.animate ? 'text-primary bg-primary-50' : 'text-text-secondary hover:text-primary'
              }`}
              title="Toggle animation"
            >
              <Icon name="Play" size={16} />
            </button>
          </div>
          
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-1 text-text-secondary hover:text-primary transition-smooth"
            title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
          >
            <Icon name={isFullscreen ? "Minimize2" : "Maximize2"} size={16} />
          </button>
        </div>
      </div>

      {/* Chart Content */}
      <div className={`p-4 ${isFullscreen ? 'h-[calc(100vh-8rem)]' : 'h-80'}`}>
        {renderChart()}
      </div>

      {/* Chart Footer */}
      <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            {type === 'line' && (
              <>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                  <span className="text-text-secondary">Views</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: colors.accent }}></div>
                  <span className="text-text-secondary">Likes</span>
                </div>
              </>
            )}
            {type === 'bar' && (
              <>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                  <span className="text-text-secondary">Recipes</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: colors.accent }}></div>
                  <span className="text-text-secondary">Views</span>
                </div>
              </>
            )}
          </div>
          
          <button className="flex items-center space-x-1 text-primary hover:text-primary-600 transition-smooth">
            <Icon name="Download" size={14} />
            <span>Export</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChartWidget;