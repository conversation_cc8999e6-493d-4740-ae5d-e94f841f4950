import React, { useState } from 'react';
import { <PERSON>Chart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import Icon from '../../../components/AppIcon';
import styles from './ChartWidget.module.scss';

const ChartWidget = ({ id, title, type, data, dateRange, filters }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartOptions, setChartOptions] = useState({
    showGrid: true,
    showTooltip: true,
    animate: true
  });

  const colors = {
    primary: '#2D5A3D',
    secondary: '#8B4513',
    accent: '#E67E22',
    success: '#27AE60',
    warning: '#F39C12',
    error: '#E74C3C'
  };

  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data}>
        {chartOptions.showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
        <XAxis 
          dataKey="name" 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
        />
        <YAxis 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip 
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          />
        )}
        <Line 
          type="monotone" 
          dataKey="views" 
          stroke={colors.primary} 
          strokeWidth={2}
          dot={{ fill: colors.primary, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.primary, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Line 
          type="monotone" 
          dataKey="likes" 
          stroke={colors.accent} 
          strokeWidth={2}
          dot={{ fill: colors.accent, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: colors.accent, strokeWidth: 2 }}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data}>
        {chartOptions.showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
        <XAxis 
          dataKey="name" 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
        />
        <YAxis 
          stroke="#7F8C8D"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        {chartOptions.showTooltip && (
          <Tooltip 
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          />
        )}
        <Bar 
          dataKey="recipes" 
          fill={colors.primary}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
        <Bar 
          dataKey="views" 
          fill={colors.accent}
          radius={[4, 4, 0, 0]}
          animationDuration={chartOptions.animate ? 1000 : 0}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderHeatmap = () => (
    <div className={styles.heatmapGrid}>
      {data.map((item, index) => (
        <div
          key={index}
          className={styles.heatmapItem}
          style={{
            backgroundColor: `rgba(45, 90, 61, ${item.value / 100})`,
            color: item.value > 50 ? '#ffffff' : '#2C3E50'
          }}
        >
          <span className={styles.heatmapHour}>{item.hour}</span>
          <span className={styles.heatmapDay}>{item.day}</span>
          <span className={styles.heatmapValue}>{item.value}</span>
        </div>
      ))}
    </div>
  );

  const renderFunnelChart = () => (
    <div className={styles.funnelContainer}>
      {data.map((stage, index) => (
        <div key={index} className={styles.funnelStage}>
          <div className={styles.funnelStageHeader}>
            <span className={styles.funnelStageTitle}>{stage.stage}</span>
            <span className={styles.funnelStagePercentage}>{stage.percentage}%</span>
          </div>
          <div className={styles.funnelProgressTrack}>
            <div
              className={styles.funnelProgressBar}
              style={{
                width: `${stage.percentage}%`,
                backgroundColor: colors.primary,
                opacity: 1 - (index * 0.15)
              }}
            />
          </div>
          <span className={styles.funnelStageCount}>{stage.count.toLocaleString()} users</span>
        </div>
      ))}
    </div>
  );

  const renderChart = () => {
    switch (type) {
      case 'line':
        return renderLineChart();
      case 'bar':
        return renderBarChart();
      case 'heatmap':
        return renderHeatmap();
      case 'funnel':
        return renderFunnelChart();
      default:
        return renderLineChart();
    }
  };

  const toggleChartOption = (option) => {
    setChartOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  return (
    <div className={`${styles.chartWidget} ${isFullscreen ? styles.fullscreen : ''}`}>
      {/* Widget Header */}
      <div className={styles.widgetHeader}>
        <div className={styles.headerInfo}>
          <h3 className={styles.widgetTitle}>{title}</h3>
          <p className={styles.widgetSubtitle}>
            {dateRange === '7days' ? 'Last 7 days' :
             dateRange === '30days' ? 'Last 30 days' :
             dateRange === 'quarter' ? 'Last quarter' : 'Last year'}
          </p>
        </div>

        <div className={styles.headerControls}>
          {/* Chart Options */}
          <div className={styles.chartOptions}>
            <button
              onClick={() => toggleChartOption('showGrid')}
              className={`${styles.optionButton} ${
                chartOptions.showGrid ? styles.optionButtonActive : ''
              }`}
              title="Toggle grid"
            >
              <Icon name="Grid3X3" size={16} />
            </button>
            <button
              onClick={() => toggleChartOption('animate')}
              className={`${styles.optionButton} ${
                chartOptions.animate ? styles.optionButtonActive : ''
              }`}
              title="Toggle animation"
            >
              <Icon name="Play" size={16} />
            </button>
          </div>

          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className={styles.fullscreenButton}
            title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
          >
            <Icon name={isFullscreen ? "Minimize2" : "Maximize2"} size={16} />
          </button>
        </div>
      </div>

      {/* Chart Content */}
      <div className={`${styles.chartContent} ${isFullscreen ? styles.chartContentFullscreen : ''}`}>
        {renderChart()}
      </div>

      {/* Chart Footer */}
      <div className={styles.chartFooter}>
        <div className={styles.footerContent}>
          <div className={styles.legendContainer}>
            {type === 'line' && (
              <>
                <div className={styles.legendItem}>
                  <div className={styles.legendDot} style={{ backgroundColor: colors.primary }}></div>
                  <span className={styles.legendLabel}>Views</span>
                </div>
                <div className={styles.legendItem}>
                  <div className={styles.legendDot} style={{ backgroundColor: colors.accent }}></div>
                  <span className={styles.legendLabel}>Likes</span>
                </div>
              </>
            )}
            {type === 'bar' && (
              <>
                <div className={styles.legendItem}>
                  <div className={styles.legendDot} style={{ backgroundColor: colors.primary }}></div>
                  <span className={styles.legendLabel}>Recipes</span>
                </div>
                <div className={styles.legendItem}>
                  <div className={styles.legendDot} style={{ backgroundColor: colors.accent }}></div>
                  <span className={styles.legendLabel}>Views</span>
                </div>
              </>
            )}
          </div>

          <button className={styles.exportButton}>
            <Icon name="Download" size={14} />
            <span>Export</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChartWidget;