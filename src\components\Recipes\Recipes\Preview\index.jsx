'use client';
import React, { useRef, useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import PreviewModeSelector from './components/PreviewModeSelector';
import RecipeHeader from './components/RecipeHeader';
import IngredientsCard from './components/IngredientsCard';
import CostAnalysisCard from './components/CostAnalysisCard';
import InstructionsCard from './components/InstructionsCard';
import ChefTipsGrid from './components/ChefTipsGrid';
import ServingInfoCard from './components/ServingInfoCard';
import ContactCard from './components/ContactCard';
import { useReactToPrint } from 'react-to-print';
import {
  getRecipePreviewData,
  incrementRecipeImpression,
} from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import './preview.scss';

const RecipePreviewView = ({ slug }) => {
  const printRef = useRef();
  const pathname = usePathname();
  const router = useRouter();
  const [recipeData, setRecipeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to determine if this is a public page
  const isPublicPage = pathname?.includes('/recipe/recipe-preview/');
  useEffect(() => {
    const fetchRecipeData = async () => {
      if (!slug) return;

      try {
        setIsLoading(true);
        // Use the service function that handles public/private route logic
        const data = await getRecipePreviewData(slug, pathname);
        setRecipeData(data);

        // Increment impression count for public recipe views
        if (isPublicPage && data?.id) {
          await incrementRecipeImpression(data?.id);
        }
      } catch (error) {
        setApiMessage('error', error?.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecipeData();
  }, [slug, pathname, isPublicPage]);

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'Recipe Preview',
    removeAfterPrint: true,
  });

  const handleExport = () => {
    // Mock export functionality
    // Export logic would go here
  };

  const handleBack = () => {
    // Determine proper back navigation based on current route
    if (isPublicPage) {
      // For public pages, go to public recipes list or home
      router.push('/');
    } else {
      // For private pages, go to recipes list
      router.push('/recipes');
    }
  };

  if (isLoading) {
    return <ContentLoader />;
  }

  if (!recipeData) {
    return (
      <NoDataView
        title="Recipe Not Found"
        description="The requested recipe could not be found. Please check the URL and try again."
      />
    );
  }

  return (
    <div className="recipe-preview">
      {/* Header Controls */}
      <div className="recipe-preview__header">
        <div className="recipe-preview__header-content">
          <div className="recipe-preview__header-left">
            <CustomButton
              variant="outlined"
              title="Back"
              leftIcon={<Icon name="ArrowLeft" size={16} />}
              onClick={handleBack}
              className="recipe-preview__action-btn recipe-preview__back-btn"
            />
            <PreviewModeSelector />
          </div>

          <div className="recipe-preview__header-actions">
            <CustomButton
              variant="outlined"
              title="Print"
              leftIcon={<Icon name="Printer" size={16} />}
              onClick={handlePrint}
              className="recipe-preview__action-btn"
            />

            <CustomButton
              variant="contained"
              title="Export"
              leftIcon={<Icon name="Download" size={16} />}
              onClick={handleExport}
              className="recipe-preview__action-btn"
            />
          </div>
        </div>
      </div>
      <div ref={printRef}>
        <div className="recipe-preview__container">
          <RecipeHeader recipeData={recipeData} isPublicPage={isPublicPage} />

          <div className="recipe-preview__content-grid">
            {/* Right Column - Instructions & Chef Notes (now positioned left) */}
            <div className="recipe-preview__right-column">
              {/* Show preparation steps only if enabled in settings for public pages, always show for private pages */}
              {(!isPublicPage ||
                recipeData?.organization_settings
                  ?.recipeDetailsToDisplayPublicly?.preparationSteps) && (
                <InstructionsCard instructions={recipeData?.steps} />
              )}

              <ChefTipsGrid recipeData={recipeData} />

              {/* Show serving info based on multiple settings for public pages, always show for private pages */}
              {(!isPublicPage ||
                recipeData?.organization_settings
                  ?.recipeDetailsToDisplayPublicly?.yieldPortioning ||
                recipeData?.organization_settings
                  ?.recipeDetailsToDisplayPublicly?.serveIn ||
                recipeData?.organization_settings
                  ?.recipeDetailsToDisplayPublicly?.garnish) && (
                <ServingInfoCard
                  recipeData={recipeData}
                  isPublicPage={isPublicPage}
                />
              )}
            </div>

            {/* Left Column - Ingredients & Cost (now positioned right) */}
            <div className="recipe-preview__left-column">
              {/* Show ingredients only if enabled in settings for public pages, always show for private pages */}
              {(!isPublicPage ||
                recipeData?.organization_settings
                  ?.recipeDetailsToDisplayPublicly?.ingredients) && (
                <IngredientsCard
                  ingredients={recipeData?.ingredients}
                  isPublicPage={isPublicPage}
                />
              )}

              {/* Show contact card only on public pages */}
              {isPublicPage && <ContactCard recipeData={recipeData} />}

              {/* Show cost only if enabled in settings for public pages, always show for private pages */}
              {(!isPublicPage ||
                recipeData?.organization_settings
                  ?.recipeDetailsToDisplayPublicly?.cost) && (
                <CostAnalysisCard recipeData={recipeData} />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipePreviewView;
