.media-icons-carousel-container {
  margin-top: var(--spacing-md);
  max-width: 100%;

  .media-icons-carousel {
    .splide__track {
      padding: var(--spacing-xs) 0;
    }

    // Use default Splide arrow styling (same as SplideThumbnailCarousel)
    .splide__arrow {
      transition: opacity 0.2s ease;
      border: var(--normal-sec-border);
      height: var(--icon-size-md);
      width: var(--icon-size-md);
      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
    svg {
      height: var(--icon-size-xxs-min);
      width: var(--icon-size-xxs-min);
    }
    .splide__arrow--prev {
      left: -15px;
    }

    .splide__arrow--next {
      right: -15px;
    }
  }

  .media-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    width: 30px;
    height: 30px;
    flex-shrink: 0;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    svg {
      width: 18px;
      height: 18px;
    }
  }

  // Icon type specific styles
  .media-icon__audio {
    flex-shrink: 0;
  }

  .media-icon__document {
    flex-shrink: 0;
  }

  .media-icon__link {
    flex-shrink: 0;
  }
}

// Document Preview Modal Styles
.document-preview-modal {
  .MuiDialog-paper {
    max-width: 90vw;
    width: 90vw;
    height: 90vh;
    margin: 5vh auto;
  }

  .document-preview-content {
    width: 100%;
    height: 100%;
    padding: var(--spacing-md);
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    overflow: hidden;

    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }
}
