'use client';
import React, { useState } from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Checkbox,
  FormControlLabel,
  Typography,
  useTheme,
  useMediaQuery,
  Divider,
  Tooltip,
} from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomEditor from '@/components/UI/CustomEditor';
import CustomSelect from '@/components/UI/CustomSelect';
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import FilterListIcon from '@mui/icons-material/FilterList';
import AddIcon from '@mui/icons-material/Add';
import { useDropzone } from 'react-dropzone';
import PreviewModal from './PreviewModal';
import CloseIcon from '@mui/icons-material/Close';
import Image from 'next/image';
import FilterComponent from '@/components/UI/FilterComponent';
import RightDrawer from '@/components/UI/RightDrawer';
import './createticket.scss';

export default function CreateTicket() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const [previewMedia, setPreviewMedia] = useState(null);
  const [mediaFiles, setMediaFiles] = useState([]);
  const [openModal, setOpenModal] = useState(false);

  // Filter related state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState(['search']);
  const [filterData, setFilterData] = useState({
    category: '',
    issueType: '',
    status: '',
  });
  const [searchValue, setSearchValue] = useState('');

  const categoryOptions = [
    { label: 'HRMS', value: 'hrms' },
    { label: 'PMS', value: 'pms' },
    { label: 'Other', value: 'other' },
  ];

  const issueTypeOptions = [
    { label: 'Bug', value: 'bug' },
    { label: 'Feature Request', value: 'feature_request' },
    { label: 'General Query', value: 'general_query' },
  ];

  // Filter configuration
  const filters = [
    {
      key: 'search',
      label: 'Search',
      options: [],
      permission: true,
    },
    {
      key: 'category',
      label: 'Category',
      options: categoryOptions,
      permission: true,
    },
    {
      key: 'issueType',
      label: 'Issue Type',
      options: issueTypeOptions,
      permission: true,
    },
    {
      key: 'status',
      label: 'Status',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Submitted', value: 'submitted' },
        { label: 'In Review', value: 'in_review' },
      ],
      permission: true,
    },
  ];

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length) {
        const newMedia = acceptedFiles.map((file) => ({
          file,
          preview: URL.createObjectURL(file),
          type: file.type.split('/')[0],
        }));
        setMediaFiles((prevFiles) => [...prevFiles, ...newMedia]);
      }
    },
    accept: 'image/*,video/*',
    multiple: true,
  });

  const handlePreviewClick = (preview) => {
    setPreviewMedia(preview);
    setOpenModal(true);
  };
  const removeMedia = (index) => {
    setMediaFiles((prevFiles) => prevFiles?.filter((file, i) => i !== index));
  };

  // Filter functions
  const toggleFilter = (filterKey) => {
    setSelectedFilters((prev) =>
      prev.includes(filterKey)
        ? prev.filter((key) => key !== filterKey)
        : [...prev, filterKey]
    );
  };

  const getFirstFourFilters = () => {
    return selectedFilters.slice(0, 4);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      // Removed saveLayout call
    }
  };

  const handleApplyFilter = () => {
    // Removed saveLayout call
    setOpenFilterDrawer(false);
  };

  const handleClearFilter = () => {
    setFilterData({
      category: '',
      issueType: '',
      status: '',
    });
    setSearchValue('');
    // Removed setFilterDataApplied call
  };

  const handleSubmitForm = () => {
    // Simulate submitting the form (e.g., sending ticket data to API)
    // Implementation goes here
  };

  return (
    <Formik
      initialValues={{
        Subject: '',
        category: '',
        issueType: '',
        description: '',
        Name: '',
        Email: '',
        PhoneNumber: '',
        term_condition: false,
      }}
      validationSchema={Yup.object({
        Subject: Yup.string().required('Subject is required'),
        category: Yup.string().required('Category is required'),
        issueType: Yup.string().required('Issue Type is required'),
        description: Yup.string().required('Description is required'),
        Name: Yup.string().required('Name is required'),
        Email: Yup.string()
          .email('Invalid email')
          .required('Email is required'),
        PhoneNumber: Yup.string().required('Phone Number is required'),
        term_condition: Yup.bool().oneOf([true], 'You must agree to the terms'),
      })}
      onSubmit={(values) => handleSubmitForm(values)}
    >
      {({
        values,
        errors,
        touched,
        handleBlur,
        handleChange,
        setFieldValue,
        handleSubmit,
      }) => (
        <Form onSubmit={handleSubmit}>
          {/* Header with buttons - similar to recipe list */}
          <Box className="recipe-category-filter-wrap">
            <Box className="section-right-title">
              <Typography className="sub-header-text">
                Create New Ticket
              </Typography>
            </Box>
            <Box className="d-flex gap-10 align-center">
              {!isMobile && (
                <CustomButton
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Filter
                        </Typography>
                      }
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                      arrow
                    >
                      <FilterListIcon />
                    </Tooltip>
                  }
                  onClick={() => setOpenFilterDrawer(true)}
                />
              )}
              <CustomButton
                title="Add Ticket"
                startIcon={<AddIcon />}
                type="submit"
              />
            </Box>
          </Box>
          <Divider />

          <Box className="support-ticket-wrap">
            <Typography className="new-ticket-wrap" variant="h5">
              New Ticket
            </Typography>
            <Box className="pt32">
              <CustomTextField
                fullWidth
                name="Subject"
                label="Subject"
                placeholder="Enter subject"
                value={values.Subject}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.Subject && Boolean(errors.Subject)}
                helperText={touched.Subject && errors.Subject}
                required
              />
            </Box>

            <Box
              className={`ticket-form-grid-container ${isMobile ? 'mobile-layout' : ''}`}
            >
              <Box>
                <CustomSelect
                  label="Category"
                  name="category"
                  placeholder="Select Category"
                  options={categoryOptions}
                  value={
                    categoryOptions?.find(
                      (opt) => opt?.value === values?.category
                    ) || ''
                  }
                  onChange={(selectedOption) =>
                    setFieldValue('category', selectedOption?.value || '')
                  }
                  error={touched?.category && errors?.category}
                  helperText={touched?.category && errors?.category}
                  required
                />
              </Box>

              <Box>
                <CustomSelect
                  label="Issue Type"
                  name="issueType"
                  placeholder="Select Issue Type"
                  options={issueTypeOptions}
                  value={
                    issueTypeOptions?.find(
                      (opt) => opt?.value === values?.issueType
                    ) || ''
                  }
                  onChange={(selectedOption) =>
                    setFieldValue('issueType', selectedOption?.value || '')
                  }
                  error={touched?.issueType && errors?.issueType}
                  helperText={touched?.issueType && errors?.issueType}
                  required
                />
              </Box>
            </Box>

            <Box className="pt16">
              <Typography className="Inter12 discription-text" variant="h6">
                Description*
              </Typography>
              <CustomEditor
                content={values.description}
                setContent={(content) => setFieldValue('description', content)}
                height="400px"
              />
              {touched?.description && errors?.description && (
                <div className="error">{errors?.description}</div>
              )}
            </Box>

            <Box className="pt16">
              <Typography className="Inter12 discription-text" variant="h6">
                Attachment
              </Typography>
              <Box className="upload-sec">
                <Box {...getRootProps()} className="upload-area">
                  <CloudUploadOutlinedIcon />
                  <input {...getInputProps()} />
                  <Typography className="p14 upload-text">
                    Drop your images or videos here or click to upload
                  </Typography>
                </Box>
              </Box>

              {/* Display Image and Video Previews */}
              <Box className="media-previews">
                {mediaFiles?.map((media, index) => (
                  <Box key={index} className="preview-container">
                    {media?.type === 'image' ? (
                      <Box className="image-container">
                        <Image
                          src={media?.preview}
                          alt={`preview-${index}`}
                          className="preview-img"
                          width={100}
                          height={100}
                          onClick={() => handlePreviewClick(media?.preview)}
                        />
                      </Box>
                    ) : media.type === 'video' ? (
                      <Box
                        className="video-container"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handlePreviewClick(media)}
                      >
                        <video
                          className="preview-video"
                          width="100"
                          height="100"
                          onClick={() => handlePreviewClick(media)}
                        >
                          <source src={media?.preview} />
                          Your browser does not support the video tag.
                        </video>
                      </Box>
                    ) : null}
                    <Box className="close-icon-wrap">
                      <CloseIcon
                        onClick={() => removeMedia(index)}
                        sx={{ cursor: 'pointer' }}
                        className="close-icon"
                      />
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>

            {/* Personal Information Section */}
            <Box className="personal-info-wrap">
              <Typography className="pt16 section-title" component="p">
                Please provide your personal details for further communication.
              </Typography>
              <Box
                className={`ticket-form-grid-container ${isMobile ? 'mobile-layout' : ''}`}
              >
                <Box>
                  <CustomTextField
                    fullWidth
                    name="Name"
                    label="Name"
                    placeholder="Enter your name"
                    value={values.Name}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.Name && Boolean(errors.Name)}
                    helperText={touched.Name && errors.Name}
                    required
                  />
                </Box>

                <Box>
                  <CustomTextField
                    fullWidth
                    name="Email"
                    label="Email"
                    placeholder="Enter your email"
                    value={values.Email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.Email && Boolean(errors.Email)}
                    helperText={touched.Email && errors.Email}
                    required
                  />
                </Box>

                <Box>
                  <CustomTextField
                    fullWidth
                    name="PhoneNumber"
                    label="Phone Number"
                    placeholder="Enter your phone number"
                    value={values.PhoneNumber}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.PhoneNumber && Boolean(errors.PhoneNumber)}
                    helperText={touched.PhoneNumber && errors.PhoneNumber}
                    required
                  />
                </Box>
              </Box>
            </Box>

            {/* Terms and Conditions */}
            <Box className="pt16">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={values?.term_condition}
                    onChange={handleChange}
                    name="term_condition"
                    color="primary"
                  />
                }
                label="I agree to the terms and conditions."
              />
              {touched?.term_condition && errors?.term_condition && (
                <div className="error">{errors?.term_condition}</div>
              )}
            </Box>

            <Box className="form-actions-btn">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                type="button"
              />
              <CustomButton
                fullWidth
                variant="contained"
                type="submit"
                title="Create Ticket"
              />
            </Box>
          </Box>
          <PreviewModal
            open={openModal}
            setOpen={setOpenModal}
            previewMedia={previewMedia}
          />

          {/* Filter Drawer */}
          <RightDrawer
            anchor="right"
            open={openFilterDrawer}
            onClose={() => setOpenFilterDrawer(false)}
            title="Ticket Filter"
            className="filter-options-drawer"
            content={
              <FilterComponent
                filters={filters}
                filterData={filterData}
                setFilterData={setFilterData}
                selectedFilters={selectedFilters}
                toggleFilter={toggleFilter}
                getFirstFourFilters={getFirstFourFilters}
                setSearchValue={setSearchValue}
                searchValue={searchValue}
                handleKeyPress={handleKeyPress}
                isMobile={isMobile}
                handleApplyFilter={handleApplyFilter}
                handleClearFilter={handleClearFilter}
              />
            }
          />
        </Form>
      )}
    </Formik>
  );
}
