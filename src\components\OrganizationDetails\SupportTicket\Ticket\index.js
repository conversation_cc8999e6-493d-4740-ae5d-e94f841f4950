import { Box, Tooltip, Typography } from '@mui/material';
import React from 'react';
import Image from 'next/image';
import CustomSelect from '@/components/UI/CustomSelect';
import ProfileImage from '../../../../../public/images/Companylogo.png';
import QueryBuilderIcon from '@mui/icons-material/QueryBuilder';
import './ticket.scss';

const statusOptions = [
  { label: 'Open', value: 'open' },
  { label: 'Escalated', value: 'escalated' },
  { label: 'In-Progress', value: 'in_progress' },
  { label: 'Invoiced', value: 'invoiced' },
  { label: 'On Hold', value: 'on_hold' },
  { label: 'QA Review', value: 'qa_review' },
  { label: 'Assigned', value: 'assigned' },
  { label: 'Under Review', value: 'under_review' },
  { label: 'Closed', value: 'closed' },
];

export default function Ticket({
  selectedStatus,
  setSelectedStatus,
  handleStatusChange,
  onTicketClick,
}) {
  const ticketData = {
    id: 100,
    name: '<PERSON><PERSON><PERSON>',
    time: '12:30 PM',
    status: selectedStatus,
  };

  return (
    <Box
      className="ticket-wrap"
      onClick={() => onTicketClick(ticketData)} // NEW: Pass data when clicked
    >
      <Box className="heading-wrap d-flex align-center">
        <Typography className="heading-text-wrap" variant="h6">
          Test
        </Typography>
      </Box>

      <Box className="d-flex align-center id-name-wrap">
        <Typography className="id-wrap" variant="h6">
          #{ticketData.id}
        </Typography>
        <Tooltip title={ticketData.name} arrow>
          <Typography component="p" className="name-wrap">
            {ticketData.name}
          </Typography>
        </Tooltip>
      </Box>

      <Box className="d-flex align-center gap-5 time-wrap">
        <QueryBuilderIcon className="timer-icon" />
        <Typography component="p" className="time-text">
          {ticketData.time}
        </Typography>
      </Box>

      <Box className="">
        <CustomSelect
          className="slected-wrap ticket-status-select"
          placeholder="Select Status"
          options={statusOptions}
          value={
            statusOptions?.find((opt) => opt?.value === selectedStatus) || ''
          }
          name="status"
          onChange={(selectedOption) =>
            setSelectedStatus(selectedOption?.value || '')
          }
        />
      </Box>

      <Box className="profile-wrap">
        <Image
          className="profile-image"
          src={ProfileImage}
          alt="Profile Image"
          width={100}
          height={100}
        />
      </Box>
    </Box>
  );
}
