.quick-action-panel {
  background-color: var(--color-white);
  border: var(--border-width-xs) solid #e5e7eb;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);

  // Header Section
  &__header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid #f3f4f6;
  }

  &__header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  &__header-icon {
    color: var(--color-orange);
  }

  // Content Section
  &__content {
    padding: var(--spacing-lg);
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // Action Card
  &__action {
    position: relative;
    padding: var(--spacing-lg);
    border: var(--border-width-xs) solid;
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    display: block;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      .quick-action-panel__action-arrow {
        opacity: 1;
        transform: translateX(4px);
      }

      .quick-action-panel__action-title {
        color: inherit;
      }
    }

    // Color variants
    &--primary {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border-color: rgba(19, 94, 150, 0.2);

      &:hover {
        background-color: rgba(19, 94, 150, 0.15);
      }
    }

    &--success {
      background-color: var(--color-success-opacity);
      color: var(--color-success);
      border-color: rgba(3, 141, 42, 0.2);

      &:hover {
        background-color: rgba(3, 141, 42, 0.15);
      }
    }

    &--warning {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
      border-color: rgba(219, 151, 44, 0.2);

      &:hover {
        background-color: rgba(219, 151, 44, 0.15);
      }
    }

    &--error {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);
      border-color: rgba(211, 47, 47, 0.2);

      &:hover {
        background-color: rgba(211, 47, 47, 0.15);
      }
    }

    &--accent {
      background-color: rgba(253, 126, 20, 0.1);
      color: var(--color-orange);
      border-color: rgba(253, 126, 20, 0.2);

      &:hover {
        background-color: rgba(253, 126, 20, 0.15);
      }
    }
  }

  &__action-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  &__action-icon {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    background-color: var(--color-white);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  &__action-info {
    flex: 1;
    min-width: 0;
  }

  &__action-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
    transition: color 0.3s ease;
  }

  &__action-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__action-count {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    opacity: 0.8;
  }

  &__action-arrow {
    opacity: 0;
    transition: all 0.3s ease;
    transform: translateX(0);
  }

  // Count Badge
  &__badge {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    background-color: var(--color-danger);
    color: var(--text-color-white);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-family: var(--font-family-primary);
  }

  // Footer Section
  &__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: var(--border-width-xs) solid #f3f4f6;
    background-color: #f9fafb;
  }

  &__footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 640px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  &__footer-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  &__footer-link {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: color 0.15s ease-out;

    &:hover {
      color: var(--color-dark-blue);
    }
  }
}
