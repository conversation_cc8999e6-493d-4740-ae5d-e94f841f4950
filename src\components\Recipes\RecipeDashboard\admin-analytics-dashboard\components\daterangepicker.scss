.date-range-picker {
  position: relative;

  &__trigger {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all 0.15s ease-out;
    font-family: var(--font-family-primary);

    &:hover {
      border-color: var(--color-primary);
    }

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  &__trigger-icon {
    color: var(--text-color-slate-gray);
  }

  &__trigger-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__trigger-chevron {
    color: var(--text-color-slate-gray);
    transition: transform 0.15s ease-out;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-sm);
    width: 20rem;
    background-color: var(--color-white);
    border: var(--border-width-xs) solid #e5e7eb;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 50;
    animation: slideDown 0.2s ease-out;
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
  }

  &__presets {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  &__preset-option {
    width: 100%;
    text-align: left;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    transition: all 0.15s ease-out;
    cursor: pointer;
    border: var(--border-width-xs) solid transparent;
    background: transparent;
    font-family: var(--font-family-primary);

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border-color: rgba(19, 94, 150, 0.2);
    }
  }

  &__preset-label {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    display: block;
    margin-bottom: var(--spacing-tiny);
  }

  &__preset-description {
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  &__custom-section {
    border-top: var(--border-width-xs) solid #f3f4f6;
    padding-top: var(--spacing-lg);
  }

  &__custom-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__custom-inputs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__input-group {
    display: flex;
    flex-direction: column;
  }

  &__input-label {
    display: block;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-xs);
    font-family: var(--font-family-primary);
  }

  &__date-input {
    width: 100%;
    padding: var(--spacing-sm);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    transition: all 0.15s ease-out;

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  &__apply-btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--color-primary);
    color: var(--text-color-white);
    border: none;
    border-radius: var(--border-radius-lg);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.15s ease-out;
    margin-top: var(--spacing-md);

    &:hover:not(:disabled) {
      background-color: var(--color-dark-blue);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
