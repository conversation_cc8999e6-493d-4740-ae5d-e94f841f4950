.date-range-picker {
  position: relative;
  width: 100%;
  max-width: 240px;

  &__trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-sm);
    padding: 7px 15px;
    background-color: var(--color-white);
    border: var(--field-border);
    border-radius: var(--field-radius);
    cursor: pointer;
    transition: border-color 0.2s ease-in-out;
    font-family: var(--font-family-primary);
    min-height: 36px;
    width: 100%;
    position: relative;

    &:hover {
      border: var(--field-border-primary);
    }

    &:focus {
      outline: none;
      border: var(--field-border-primary);
      box-shadow: none;
    }
  }

  &__trigger-icon {
    color: var(--text-color-slate-gray);
    flex-shrink: 0;
  }

  &__trigger-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    color: var(--text-color-black);
    line-height: var(--line-height-sm);
    flex: 1;
    text-align: left;
  }

  &__trigger-chevron {
    color: var(--text-color-slate-gray);
    transition: transform 0.2s ease-in-out;
    flex-shrink: 0;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: var(--spacing-xs);
    width: 320px;
    max-width: 90vw;
    background-color: var(--color-white);
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-xs);
    box-shadow: var(--box-shadow-xs);
    z-index: 999999;
    animation: slideDown 0.2s ease-out;

    // Ensure dropdown is above other elements
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      pointer-events: none;
    }

    // Responsive positioning
    @media (max-width: 480px) {
      left: -50%;
      width: 280px;
      max-width: calc(100vw - 32px);
    }

    @media (max-width: 360px) {
      left: -75%;
      width: 260px;
    }
  }

  &__content {
    padding: 16px;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
  }

  &__presets {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: var(--spacing-lg);
    max-height: 200px;
    overflow-y: auto;
  }

  &__preset-option {
    width: 100%;
    text-align: left;
    padding: 8px 16px;
    border-radius: var(--border-radius-xs);
    transition: background-color 0.2s ease-in-out;
    cursor: pointer;
    border: none;
    background: transparent;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);

    &:hover {
      background-color: var(--color-primary);
      color: var(--text-color-white);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
    }
  }

  &__preset-label {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    display: block;
    margin-bottom: 2px;
    line-height: var(--line-height-sm);
  }

  &__preset-description {
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    line-height: var(--line-height-xs);

    .date-range-picker__preset-option:hover &,
    .date-range-picker__preset-option--active & {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  &__custom-section {
    border-top: var(--border-width-xs) solid #f3f4f6;
    padding-top: var(--spacing-lg);
  }

  &__custom-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__custom-inputs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__input-group {
    display: flex;
    flex-direction: column;
  }

  &__input-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    margin-bottom: var(--spacing-xxs);
    font-family: var(--font-family-primary);
    line-height: var(--line-height-sm);
  }

  &__date-input {
    width: 100%;
    padding: 7px 12px;
    border: var(--field-border);
    border-radius: var(--field-radius);
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    transition: border-color 0.2s ease-in-out;
    background: var(--field-background);
    line-height: var(--line-height-sm);
    min-height: 36px;

    &:hover {
      border: var(--field-border-primary);
    }

    &:focus {
      outline: none;
      border: var(--field-border-primary);
      box-shadow: none;
    }
  }

  &__apply-btn {
    width: 100%;
    padding: 8px 16px;
    background-color: var(--color-primary);
    color: var(--text-color-white);
    border: none;
    border-radius: var(--border-radius-xs);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
    margin-top: var(--spacing-md);
    min-height: 36px;

    &:hover:not(:disabled) {
      background-color: var(--color-dark-blue);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Global z-index fix for date range picker
body {
  .date-range-picker__dropdown {
    z-index: 999999 !important;
  }
}
