.date-range-picker {
  position: relative;
  width: 100%;
  max-width: 240px;

  &__trigger {
    .custom-btn {
      justify-content: space-between !important;
      padding: 7px 12px !important;
      background-color: var(--color-white) !important;
      border: var(--field-border) !important;
      border-radius: var(--field-radius) !important;
      font-family: var(--font-family-primary) !important;
      min-height: 36px !important;
      color: var(--text-color-black) !important;
      text-transform: none !important;

      &:hover {
        border: var(--field-border-primary) !important;
        background-color: var(--color-white) !important;
      }

      &:focus {
        outline: none !important;
        border: var(--field-border-primary) !important;
        box-shadow: none !important;
      }

      .MuiButton-startIcon {
        margin-right: var(--spacing-sm) !important;
        color: var(--text-color-slate-gray) !important;
      }

      .MuiButton-endIcon {
        margin-left: var(--spacing-sm) !important;
        color: var(--text-color-slate-gray) !important;
      }
    }
  }

  &__trigger-text {
    font-size: var(--font-size-sm) !important;
    font-weight: var(--font-weight-normal) !important;
    color: var(--text-color-black) !important;
    line-height: var(--line-height-sm) !important;
    flex: 1;
    text-align: left;
  }

  &__trigger-chevron {
    transition: transform 0.2s ease-in-out;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: var(--spacing-xs);
    width: 320px;
    max-width: 90vw;
    background-color: var(--color-white);
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-xs);
    box-shadow: var(--box-shadow-xs);
    z-index: 999999;
    animation: slideDown 0.2s ease-out;

    // Ensure dropdown is above other elements
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      pointer-events: none;
    }

    // Responsive positioning
    @media (max-width: 480px) {
      left: -50%;
      width: 280px;
      max-width: calc(100vw - 32px);
    }

    @media (max-width: 360px) {
      left: -75%;
      width: 260px;
    }
  }

  &__content {
    padding: 16px;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
  }

  &__presets {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    max-height: 200px;
    overflow-y: auto;
  }

  &__preset-option {
    .custom-btn {
      text-align: left !important;
      justify-content: flex-start !important;
      padding: 8px 12px !important;
      border-radius: var(--border-radius-xs) !important;
      font-size: var(--font-size-sm) !important;
      line-height: var(--line-height-sm) !important;
      min-height: auto !important;

      .MuiButton-startIcon {
        margin-right: var(--spacing-sm) !important;
        color: inherit !important;
      }

      &.MuiButton-outlined {
        border-color: var(--color-light-gray) !important;
        color: var(--text-color-primary) !important;
        background-color: transparent !important;

        &:hover {
          background-color: var(--color-primary-opacity) !important;
          border-color: var(--color-primary) !important;
          color: var(--color-primary) !important;
        }
      }

      &.MuiButton-contained {
        background-color: var(--color-primary) !important;
        border-color: var(--color-primary) !important;
        color: var(--text-color-white) !important;

        &:hover {
          background-color: var(--color-dark-blue) !important;
        }
      }
    }
  }

  &__preset-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    width: 100%;
  }

  &__preset-label {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    display: block;
    margin-bottom: 2px;
    line-height: var(--line-height-sm);
  }

  &__preset-description {
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    line-height: var(--line-height-xs);
    opacity: 0.8;

    .date-range-picker__preset-option .custom-btn.MuiButton-contained & {
      color: rgba(255, 255, 255, 0.8);
    }

    .date-range-picker__preset-option .custom-btn.MuiButton-outlined:hover & {
      color: var(--color-primary);
      opacity: 0.9;
    }
  }

  &__custom-section {
    border-top: var(--border-width-xs) solid #f3f4f6;
    padding-top: var(--spacing-lg);
  }

  &__custom-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__custom-inputs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__input-group {
    display: flex;
    flex-direction: column;
  }

  &__input-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    margin-bottom: var(--spacing-xxs);
    font-family: var(--font-family-primary);
    line-height: var(--line-height-sm);
  }

  &__date-input {
    width: 100%;
    padding: 7px 12px;
    border: var(--field-border);
    border-radius: var(--field-radius);
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    transition: border-color 0.2s ease-in-out;
    background: var(--field-background);
    line-height: var(--line-height-sm);
    min-height: 36px;

    &:hover {
      border: var(--field-border-primary);
    }

    &:focus {
      outline: none;
      border: var(--field-border-primary);
      box-shadow: none;
    }
  }

  &__apply-btn {
    margin-top: var(--spacing-md);

    .custom-btn {
      background-color: var(--color-primary) !important;
      color: var(--text-color-white) !important;
      border-color: var(--color-primary) !important;
      border-radius: var(--border-radius-xs) !important;
      font-size: var(--font-size-sm) !important;
      line-height: var(--line-height-sm) !important;
      padding: 8px 16px !important;
      min-height: 36px !important;

      &:hover:not(.Mui-disabled) {
        background-color: var(--color-dark-blue) !important;
        border-color: var(--color-dark-blue) !important;
      }

      &.Mui-disabled {
        opacity: 0.5 !important;
        cursor: not-allowed !important;
        background-color: var(--color-light-gray) !important;
        color: var(--text-color-slate-gray) !important;
        border-color: var(--color-light-gray) !important;
      }
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Global z-index fix for date range picker
body {
  .date-range-picker__dropdown {
    z-index: 999999 !important;
  }
}
