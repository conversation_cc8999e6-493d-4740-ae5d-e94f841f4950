import React from 'react';
import ContactForm from './ContactForm';
import ContactInfo from './ContactInfo';
import CustomCtaLink from './CustomCtaLink';
import './ContactCard.scss';

const ContactCard = ({ recipeData }) => {
  const contactSettings =
    recipeData?.organization_settings?.publicRecipeCallToAction;

  // Don't render if no contact settings are enabled
  if (!contactSettings || contactSettings.none) {
    return null;
  }

  const handleFormSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
    // You can add API call here to submit the form
  };

  const handleCustomCtaClick = () => {
    if (contactSettings?.customCtaLink?.link) {
      window.open(
        contactSettings.customCtaLink.link,
        '_blank',
        'noopener,noreferrer'
      );
    }
  };

  // Priority order: contactForm > contactInfo > customCtaLink
  // Render Contact Form (Highest Priority)
  if (contactSettings?.contactForm) {
    return <ContactForm handleFormSubmit={handleFormSubmit} />;
  }

  // Render Contact Info (Second Priority)
  if (contactSettings?.contactInfo?.enabled) {
    const contactInfo = contactSettings.contactInfo;
    return <ContactInfo contactInfo={contactInfo} />;
  }

  // Render Custom CTA Link (Lowest Priority)
  if (contactSettings?.customCtaLink?.enabled) {
    const ctaLink = contactSettings?.customCtaLink;
    return (
      <CustomCtaLink
        ctaLink={ctaLink}
        handleCustomCtaClick={handleCustomCtaClick}
      />
    );
  }

  return null;
};

export default ContactCard;
