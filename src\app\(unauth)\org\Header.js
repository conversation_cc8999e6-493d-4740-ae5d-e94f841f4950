'use client';
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import { useRouter } from 'next/navigation';
import FreeDemo from '@/components/FreeDemo';
import Link from 'next/link';
import RightDrawer from '@/components/UI/RightDrawer';
import MenuOutlinedIcon from '@mui/icons-material/MenuOutlined';
import AppBrandLogo from '@/components/UI/AppBrandLogo';
import { INFO_LINKS } from '@/helper/constants/urls';
export default function UnAuthHeader({ pathname }) {
  const [open, setOpen] = React.useState(false);
  const [openMenuDrawer, setOpenMenuDrawer] = useState(false);
  const handleClickOpen = () => {
    setOpen(true);
    setOpenMenuDrawer(false);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const router = useRouter();
  const handleNavigate = () => {
    pathname === '/org/login'
      ? router.push('/org/sign-up')
      : router.push('/org/login');
    setOpenMenuDrawer(false);
  };
  return (
    <>
      <Box className=" un-auth-header-wrap d-flex align-center justify-space-between flex-wrap pt32">
        <AppBrandLogo />
        <Box className="org-unauth-menu-icon">
          <MenuOutlinedIcon onClick={() => setOpenMenuDrawer(true)} />
        </Box>
        <Box className="d-flex align-center un-auth-header-btn-wrap">
          <Box
            className="login-btn-wrap d-flex align-center"
            textAlign="center"
          >
            <Typography component="p" className="">
              <Link
                href={INFO_LINKS?.home}
                target="_blank"
                rel="noopener noreferrer"
                className="login-btn"
              >
                Home
              </Link>
            </Typography>
            <Typography component="p" className="">
              <Link
                href={INFO_LINKS?.contactUs}
                target="_blank"
                rel="noopener noreferrer"
                className="login-btn"
              >
                Contact Us
              </Link>
            </Typography>
            <Typography
              component="p"
              className="login-btn cursor-pointer"
              onClick={() => handleNavigate()}
            >
              {pathname === '/org/login' ? 'Sign Up' : 'Log In'}
            </Typography>
          </Box>
          <Box className="free-demo-btn-wrap" textAlign="center">
            <CustomButton
              className="free-demo-btn"
              variant="contained"
              title="Request Free Demo"
              type="submit"
              onClick={handleClickOpen}
            />
          </Box>
        </Box>
      </Box>
      <FreeDemo open={open} handleClose={handleClose} />
      <RightDrawer
        className="unauth-menu-drawer"
        anchor={'right'}
        open={openMenuDrawer}
        onClose={() => setOpenMenuDrawer(false)}
        title="Menu"
        content={
          <Box className="d-flex flex-col align-center un-auth-header-menu">
            <Box
              className="login-btn-wrap d-flex align-center flex-col"
              textAlign="center"
            >
              <Typography
                component="p"
                className="login-btn cursor-pointer"
                onClick={() => setOpenMenuDrawer(false)}
              >
                <Link
                  href={INFO_LINKS?.home}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="login-btn"
                >
                  Home
                </Link>
              </Typography>
              <Typography
                component="p"
                className="login-btn cursor-pointer"
                onClick={() => setOpenMenuDrawer(false)}
              >
                <Link
                  href={INFO_LINKS?.contactUs}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="login-btn"
                >
                  Contact Us
                </Link>
              </Typography>
              <Typography
                component="p"
                className="login-btn cursor-pointer"
                onClick={() => handleNavigate()}
              >
                {pathname === '/org/login' ? 'Sign Up' : 'Log In'}
              </Typography>
            </Box>
            <Box className="menu-free-demo-btn-wrap" textAlign="center">
              <CustomButton
                fullWidth
                className="free-demo-btn"
                fontWeight="600"
                variant="contained"
                background="#39596e"
                backgroundhover="#39596e"
                colorhover="#FFFFFF"
                title="Request Free Demo"
                type="submit"
                onClick={handleClickOpen}
              />
            </Box>
          </Box>
        }
      />
    </>
  );
}
