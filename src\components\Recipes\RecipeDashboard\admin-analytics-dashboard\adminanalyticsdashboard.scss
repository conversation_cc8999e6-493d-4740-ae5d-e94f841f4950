.admin-analytics-dashboard {
  font-family: var(--font-family-primary);
  position: relative;
  width: 100%;

  // Main Content Area
  &__main {
    width: 100%;
    overflow-x: auto;
    overflow-y: auto;
  }

  // Container
  &__container {
    width: 100%;
    padding: 0;
  }

  // Header Section
  &__header {
    background: linear-gradient(135deg, var(--color-white) 0%, #f8f9fa 100%);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xl);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: var(--border-width-xs) solid rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: auto;

    @media (min-width: 768px) {
      border-radius: var(--border-radius-lg);
      margin-bottom: var(--spacing-2xl);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    @media (min-width: 1024px) {
      margin-bottom: var(--spacing-3xl);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
  }

  &__header-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);

    @media (min-width: 640px) {
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
    }

    @media (min-width: 1024px) {
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
    }
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-2xl);
    background: linear-gradient(
      135deg,
      var(--color-primary) 0%,
      var(--color-dark-blue) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;

    @media (min-width: 640px) {
      font-size: var(--font-size-3xl);
    }

    @media (min-width: 1024px) {
      font-size: var(--font-size-4xl);
    }
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
    margin-bottom: var(--spacing-sm);
    color: var(--text-color-slate-gray);
    opacity: 0.8;
  }

  // Live Indicator
  &__live-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
  }

  &__live-dot {
    width: var(--spacing-sm);
    height: var(--spacing-sm);
    background-color: var(--color-success);
    border-radius: var(--border-radius-full);
    animation: pulse 2s infinite;
  }

  &__live-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-success);
    font-weight: var(--font-weight-medium);
  }

  // Header Actions
  &__header-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;

    @media (min-width: 640px) {
      flex-direction: row;
      align-items: center;
      gap: var(--spacing-md);
    }

    @media (min-width: 1024px) {
      align-items: flex-end;
      justify-content: flex-end;
    }
  }

  &__export-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    width: 100%;

    @media (min-width: 640px) {
      flex-direction: row;
      align-items: center;
      gap: var(--spacing-sm);
      width: auto;
    }
  }

  &__export-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: var(--border-width-xs) solid;
    position: relative;
    overflow: hidden;
    flex: 1;

    @media (min-width: 640px) {
      gap: var(--spacing-sm);
      padding: var(--spacing-md) var(--spacing-lg);
      border-radius: var(--border-radius-lg);
      font-size: var(--font-size-sm);
      flex: none;
      justify-content: flex-start;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      &::before {
        left: 100%;
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    &--primary {
      background: linear-gradient(
        135deg,
        var(--color-primary) 0%,
        var(--color-dark-blue) 100%
      );
      color: var(--text-color-white);
      border-color: var(--color-primary);
      box-shadow: 0 4px 15px rgba(19, 94, 150, 0.3);

      &:hover:not(:disabled) {
        box-shadow: 0 8px 25px rgba(19, 94, 150, 0.4);
      }
    }

    &--secondary {
      background: rgba(255, 255, 255, 0.9);
      color: var(--color-primary);
      border-color: var(--color-primary);
      backdrop-filter: blur(10px);

      &:hover:not(:disabled) {
        background: var(--color-primary-opacity);
        border-color: var(--color-dark-blue);
      }
    }
  }

  // Statistics Grid
  &__stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);

    @media (min-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-2xl);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (min-width: 1280px) {
      grid-template-columns: repeat(6, 1fr);
      margin-bottom: var(--spacing-3xl);
    }
  }

  // Charts Grid
  &__charts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);

    @media (min-width: 768px) {
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-2xl);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
      margin-bottom: var(--spacing-3xl);
    }
  }

  // Bottom Section
  &__bottom-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);

    @media (min-width: 768px) {
      gap: var(--spacing-lg);
    }

    @media (min-width: 1024px) {
      grid-template-columns: 2fr 1fr;
    }

    @media (min-width: 1280px) {
      grid-template-columns: 3fr 2fr;
    }
  }

  // Filter Placeholder
  &__filter-placeholder {
    background-color: var(--color-light-blue);
    border: var(--border-width-xs) dashed var(--color-primary);
    border-radius: var(--border-radius-md);
    text-align: center;
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);

    p {
      margin: 0;
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      color: var(--text-color-slate-gray);
    }
  }

  // Export Overlay
  &__export-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
  }

  &__export-modal {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xxl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
  }

  &__export-spinner {
    animation: spin 1s linear infinite;
    color: var(--color-primary);
  }

  &__export-text {
    color: var(--text-color-primary);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
  }
}

// Animations
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
