'use client';

import React from 'react';
import './AdminAnalyticsDashboard/adminanalyticsdashboard.scss';

const TestDashboard = () => {
  return (
    <div className="admin-analytics-dashboard">
      <main className="admin-analytics-dashboard__main">
        <div className="admin-analytics-dashboard__container">
          {/* Header Section */}
          <div className="admin-analytics-dashboard__header">
            <div className="admin-analytics-dashboard__header-content">
              <div className="admin-analytics-dashboard__header-info">
                <h1 className="admin-analytics-dashboard__title">
                  Test Analytics Dashboard
                </h1>
                <p className="admin-analytics-dashboard__subtitle">
                  Testing if styles are working properly
                </p>
              </div>
            </div>
          </div>

          {/* Test Grid */}
          <div className="admin-analytics-dashboard__stats-grid">
            <div style={{ 
              backgroundColor: 'white', 
              padding: '1rem', 
              borderRadius: '8px',
              border: '1px solid #ddd',
              textAlign: 'center'
            }}>
              <h3>Test Card 1</h3>
              <p>1,234</p>
            </div>
            <div style={{ 
              backgroundColor: 'white', 
              padding: '1rem', 
              borderRadius: '8px',
              border: '1px solid #ddd',
              textAlign: 'center'
            }}>
              <h3>Test Card 2</h3>
              <p>856</p>
            </div>
            <div style={{ 
              backgroundColor: 'white', 
              padding: '1rem', 
              borderRadius: '8px',
              border: '1px solid #ddd',
              textAlign: 'center'
            }}>
              <h3>Test Card 3</h3>
              <p>$12,450</p>
            </div>
          </div>

          {/* Test Charts Grid */}
          <div className="admin-analytics-dashboard__charts-grid">
            <div style={{ 
              backgroundColor: 'white', 
              padding: '2rem', 
              borderRadius: '8px',
              border: '1px solid #ddd',
              height: '300px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <h3>Chart Widget 1</h3>
            </div>
            <div style={{ 
              backgroundColor: 'white', 
              padding: '2rem', 
              borderRadius: '8px',
              border: '1px solid #ddd',
              height: '300px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <h3>Chart Widget 2</h3>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default TestDashboard;
