import React from 'react';
import { Box, Typography, Divider } from '@mui/material';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import HeaderImage from '@/components/UI/ImageSecurity';
import CommentIcon from '@mui/icons-material/Comment';
import './crhistorydetails.scss';
const CRHistoryDetails = ({ cdata, crData, getImageName }) => {
  return (
    <Box className="change-request-history-details">
      {cdata?.change_request_subject && (
        <Typography className="body-text  fw400">
          <span className="fw500"> Subject : </span>
          <span className="title-text">{cdata?.change_request_subject}</span>
        </Typography>
      )}
      <Box className="old-new-info-box d-flex">
        {cdata?.old_data && (
          <Box className="old-info-box">
            <Typography className="body-text fw500 pt4">
              Old information
            </Typography>
            <Typography className="title-text">
              {crData?.old_data.split('\n').map((line, index) => (
                <React.Fragment key={index}>
                  {line}
                  <br />
                </React.Fragment>
              ))}
            </Typography>
          </Box>
        )}
        {cdata?.old_data && cdata?.new_data && (
          <Divider
            className="cr-history-divider"
            orientation="vertical"
            flexItem
          />
        )}
        {cdata?.new_data && (
          <Box className="new-info-box">
            <Typography className="body-text  fw500 pt4">
              New information
            </Typography>
            <Typography className="title-text">
              {crData?.new_data.split('\n').map((line, index) => (
                <React.Fragment key={index}>
                  {line}
                  <br />
                </React.Fragment>
              ))}
            </Typography>
          </Box>
        )}
      </Box>
      {cdata &&
        cdata?.change_request_files &&
        cdata?.change_request_files?.length > 0 && (
          <Box className="pt4">
            <Typography className="body-text  fw500 pt4">
              Attached file
            </Typography>
            <Box className="file-grid-container">
              {cdata?.change_request_files?.map((f) => {
                const url = crData?.baseUrl + f;
                return (
                  <Box
                    key={f}
                    className="selected-files selected-view-files file-grid-item"
                  >
                    <Box className="file-name">
                      <Box className="d-flex align-center gap-sm">
                        <InsertDriveFileIcon className="file-icon" />
                        <Typography className="title-text text-ellipsis-line">
                          {getImageName(f)}
                        </Typography>
                      </Box>
                    </Box>
                    <HeaderImage
                      type="url"
                      imageUrl={url}
                      Content={<RemoveRedEyeIcon />}
                      className="d-flex align-center"
                    />
                  </Box>
                );
              })}
            </Box>
          </Box>
        )}

      {cdata?.change_request_remark && (
        <Box>
          <Typography className="body-text fw500 pt8">Remark</Typography>
          <Box className="d-flex align-start">
            <CommentIcon className="res-remark-icon" />
            <Typography className="title-text">
              {cdata?.change_request_remark}
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default CRHistoryDetails;
