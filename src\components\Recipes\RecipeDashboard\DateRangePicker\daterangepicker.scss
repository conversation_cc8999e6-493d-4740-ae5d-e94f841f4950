.date-range-picker {
  position: relative;
  display: inline-block;

  // Trigger <PERSON>
  &__trigger {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.15s ease-out;
    font-family: var(--font-family-primary);

    &:hover {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 1px var(--color-primary);
    }

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(19, 94, 150, 0.1);
    }
  }

  &__icon {
    color: var(--text-color-slate-gray);
    flex-shrink: 0;
  }

  &__label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    white-space: nowrap;
  }

  &__chevron {
    color: var(--text-color-slate-gray);
    flex-shrink: 0;
    transition: transform 0.15s ease-out;
  }

  // Dropdown
  &__dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-sm);
    width: 320px;
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-xs);
    z-index: 50;
    animation: slideDown 0.15s ease-out;

    @media (max-width: 480px) {
      width: 280px;
      right: auto;
      left: 0;
    }
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }

  // Preset Ranges
  &__presets {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  &__preset-btn {
    width: 100%;
    text-align: left;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.15s ease-out;
    font-family: var(--font-family-primary);

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border: var(--border-width-xs) solid var(--color-primary);
    }
  }

  &__preset-label {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-tiny);
  }

  &__preset-description {
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  // Custom Range Section
  &__custom {
    border-top: var(--border-width-xs) solid var(--color-light-grayish-blue);
    padding-top: var(--spacing-lg);
  }

  &__custom-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__custom-fields {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  // Form Fields
  &__field {
    display: flex;
    flex-direction: column;
  }

  &__field-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-tiny);
  }

  &__field-input {
    padding: var(--spacing-sm);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    transition: all 0.15s ease-out;

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(19, 94, 150, 0.1);
    }

    &:hover {
      border-color: var(--color-primary);
    }
  }

  // Apply Button
  &__apply-btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--color-primary);
    color: var(--text-color-white);
    border: none;
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover:not(:disabled) {
      background-color: var(--color-dark-blue);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Animation for dropdown
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
