import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './ContactCard.scss';

const ContactInfo = ({ contactInfo }) => (
  <div className="contact-card">
    <div className="contact-card__header">
      <p className="contact-card__title">
        <Icon name="Phone" size={20} color="currentColor" />
        <span>Contact Information</span>
      </p>
    </div>
    <div className="contact-card__content">
      <div className="contact-card__info">
        {contactInfo.name && (
          <div className="contact-card__info-item">
            <Icon name="User" size={16} className="contact-card__info-icon" />
            <span className="contact-card__info-text">{contactInfo.name}</span>
          </div>
        )}
        {contactInfo.phone && (
          <div className="contact-card__info-item">
            <Icon name="Phone" size={16} className="contact-card__info-icon" />
            <a
              href={`tel:${contactInfo.phone}`}
              className="contact-card__info-link"
            >
              {contactInfo.phone}
            </a>
          </div>
        )}
        {contactInfo.email && (
          <div className="contact-card__info-item">
            <Icon name="Mail" size={16} className="contact-card__info-icon" />
            <a
              href={`mailto:${contactInfo.email}`}
              className="contact-card__info-link"
            >
              {contactInfo.email}
            </a>
          </div>
        )}
        {contactInfo.link && (
          <div className="contact-card__info-item">
            <Icon
              name="ExternalLink"
              size={16}
              className="contact-card__info-icon"
            />
            <a
              href={contactInfo.link}
              target="_blank"
              rel="noopener noreferrer"
              className="contact-card__info-link"
            >
              Visit Website
            </a>
          </div>
        )}
      </div>
    </div>
  </div>
);

export default ContactInfo;
