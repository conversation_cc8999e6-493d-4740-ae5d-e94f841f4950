'use client';
import React, { useEffect, useState, useContext } from 'react';
import { Box, Tab } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import SupportTicket from '@/components/OrganizationDetails/SupportTicket';
import OrganizationProfile from '@/components/OrganizationDetails/Profile';
import OrganizationForm from '@/components/OrganizationDetails/OrganizationForm';
import PlanDetails from '@/components/PaymentMethod/PlanDetails';
import AuthContext from '@/helper/authcontext';
// import { fetchFromStorage } from '@/helper/context/storage';
// import { identifiers } from '@/helper/constants/identifier';
import { usePathname, useRouter } from 'next/navigation';
import OrganizationProfileDetails from '@/components/OrganizationDetails/OrganizationForm/OrganizationProfileDetails';
import PaymentHistory from '@/components/OrganizationDetails/PaymentHistory';
import Billing from '@/components/OrganizationDetails/Billing';
import PersonIcon from '@mui/icons-material/Person';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
import ReceiptIcon from '@mui/icons-material/Receipt';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './organization.scss';

// const userOptionsList = ['Profile', 'Current Plan', 'Payment History'];
const userOptionsList = [
  {
    id: 1,
    name: 'Profile',
    icon: <PersonIcon className="icon-wrap" />,
  },
  {
    id: 2,
    name: 'Plan Management & Billing',
    icon: <CardMembershipIcon className="icon-wrap" />,
  },
  {
    id: 3,
    name: 'Payment History',
    icon: <ReceiptIcon className="icon-wrap" />,
  },
];
export default function Organization() {
  const { authState, setRestrictedModal, orgDetails } = useContext(AuthContext);
  const pathname = usePathname();
  // const authdata = fetchFromStorage(identifiers?.AUTH_DATA);
  const [selectedUserOption, setSelectedUserOption] = useState(1);
  const [previewImage, setPreviewImage] = useState();
  const [organizationDetails, setOrganizationDetails] = useState({
    name: '',
    website: '',
  });
  const [tab, setTab] = useState(1);
  const router = useRouter();
  const isNormalUser = fetchFromStorage(identifiers?.NORMAL_LOGIN);
  const getVisibleTabs = () => {
    if (selectedUserOption === 1) {
      return [
        { id: 1, name: 'Organization Profile' },
        { id: 2, name: 'Super Admin' },
        // { id: 6, name: 'Billing' },
      ];
    }
    return [];
  };

  const tabChangeHandler = (event, newValue) => {
    setTab(newValue);
  };

  useEffect(() => {
    // const authData = fetchFromStorage(identifiers?.AUTH_DATA);
    if (checkOrganizationRole('super_admin')) {
      router.push(`/sorg/organization`);
    } else if (orgDetails && orgDetails?.attributes) {
      const isOrgView = checkOrganizationRole('org_master') || false;
      const isRestricted = isNormalUser
        ? false
        : isOrgView
          ? !orgDetails?.attributes?.email ||
            !orgDetails?.attributes?.contact_person ||
            authState?.purchase_plan === false
          : !authState?.profile_status;

      if (isRestricted) {
        setRestrictedModal({
          isOrgView: isOrgView,
          purchase_plan: authState?.purchase_plan,
          user_status:
            !orgDetails?.attributes?.email ||
            !orgDetails?.attributes?.contact_person,
          profile_status: authState?.profile_status,
        });
      }
    }
  }, [pathname, orgDetails?.attributes]);

  // useEffect(() => {
  //   if (selectedUserOption === 'Profile') {
  //     setTab(1);
  //   }
  // }, [selectedUserOption]);
  return (
    <Box className="main-page-container">
      <Box className="organization-profile-wrap d-flex gap-10">
        <Box className="left-profile-header d-flex">
          <OrganizationProfileDetails
            previewImage={previewImage}
            userOptionsList={userOptionsList}
            setSelectedUserOption={setSelectedUserOption}
            selectedUserOption={selectedUserOption}
            organizationDetails={organizationDetails}
          />
        </Box>
        <Box className="organization-sec-wrap">
          {selectedUserOption === 3 ? (
            <PaymentHistory />
          ) : selectedUserOption === 2 ? (
            <PlanDetails
              setTab={setTab}
              setSelectedUserOption={setSelectedUserOption}
            />
          ) : (
            <TabContext value={String(tab)}>
              <Box className="tabs-wrap">
                <Box className="report-tabs">
                  <TabList
                    variant="scrollable"
                    scrollButtons="auto"
                    onChange={tabChangeHandler}
                    aria-label="action tabs"
                    className="tab-list-sec"
                  >
                    {getVisibleTabs()?.map((obj, index) => {
                      return (
                        <Tab
                          key={index}
                          label={obj?.name}
                          value={String(obj?.id)}
                          className="tab-name"
                        />
                      );
                    })}
                  </TabList>
                </Box>
              </Box>
              <TabPanel value="1" className="pl0 pr0 pb0 pt0">
                <OrganizationForm
                  OrgId={authState?.attributes?.organizationId}
                  previewImage={previewImage}
                  setPreviewImage={setPreviewImage}
                  organizationDetails={organizationDetails}
                  setOrganizationDetails={setOrganizationDetails}
                />
              </TabPanel>
              <TabPanel value="2" className="pl0 pr0 pb0 pt0">
                <OrganizationProfile />
              </TabPanel>
              <TabPanel value="3" className="pl0 pr0 pb0">
                <PlanDetails
                  setTab={setTab}
                  setSelectedUserOption={setSelectedUserOption}
                />
              </TabPanel>
              <TabPanel value="4" className="pl0 pr0 pb0">
                <PaymentHistory setTab={setTab} />
              </TabPanel>

              {/* <TabPanel value="6" className="pl0 pr0 pb0">
            <PrivacyPolicy />
          </TabPanel> */}
              <TabPanel value="6" className="pl0 pr0 pb0">
                <Billing setTabVal={setTab} />
              </TabPanel>
              {/* <TabPanel value="6" className="pl0 pr0 pb0">
                <PaymentMethod />
              </TabPanel> */}
              <TabPanel value="5" className="pl0 pr0 pb0 pt0">
                <SupportTicket />
              </TabPanel>
            </TabContext>
          )}
        </Box>
      </Box>
    </Box>
  );
}
