@import '@/styles/variable.scss';

.support-ticket-layout {
  .section-wrapper {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
    height: 100%;
    
    @media (max-width: 1500px) {
      flex-direction: column;
    }
    
    @media (max-width: 1024px) {
      flex-direction: column;
    }
    
    .section-left {
      max-width: 250px;
      width: 100%;
      background-color: var(--color-white);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--box-shadow-xs);
      height: 100%;
      overflow: hidden;
      
      @media (max-width: 1500px) {
        display: none;
      }
      
      @media (max-width: 1024px) {
        max-width: 100%;
        max-height: max-content;
        min-height: max-content;
        display: none;
      }
      
      .section-left-title {
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--border-color-light-gray);
        
        .sub-header-text {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
        }
      }
      
      .side-menu-list {
        padding: var(--spacing-lg);
        height: calc(100% - 49px);
        overflow: auto;
      }
    }
    
    .section-right {
      width: 100%;
      background-color: var(--color-white);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--box-shadow-xs);
      height: 100%;
      overflow: hidden;
      
      .recipe-category-filter-wrap {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        justify-content: space-between;
        padding: var(--spacing-lg) var(--spacing-xxl);
        
        @media (max-width: 768px) {
          padding: var(--spacing-md);
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-md);
        }
      }
      
      .section-right-title {
        width: 100%;
        max-width: max-content;
        
        .sub-header-text {
          font-size: var(--font-size-xl);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
        }
      }
      
      .section-right-content {
        padding: 0;
        height: calc(100% - 80px);
        overflow: hidden;
        
        // Remove the header from the nested SupportTicket component
        .support-ticket-section-wrapper {
          .support-ticket-section-right {
            .show-tickets-wrap {
              .recipe-category-filter-wrap {
                display: none; // Hide the nested header
              }
            }
          }
        }
      }
    }
  }
  
  .filter-options-drawer {
    .MuiDrawer-paper {
      width: 320px;
      max-width: 90vw;
    }
  }
  
  .filter-btn {
    @media (max-width: 1500px) {
      display: flex !important;
    }
    
    @media (min-width: 1501px) {
      display: none !important;
    }
  }
}

// Global styles for filter component in sidebar
.filter-component-sidebar {
  .filter-section {
    margin-bottom: var(--spacing-lg);
    
    .filter-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-sm);
      
      .filter-label {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--text-color-secondary);
      }
    }
    
    .filter-options {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }
  }
  
  .search-section {
    margin-bottom: var(--spacing-lg);
  }
  
  .filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    
    .MuiButton-root {
      flex: 1;
      padding: var(--spacing-sm) var(--spacing-md);
      font-size: var(--font-size-sm);
    }
  }
}
