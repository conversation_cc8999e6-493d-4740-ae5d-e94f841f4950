'use client';

import React from 'react';
import Icon from '@/components/AppIcon';
import './statisticstile.scss';

const StatisticsTile = ({ 
  id, 
  title, 
  value, 
  change, 
  trend = 'neutral', 
  sparklineData = [], 
  icon, 
  color = 'primary', 
  description, 
  onClick,
  realTimeValue 
}) => {
  const getColorClass = (colorType) => {
    const colorClasses = {
      primary: 'statistics-tile__icon--primary',
      success: 'statistics-tile__icon--success',
      warning: 'statistics-tile__icon--warning',
      error: 'statistics-tile__icon--error',
      accent: 'statistics-tile__icon--accent',
      secondary: 'statistics-tile__icon--secondary'
    };
    return colorClasses[colorType] || colorClasses.primary;
  };

  const getTrendClass = (trendType) => {
    const trendClasses = {
      up: 'statistics-tile__trend--up',
      down: 'statistics-tile__trend--down',
      neutral: 'statistics-tile__trend--neutral'
    };
    return trendClasses[trendType] || trendClasses.neutral;
  };

  const getTrendIcon = (trendType) => {
    const trendIcons = {
      up: 'TrendingUp',
      down: 'TrendingDown',
      neutral: 'Minus'
    };
    return trendIcons[trendType] || trendIcons.neutral;
  };

  // Simple sparkline SVG
  const renderSparkline = () => {
    if (!sparklineData || sparklineData.length === 0) return null;

    const max = Math.max(...sparklineData);
    const min = Math.min(...sparklineData);
    const range = max - min || 1;
    
    const points = sparklineData.map((value, index) => {
      const x = (index / (sparklineData.length - 1)) * 60;
      const y = 20 - ((value - min) / range) * 20;
      return `${x},${y}`;
    }).join(' ');

    return (
      <svg width="60" height="20" className="statistics-tile__sparkline">
        <polyline
          points={points}
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  };

  const handleClick = () => {
    if (onClick && typeof onClick === 'function') {
      onClick(id);
    }
  };

  return (
    <div
      onClick={handleClick}
      className={`statistics-tile${onClick ? ' statistics-tile--clickable' : ''}`}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      } : undefined}
    >
      <div className="statistics-tile__header">
        <div className={`statistics-tile__icon ${getColorClass(color)}`}>
          <Icon name={icon} size={20} />
        </div>
        {change && (
          <div className={`statistics-tile__trend ${getTrendClass(trend)}`}>
            <Icon 
              name={getTrendIcon(trend)} 
              size={14} 
            />
            <span className="statistics-tile__trend-value">{change}</span>
          </div>
        )}
      </div>

      <div className="statistics-tile__content">
        <h3 className="statistics-tile__title">{title}</h3>
        <div className="statistics-tile__value-container">
          <p className="statistics-tile__value">
            {realTimeValue || value}
          </p>
          {realTimeValue && realTimeValue !== value && (
            <span className="statistics-tile__update-indicator">
              Updated
            </span>
          )}
        </div>
      </div>

      <div className="statistics-tile__footer">
        <p className="statistics-tile__description">{description}</p>
        <div className={`statistics-tile__sparkline-container ${getColorClass(color)}`}>
          {renderSparkline()}
        </div>
      </div>

      {/* Hover effect indicator */}
      {onClick && (
        <div className="statistics-tile__hover-indicator">
          <div className="statistics-tile__hover-content">
            <span>View details</span>
            <Icon name="ArrowRight" size={12} />
          </div>
        </div>
      )}
    </div>
  );
};

export default StatisticsTile;

/*
Usage Example:

import StatisticsTile from '@/components/Recipes/RecipeDashboard/StatisticsTile';

const sampleData = {
  id: 'total-recipes',
  title: 'Total Recipes',
  value: '1,234',
  change: '+12%',
  trend: 'up',
  sparklineData: [10, 15, 12, 18, 22, 16, 25, 20, 28],
  icon: 'ChefHat',
  color: 'primary',
  description: 'Published recipes',
  onClick: (id) => console.log('Clicked:', id),
  realTimeValue: '1,245'
};

function MyComponent() {
  return (
    <div className="stats-grid">
      <StatisticsTile {...sampleData} />
    </div>
  );
}
*/
