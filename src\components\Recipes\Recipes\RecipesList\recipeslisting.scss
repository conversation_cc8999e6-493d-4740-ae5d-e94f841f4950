.recipes-listing {
  height: 100%;
  overflow: auto;
  // background-color: var(--color-secondary);

  &__main {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
  &__main--public {
    padding: var(--spacing-lg) var(--spacing-none) var(--spacing-none);
  }
  // &__container {
  // max-width: 1280px;
  // margin: 0 auto;
  // padding: 0 var(--spacing-lg);
  // padding-top: var(--spacing-2xl);
  // padding-bottom: var(--spacing-2xl);

  // @media (max-width: 768px) {
  //   padding: 0 var(--spacing-sm);
  //   padding-top: var(--spacing-lg);
  //   padding-bottom: var(--spacing-lg);
  // }

  // @media (max-width: 1024px) {
  //   padding: 0 var(--spacing-lg);
  //   padding-top: var(--spacing-xl);
  //   padding-bottom: var(--spacing-xl);
  // }
  // }

  // Header Section
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xsm) var(--spacing-md);
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
  }

  // Search Section
  &__search-section {
    width: 100%;
    display: flex;
    gap: var(--spacing-md);
  }

  // Categories Section
  &__categories {
    margin-bottom: var(--spacing-lg);
  }

  &__category-chips {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    overflow-x: auto;
    padding-bottom: var(--spacing-xs);

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: var(--color-light-gray);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-primary);
      border-radius: 2px;
    }
  }

  &__category-chip {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--btn-padding-sm);
    border-radius: var(--border-radius-full);
    white-space: nowrap;
    border: 1px solid var(--border-color-light-gray);
    background-color: var(--color-white);
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family-primary);
    cursor: pointer;

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border-color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--color-white);
      border-color: var(--color-primary);
    }

    &--loading {
      pointer-events: none;
      background-color: #f5f5f5;
      border-color: #e0e0e0;

      .skeleton-icon {
        width: 16px;
        height: 16px;
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 2px;
      }

      .skeleton-text {
        width: 60px;
        height: 14px;
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 2px;
      }
    }

    &-remove {
      margin-left: var(--spacing-xxs);
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }
  }
  &__category-chip-icon {
    width: 16px;
    height: 16px;
    // margin-right: var(--spacing-xs);
  }

  &__category-loading {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  &__category-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--color-primary-opacity);
    color: var(--color-primary);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family-primary);
    white-space: nowrap;
  }

  // Controls Section
  &__controls {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-lg);

    @media (min-width: 640px) {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 0;
    }
  }

  &__controls-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    width: 100%;
  }

  &__controls-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    width: 30%;
    justify-content: flex-end;
  }

  // Sort Section
  &__sort-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  &__sort-icon {
    color: var(--text-color-slate-gray);
  }

  &__sort-select {
    border: 1px solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    background-color: var(--color-white);
    color: var(--text-color-primary);

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  // View Toggle
  &__view-toggle {
    display: flex;
    align-items: center;
    background-color: var(--color-white);
    border: 1px solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xxs);
  }

  &__view-btn {
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    background: transparent;
    border: none;
    color: var(--text-color-slate-gray);
    cursor: pointer;

    &:hover {
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--color-white);
    }
  }

  // Results Count
  &__results-count {
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
  }

  // Filter Button
  &__filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--btn-padding-sm);
    border-radius: var(--btn-icon-border-radius);
    border: var(--normal-sec-border);
    background-color: var(--color-white);
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    cursor: pointer;

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border-color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--color-white);
      border-color: var(--color-primary);
    }
  }

  // Content Section
  &__content {
    display: flex;
    gap: var(--spacing-lg);
  }

  &__grid-container {
    flex: 1;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
