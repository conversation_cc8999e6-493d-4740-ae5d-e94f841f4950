'use client';
import {
  Box,
  IconButton,
  Typography,
  Divider,
  useTheme,
  useMediaQuery,
  Tooltip,
} from '@mui/material';
import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Ticket from './Ticket';
import TicketDetails from './TicketDetails';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import CheckIcon from '@mui/icons-material/Check';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CustomSelect from '@/components/UI/CustomSelect';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import NoDataView from '@/components/UI/NoDataView';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import './supportticket.scss';

export default function SupportTicket() {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const [selectedStatus, setSelectedStatus] = useState('open');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  const [searchValue, setSearchValue] = useState('');

  // Filter related state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState(['search']);
  const [filterData, setFilterData] = useState({
    status: '',
    priority: '',
    assignee: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    priority: '',
    assignee: '',
    searchValue: '',
  });

  // Filter configuration
  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: [
          { label: 'Open', value: 'open' },
          { label: 'Escalated', value: 'escalated' },
          { label: 'In-Progress', value: 'in_progress' },
          { label: 'Invoiced', value: 'invoiced' },
          { label: 'On Hold', value: 'on_hold' },
          { label: 'QA Review', value: 'qa_review' },
          { label: 'Assigned', value: 'assigned' },
          { label: 'Under Review', value: 'under_review' },
          { label: 'Closed', value: 'closed' },
        ],
        permission: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        options: [
          { label: 'Low', value: 'low' },
          { label: 'Medium', value: 'medium' },
          { label: 'High', value: 'high' },
          { label: 'Critical', value: 'critical' },
        ],
        permission: true,
      },
      {
        key: 'assignee',
        label: 'Assignee',
        options: [
          { label: 'Unassigned', value: 'unassigned' },
          { label: 'John Doe', value: 'john_doe' },
          { label: 'Jane Smith', value: 'jane_smith' },
        ],
        permission: true,
      },
    ],
    []
  );

  const handleStatusChange = (event) => {
    setSelectedStatus(event?.target?.value);
  };

  const toggleDrawer = () => {
    setIsDrawerOpen((prev) => !prev);
  };

  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
  };

  // Filter functions
  const toggleFilter = (filterKey) => {
    setSelectedFilters((prev) =>
      prev.includes(filterKey)
        ? prev.filter((key) => key !== filterKey)
        : [...prev, filterKey]
    );
  };

  const saveLayout = () => {
    setFilterDataApplied({ ...filterData, searchValue });
  };

  const getFirstFourFilters = () => {
    return selectedFilters.slice(0, 4);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      saveLayout();
    }
  };

  const handleCreateTicket = () => {
    router.push('/org/add-ticket');
  };

  const handleApplyFilter = () => {
    setFilterDataApplied({ ...filterData, searchValue });
    setOpenFilterDrawer(false);
  };

  const handleClearFilter = () => {
    setFilterData({
      status: '',
      priority: '',
      assignee: '',
    });
    setSearchValue('');
    setFilterDataApplied({
      status: '',
      priority: '',
      assignee: '',
      searchValue: '',
    });
  };
  return (
    <>
      <Box className="support-ticket-section-wrapper">
        <Box className="support-ticket-section-left">
          <Box className="tickets">
            <Ticket
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              handleStatusChange={handleStatusChange}
              onTicketClick={handleTicketClick}
            />
          </Box>
        </Box>
        <Box className="support-ticket-section-right">
          <Box className="show-tickets-wrap">
            {/* Header with Support Tickets text and Create Ticket button */}
            <Box className="recipe-category-filter-wrap">
              <Box className="section-right-title">
                <Typography className="sub-header-text">
                  Support Tickets
                </Typography>
              </Box>
              <Box className="mr8 pr4">
                <CustomButton
                  title="Create Ticket"
                  startIcon={<AddIcon />}
                  onClick={handleCreateTicket}
                />
              </Box>
            </Box>
            <Divider />

            {/* Filter section like AllergenList */}
            <Box className="section-right-content support-ticket-container">
              <Box className="search-section-wrap">
                {!isMobile &&
                  selectedFilters?.map((key) => {
                    const filter = filters?.find((f) => f?.key === key);
                    return filter?.permission ? (
                      <React.Fragment key={key}>
                        {key === 'search' ? (
                          <Box className="search-section-fields">
                            <CustomSearch
                              fullWidth
                              setSearchValue={setSearchValue}
                              onKeyPress={handleKeyPress}
                              searchValue={searchValue}
                            />
                          </Box>
                        ) : (
                          <Box className="search-section-fields">
                            <CustomSelect
                              placeholder={filter?.label}
                              options={filter?.options}
                              value={
                                filter?.options?.find((opt) => {
                                  return opt?.value === filterData[key];
                                }) || ''
                              }
                              onChange={(e) =>
                                setFilterData({
                                  ...filterData,
                                  [key]: e?.value,
                                })
                              }
                              menuPortalTarget={document.body}
                              styles={{
                                menuPortal: (base) => ({
                                  ...base,
                                  zIndex: 9999,
                                }),
                              }}
                            />
                          </Box>
                        )}
                      </React.Fragment>
                    ) : null;
                  })}

                {!isMobile && (
                  <>
                    <Box>
                      <CustomButton
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Apply Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <CheckIcon />
                          </Tooltip>
                        }
                        onClick={handleApplyFilter}
                      />
                    </Box>
                    <Box>
                      <CustomButton
                        variant="outlined"
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Clear Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <ClearOutlinedIcon />
                          </Tooltip>
                        }
                        onClick={handleClearFilter}
                      />
                    </Box>
                  </>
                )}
                <Box>
                  <CustomButton
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            Filters
                          </Typography>
                        }
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <FilterListIcon />
                      </Tooltip>
                    }
                    onClick={() => {
                      setOpenFilterDrawer(true);
                    }}
                  />
                </Box>
              </Box>

              <Box className="ticket-container pt32">
                <Box className="menu-icon-wrap">
                  <MenuIcon
                    onClick={toggleDrawer}
                    className="drawer-toggle-icon"
                  />
                </Box>
                <Box className="tickets-details">
                  {selectedTicket ? (
                    <TicketDetails ticket={selectedTicket} />
                  ) : (
                    <Box className="no-data d-flex align-center justify-center">
                      <NoDataView
                        title="No Ticket Selected"
                        description="Please select a ticket from the left panel to view its details, or create a new ticket."
                      />
                    </Box>
                  )}
                </Box>
              </Box>
              <Box className="support-ticket-container">
                <Box className={`drawer ${isDrawerOpen ? 'open' : ''}`}>
                  <Box className="drawer-content">
                    <IconButton className="close-icon" onClick={toggleDrawer}>
                      <CloseIcon />
                    </IconButton>
                    <Ticket
                      selectedStatus={selectedStatus}
                      setSelectedStatus={setSelectedStatus}
                      handleStatusChange={handleStatusChange}
                      onTicketClick={handleTicketClick}
                    />
                  </Box>
                </Box>
              </Box>
              <Box className="drawer-wrap">
                <RightDrawer
                  className="ticket-filter-drawer"
                  anchor="right"
                  open={openFilterDrawer}
                  onClose={() => setOpenFilterDrawer(false)}
                  title="Ticket Filter"
                  content={
                    <FilterComponent
                      filters={filters}
                      filterData={filterData}
                      setFilterData={setFilterData}
                      selectedFilters={selectedFilters}
                      toggleFilter={toggleFilter}
                      saveLayout={saveLayout}
                      setOpenFilterDrawer={setOpenFilterDrawer}
                      setSelectedFilters={setSelectedFilters}
                      getFirstFourFilters={getFirstFourFilters}
                      setSearchValue={setSearchValue}
                      searchValue={searchValue}
                      handleKeyPress={handleKeyPress}
                      isMobile={false}
                      handleApplyFilter={handleApplyFilter}
                      handleClearFilter={handleClearFilter}
                    />
                  }
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}
