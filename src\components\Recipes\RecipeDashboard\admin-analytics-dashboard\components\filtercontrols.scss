.filter-controls {
  background-color: var(--color-white);
  border: var(--border-width-xs) solid #e5e7eb;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);

  // Header Section
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
  }

  &__header-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__icon {
    color: var(--text-color-slate-gray);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin: 0;
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin: 0;
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__clear-btn {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-danger);
    background: transparent;
    border: none;
    cursor: pointer;
    transition: color 0.15s ease-out;

    &:hover {
      color: #c53030;
    }
  }

  &__toggle-btn {
    padding: var(--spacing-sm);
    color: var(--text-color-slate-gray);
    background: transparent;
    border: none;
    cursor: pointer;
    transition: color 0.15s ease-out;

    &:hover {
      color: var(--color-primary);
    }
  }

  // Quick Filters Section
  &__quick-filters {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
  }

  &__quick-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  &__quick-select {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    transition: all 0.15s ease-out;

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  // Expanded Filters Section
  &__expanded {
    border-top: var(--border-width-xs) solid #f3f4f6;
    padding: var(--spacing-lg);
    animation: slideDown 0.2s ease-out;
  }

  &__expanded-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__filter-group {
    display: flex;
    flex-direction: column;
  }

  &__filter-label {
    display: block;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  &__filter-select {
    width: 100%;
    padding: var(--spacing-sm);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    transition: all 0.15s ease-out;

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  // Active Filters Section
  &__active-section {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: var(--border-width-xs) solid #f3f4f6;
  }

  &__active-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  &__active-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  &__active-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: var(--color-primary-opacity);
    color: var(--color-primary);
    border-radius: var(--border-radius-full);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
  }

  &__tag-remove {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    transition: color 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: var(--color-dark-blue);
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
