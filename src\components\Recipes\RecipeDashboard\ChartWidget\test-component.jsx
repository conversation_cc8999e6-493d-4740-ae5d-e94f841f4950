'use client';

import React from 'react';
import ChartWidget from './ChartWidget';

// Sample data for testing
const sampleLineData = [
  { name: 'Jan', views: 400, likes: 240 },
  { name: 'Feb', views: 300, likes: 139 },
  { name: 'Mar', views: 200, likes: 980 },
  { name: 'Apr', views: 278, likes: 390 },
  { name: 'May', views: 189, likes: 480 },
];

const sampleBarData = [
  { name: 'Week 1', recipes: 20, views: 400 },
  { name: 'Week 2', recipes: 30, views: 300 },
  { name: 'Week 3', recipes: 25, views: 200 },
  { name: 'Week 4', recipes: 35, views: 278 },
];

const sampleHeatmapData = [
  { hour: '9AM', day: 'Mon', value: 80 },
  { hour: '10AM', day: 'Mon', value: 60 },
  { hour: '11AM', day: 'Mon', value: 90 },
  { hour: '12PM', day: 'Mon', value: 70 },
];

const sampleFunnelData = [
  { stage: 'Views', percentage: 100, count: 1000 },
  { stage: 'Clicks', percentage: 75, count: 750 },
  { stage: 'Likes', percentage: 50, count: 500 },
  { stage: 'Shares', percentage: 25, count: 250 },
];

// Test component to verify ChartWidget functionality
const ChartWidgetTest = () => {
  return (
    <div style={{ padding: '20px', display: 'grid', gap: '20px' }}>
      <h1>ChartWidget Component Test</h1>
      
      <div>
        <h2>Line Chart</h2>
        <ChartWidget
          title="Recipe Views & Likes"
          type="line"
          data={sampleLineData}
          dateRange="7days"
        />
      </div>

      <div>
        <h2>Bar Chart</h2>
        <ChartWidget
          title="Weekly Recipe Stats"
          type="bar"
          data={sampleBarData}
          dateRange="30days"
        />
      </div>

      <div>
        <h2>Heatmap</h2>
        <ChartWidget
          title="Activity Heatmap"
          type="heatmap"
          data={sampleHeatmapData}
          dateRange="7days"
        />
      </div>

      <div>
        <h2>Funnel Chart</h2>
        <ChartWidget
          title="User Engagement Funnel"
          type="funnel"
          data={sampleFunnelData}
          dateRange="quarter"
        />
      </div>
    </div>
  );
};

export default ChartWidgetTest;
