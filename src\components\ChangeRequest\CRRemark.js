'use client';

import React, { useEffect, useContext, useState } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import {
  DateFormat,
  getImageName,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { changeRequestService } from '@/services/changeRequestService';
import ContentLoader from '../UI/ContentLoader';
import NoDataView from '../UI/NoDataView';
import UserDetailsView from './UserDetailsVIew/UserDetailsView';
import CRHistoryDetails from './CRHistoryDetails/CRHistoryDetails';

const CRRemark = ({ crId }) => {
  const router = useRouter();
  const { userdata, setUserdata } = useContext(AuthContext);

  const [loader, setLoader] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [crData, setCrData] = useState();

  // Function to extract image name from full path

  const handleChangeAccordion = (panel) => {
    expanded === panel ? setExpanded() : setExpanded(panel);
  };

  // Change request reamark
  const handleCRRequest = async (status, remarks) => {
    setLoader(true);
    let request = {
      change_request_status: status,
      ...(remarks && { change_request_remark: remarks }),
    };
    try {
      const { status, data } = await changeRequestService.updateCRStatus(
        crId,
        request
      );
      if (status === 200) {
        setLoader(false);
        if (data.status) {
          setLoader(false);
          setApiMessage('success', data?.message);
          router.push('/change-request');
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Change request data by Id
  const getCRData = async () => {
    setLoader(true);
    try {
      const { status, data } = await changeRequestService.getCRById(crId);
      if (status === 200) {
        setLoader(false);
        setCrData(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setCrData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleRequestStatus = (status) => {
    return (
      <>
        {status === 'reopened' ? (
          <span className="sub-title-text  status-yellow ">Re-opened</span>
        ) : status === 'pending' ? (
          <span className="sub-title-text draft  text-capital">{status}</span>
        ) : status === 'approved' ? (
          <span className="sub-title-text active-onboarding  text-capital">
            {status}
          </span>
        ) : status === 'closed' ? (
          <span className="sub-title-text closed  text-capital">{status}</span>
        ) : status === 'rejected' ||
          status === 'deleted' ||
          status === 'cancelled' ? (
          <span className="sub-title-text failed  text-capital">{status}</span>
        ) : (
          <span className="sub-title-text success  text-capital">{status}</span>
        )}
      </>
    );
  };

  const oldCR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldCR?.IsFromUser === undefined && oldCR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldCR,
        IsFromUser: true,
      });
    }
  }, [oldCR]);
  useEffect(() => {
    if (crId) {
      getCRData();
    }
  }, [crId]);
  const ActionList =
    crData?.change_request_status === 'pending' ||
    crData?.change_request_status === 'rejected' ||
    crData?.change_request_status === 'reopened'
      ? identifiers?.CHANGE_REQUEST_UPDATE_OPTION
      : identifiers?.CHANGE_REQUEST_UPDATE_OPTIONS;

  const history =
    crData &&
    crData?.request_history &&
    crData?.request_history?.length > 0 &&
    crData?.request_history.filter((f, index) => index !== 0);

  return (
    <>
      <Box className="change-request">
        <Box className="d-flex align-center mb8">
          <ArrowBackIosIcon
            className="cursor-pointer"
            onClick={() => {
              setTimeout(() => {
                router?.push('/change-request');
              }, 1000);
            }}
          />
          <Typography className="body-text fw600 pr8">
            Remark Change Request
          </Typography>
        </Box>
        <Divider className="mb16 mt16" />
        {loader ? (
          <ContentLoader />
        ) : !crData || crData.length === 0 ? (
          <NoDataView
            title="No Change Request Found"
            description="There is no Change Request available at the moment."
          />
        ) : (
          <>
            <Box className="d-flex pt4">
              <UserDetailsView
                userData={crData?.change_request_user}
                handleRequestStatus={handleRequestStatus}
                createdAt={crData?.createdAt}
                status={crData?.change_request_status}
              />
            </Box>
            <Box className="pt32">
              <CRHistoryDetails
                cdata={crData}
                crData={crData}
                getImageName={getImageName}
              />
            </Box>

            <Formik
              initialValues={{
                status:
                  // singleList?.resignation_status &&
                  // singleList?.resignation_status !== 'pending'
                  //   ? singleList?.resignation_status
                  //   :
                  '',
                remark:
                  // singleList?.approved_by_reason
                  //   ? singleList?.approved_by_reason
                  //   :
                  '',
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                status: Yup.string().trim().required('This field is required'),
                remark: Yup.string().test(
                  'required-if-yes',
                  'This field is required',
                  function (value) {
                    const status = this.parent.status;
                    if (status === 'approved' || status === 'rejected') {
                      return !!value;
                    }
                    return true;
                  }
                ),
              })}
              onSubmit={async (requestData) => {
                handleCRRequest(requestData?.status, requestData?.remark);
              }}
            >
              {({
                errors,
                touched,
                // handleBlur,
                values,
                handleSubmit,
                handleChange,
                setFieldValue,
                // dirty,
                // isValid,
              }) => (
                <Form onSubmit={handleSubmit}>
                  {(crData?.change_request_status === 'pending' ||
                    crData?.change_request_status === 'rejected' ||
                    crData?.change_request_status === 'reopened' ||
                    crData?.change_request_status === 'approved') && (
                    <>
                      <Box className="pt16 pb16 remark-width">
                        <CustomSelect
                          required
                          name="status"
                          placeholder="Status"
                          label="Status"
                          options={ActionList}
                          value={
                            ActionList?.find((opt) => {
                              return opt?.value === values?.status;
                            }) || ''
                          }
                          error={touched?.status && errors?.status}
                          helperText={touched?.status && errors?.status}
                          onChange={(selectedOption) =>
                            setFieldValue('status', selectedOption?.value || '')
                          }
                        />
                      </Box>
                      {(values?.status === 'approved' ||
                        values?.status === 'rejected') && (
                        <Box className="pb32">
                          <CustomTextField
                            required
                            name="remark"
                            multiline
                            rows={3}
                            onChange={handleChange}
                            fullWidth
                            placeholder="Remark"
                            value={values?.remark}
                            error={Boolean(touched.remark && errors.remark)}
                            helperText={touched.remark && errors.remark}
                            label="Remark"
                          />
                        </Box>
                      )}
                      <Box className="d-flex justify-end">
                        <CustomButton
                          type="submit"
                          variant="contained"
                          title={loader ? 'Updating...' : 'Update'}
                          disabled={loader}
                        />
                      </Box>
                    </>
                  )}
                </Form>
              )}
            </Formik>

            <Box className="pt24">
              <Box className="">
                {history &&
                  history?.length > 0 &&
                  history?.map((cdata, index) => {
                    return (
                      <Accordion
                        // key={1}
                        elevation={0}
                        className="cr-accordion"
                        expanded={expanded === index}
                        onChange={() => {
                          handleChangeAccordion(index);
                        }}
                      >
                        <AccordionSummary
                          expandIcon={<KeyboardArrowDownIcon />}
                          className="accordion-heading"
                        >
                          <Box className="subject-section gap-sm">
                            <Typography className="body-text fw600 subject-text text-ellipsis-line">
                              {cdata?.change_request_subject}
                            </Typography>
                            <Box className="d-flex align-center justify-center h100">
                              {handleRequestStatus(
                                cdata?.change_request_status
                              )}
                            </Box>
                            <Typography className="title-text date-text">
                              {DateFormat(cdata?.createdAt, 'datesWithhour')}
                            </Typography>
                          </Box>
                        </AccordionSummary>
                        <AccordionDetails className="">
                          <CRHistoryDetails
                            cdata={cdata}
                            crData={crData}
                            getImageName={getImageName}
                          />
                        </AccordionDetails>
                      </Accordion>
                    );
                  })}
              </Box>
            </Box>
          </>
        )}
      </Box>
    </>
  );
};

export default CRRemark;
