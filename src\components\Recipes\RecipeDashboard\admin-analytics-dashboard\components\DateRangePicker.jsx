// src/pages/admin-analytics-dashboard/components/DateRangePicker.jsx
import React, { useState, useRef, useEffect } from 'react';
import Icon from 'components/AppIcon';

const DateRangePicker = ({ value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [customRange, setCustomRange] = useState({
    startDate: '',
    endDate: ''
  });
  const dropdownRef = useRef(null);

  const presetRanges = [
    { value: '7days', label: 'Last 7 days', description: 'Recent week activity' },
    { value: '30days', label: 'Last 30 days', description: 'Monthly overview' },
    { value: 'quarter', label: 'Last quarter', description: 'Quarterly analysis' },
    { value: 'year', label: 'Last year', description: 'Annual performance' },
    { value: 'custom', label: 'Custom range', description: 'Select specific dates' }
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleRangeSelect = (rangeValue) => {
    if (rangeValue === 'custom') {
      // Keep dropdown open for custom range selection
      return;
    }
    
    onChange(rangeValue);
    setIsOpen(false);
  };

  const handleCustomRangeSubmit = () => {
    if (customRange.startDate && customRange.endDate) {
      onChange({
        type: 'custom',
        startDate: customRange.startDate,
        endDate: customRange.endDate
      });
      setIsOpen(false);
    }
  };

  const getSelectedLabel = () => {
    if (typeof value === 'object' && value.type === 'custom') {
      return `${value.startDate} - ${value.endDate}`;
    }
    
    const preset = presetRanges.find(range => range.value === value);
    return preset ? preset.label : 'Select range';
  };

  const formatDateForInput = (date) => {
    return date.toISOString().split('T')[0];
  };

  const getDefaultDates = () => {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    return {
      startDate: formatDateForInput(lastWeek),
      endDate: formatDateForInput(today)
    };
  };

  // Initialize custom range with default dates
  useEffect(() => {
    if (!customRange.startDate && !customRange.endDate) {
      setCustomRange(getDefaultDates());
    }
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-surface border border-gray-300 rounded-lg hover:border-primary focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
      >
        <Icon name="Calendar" size={16} className="text-text-secondary" />
        <span className="text-sm font-medium text-text-primary">
          {getSelectedLabel()}
        </span>
        <Icon 
          name={isOpen ? "ChevronUp" : "ChevronDown"} 
          size={16} 
          className="text-text-secondary" 
        />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-surface border border-gray-200 rounded-lg shadow-modal z-50 animate-slide-down">
          <div className="p-4">
            <h4 className="font-medium text-text-primary mb-3">Select Date Range</h4>
            
            {/* Preset Ranges */}
            <div className="space-y-2 mb-4">
              {presetRanges.slice(0, -1).map((range) => (
                <button
                  key={range.value}
                  onClick={() => handleRangeSelect(range.value)}
                  className={`w-full text-left p-3 rounded-lg transition-smooth hover:bg-primary-50 ${
                    value === range.value ? 'bg-primary-50 text-primary border border-primary-200' : 'hover:text-primary'
                  }`}
                >
                  <div className="font-medium text-sm">{range.label}</div>
                  <div className="text-xs text-text-secondary">{range.description}</div>
                </button>
              ))}
            </div>

            {/* Custom Range */}
            <div className="border-t border-gray-100 pt-4">
              <h5 className="font-medium text-sm text-text-primary mb-3">Custom Range</h5>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-text-secondary mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={customRange.startDate}
                    onChange={(e) => setCustomRange(prev => ({ ...prev, startDate: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
                  />
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-text-secondary mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={customRange.endDate}
                    onChange={(e) => setCustomRange(prev => ({ ...prev, endDate: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
                  />
                </div>
                
                <button
                  onClick={handleCustomRangeSubmit}
                  disabled={!customRange.startDate || !customRange.endDate}
                  className="w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-smooth disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Apply Custom Range
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker;