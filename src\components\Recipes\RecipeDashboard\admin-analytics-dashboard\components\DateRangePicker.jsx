'use client';

import React, { useState, useRef, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import './daterangepicker.scss';

const DateRangePicker = ({ value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [customRange, setCustomRange] = useState({
    startDate: '',
    endDate: '',
  });
  const dropdownRef = useRef(null);

  const presetRanges = [
    {
      value: '7days',
      label: 'Last 7 days',
      description: 'Recent week activity',
    },
    { value: '30days', label: 'Last 30 days', description: 'Monthly overview' },
    {
      value: 'quarter',
      label: 'Last quarter',
      description: 'Quarterly analysis',
    },
    { value: 'year', label: 'Last year', description: 'Annual performance' },
    {
      value: 'custom',
      label: 'Custom range',
      description: 'Select specific dates',
    },
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    const handleEscapeKey = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen]);

  const handleRangeSelect = (rangeValue) => {
    if (rangeValue === 'custom') {
      // Keep dropdown open for custom range selection
      return;
    }

    onChange(rangeValue);
    setIsOpen(false);
  };

  const handleCustomRangeSubmit = () => {
    if (customRange.startDate && customRange.endDate) {
      onChange({
        type: 'custom',
        startDate: customRange.startDate,
        endDate: customRange.endDate,
      });
      setIsOpen(false);
    }
  };

  const getSelectedLabel = () => {
    if (typeof value === 'object' && value.type === 'custom') {
      return `${value.startDate} - ${value.endDate}`;
    }

    const preset = presetRanges.find((range) => range.value === value);
    return preset ? preset.label : 'Select range';
  };

  const formatDateForInput = (date) => {
    return date.toISOString().split('T')[0];
  };

  const getDefaultDates = () => {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    return {
      startDate: formatDateForInput(lastWeek),
      endDate: formatDateForInput(today),
    };
  };

  // Initialize custom range with default dates
  useEffect(() => {
    if (!customRange.startDate && !customRange.endDate) {
      setCustomRange(getDefaultDates());
    }
  }, []);

  return (
    <div className="date-range-picker" ref={dropdownRef}>
      <CustomButton
        onClick={() => setIsOpen(!isOpen)}
        className="date-range-picker__trigger"
        variant="outlined"
        fullWidth
        startIcon={<Icon name="Calendar" size={16} />}
        endIcon={
          <Icon
            name={isOpen ? 'ChevronUp' : 'ChevronDown'}
            size={16}
            className={`date-range-picker__trigger-chevron ${isOpen ? 'date-range-picker__trigger-chevron--open' : ''}`}
          />
        }
      >
        <span className="date-range-picker__trigger-text">
          {getSelectedLabel()}
        </span>
      </CustomButton>

      {isOpen && (
        <div className="date-range-picker__dropdown">
          <div className="date-range-picker__content">
            <h4 className="date-range-picker__title">Select Date Range</h4>

            {/* Preset Ranges */}
            <div className="date-range-picker__presets">
              {presetRanges.slice(0, -1).map((range) => (
                <CustomButton
                  key={range.value}
                  onClick={() => handleRangeSelect(range.value)}
                  className={`date-range-picker__preset-option${
                    value === range.value
                      ? ' date-range-picker__preset-option--active'
                      : ''
                  }`}
                  variant={value === range.value ? 'contained' : 'outlined'}
                  fullWidth
                  startIcon={<Icon name="Calendar" size={14} />}
                >
                  <div className="date-range-picker__preset-content">
                    <div className="date-range-picker__preset-label">
                      {range.label}
                    </div>
                    <div className="date-range-picker__preset-description">
                      {range.description}
                    </div>
                  </div>
                </CustomButton>
              ))}
            </div>

            {/* Custom Range */}
            <div className="date-range-picker__custom-section">
              <h5 className="date-range-picker__custom-title">Custom Range</h5>

              <div className="date-range-picker__custom-inputs">
                <div className="date-range-picker__input-group">
                  <label className="date-range-picker__input-label">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={customRange.startDate}
                    onChange={(e) =>
                      setCustomRange((prev) => ({
                        ...prev,
                        startDate: e.target.value,
                      }))
                    }
                    className="date-range-picker__date-input"
                  />
                </div>

                <div className="date-range-picker__input-group">
                  <label className="date-range-picker__input-label">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={customRange.endDate}
                    onChange={(e) =>
                      setCustomRange((prev) => ({
                        ...prev,
                        endDate: e.target.value,
                      }))
                    }
                    className="date-range-picker__date-input"
                  />
                </div>

                <CustomButton
                  onClick={handleCustomRangeSubmit}
                  disabled={!customRange.startDate || !customRange.endDate}
                  className="date-range-picker__apply-btn"
                  variant="contained"
                  fullWidth
                  startIcon={<Icon name="Check" size={16} />}
                >
                  Apply Custom Range
                </CustomButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker;
