'use client';
import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined';
import AddIcon from '@mui/icons-material/Add';
import CustomButton from '@/components/UI/CustomButton';
import FilterComponent from '@/components/UI/FilterComponent';
import RightDrawer from '@/components/UI/RightDrawer';
import './addticketlayout.scss';

export default function AddTicketLayout({ children }) {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  // Filter related state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState(['search']);
  const [filterData, setFilterData] = useState({
    category: '',
    priority: '',
    status: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    category: '',
    priority: '',
    status: '',
    searchValue: '',
  });
  const [searchValue, setSearchValue] = useState('');

  // Filter configuration
  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'category',
        label: 'Category',
        options: [
          { label: 'Technical Support', value: 'technical' },
          { label: 'Billing', value: 'billing' },
          { label: 'General Inquiry', value: 'general' },
          { label: 'Feature Request', value: 'feature' },
        ],
        permission: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        options: [
          { label: 'Low', value: 'low' },
          { label: 'Medium', value: 'medium' },
          { label: 'High', value: 'high' },
          { label: 'Critical', value: 'critical' },
        ],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: [
          { label: 'Draft', value: 'draft' },
          { label: 'Submitted', value: 'submitted' },
          { label: 'In Progress', value: 'in_progress' },
          { label: 'Resolved', value: 'resolved' },
        ],
        permission: true,
      },
    ],
    []
  );

  // Filter functions
  const toggleFilter = (filterKey) => {
    setSelectedFilters((prev) =>
      prev.includes(filterKey)
        ? prev.filter((key) => key !== filterKey)
        : [...prev, filterKey]
    );
  };

  const saveLayout = () => {
    setFilterDataApplied({ ...filterData, searchValue });
  };

  const getFirstFourFilters = () => {
    return selectedFilters.slice(0, 4);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      saveLayout();
    }
  };

  const handleApplyFilter = () => {
    saveLayout();
    setOpenFilterDrawer(false);
  };

  const handleClearFilter = () => {
    setFilterData({
      category: '',
      priority: '',
      status: '',
    });
    setSearchValue('');
    setFilterDataApplied({
      category: '',
      priority: '',
      status: '',
      searchValue: '',
    });
  };

  const handleCreateTicket = () => {
    // Already on create ticket page, could navigate to list or refresh
    router.push('/org/support-ticket');
  };

  return (
    <Box className="section-wrapper">
      {/* Filter Section (Left Side) - Only show on desktop */}
      {!isMobile && (
        <Box className="section-left">
          <Box className="section-left-title">
            <Typography className="sub-header-text">Filters</Typography>
          </Box>
          <Box className="side-menu-list">
            <FilterComponent
              filters={filters}
              filterData={filterData}
              setFilterData={setFilterData}
              selectedFilters={selectedFilters}
              toggleFilter={toggleFilter}
              saveLayout={saveLayout}
              setOpenFilterDrawer={setOpenFilterDrawer}
              setSelectedFilters={setSelectedFilters}
              getFirstFourFilters={getFirstFourFilters}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              handleKeyPress={handleKeyPress}
              isMobile={false}
              handleApplyFilter={handleApplyFilter}
              handleClearFilter={handleClearFilter}
              isInSidebar={true}
            />
          </Box>
        </Box>
      )}

      {/* Main Content Section (Right Side) */}
      <Box className="section-right">
        {/* Header with Create Support Ticket text and buttons on right side */}
        <Box className="recipe-category-filter-wrap">
          <Box className="d-flex justify-space-between align-center w-100">
            <Box className="section-right-title">
              <Typography className="sub-header-text">
                Create Support Ticket
              </Typography>
            </Box>
            <Box className="d-flex gap-10 align-center">
              {/* Filter icon - only show on mobile (< 1500px) */}
              {isMobile && (
                <Box
                  className="filter-icon-btn"
                  onClick={() => setOpenFilterDrawer(true)}
                  sx={{
                    cursor: 'pointer',
                    padding: '8px',
                    borderRadius: '4px',
                    '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' },
                  }}
                >
                  <FilterAltOutlinedIcon />
                </Box>
              )}
              <CustomButton
                title="View Tickets"
                startIcon={<AddIcon />}
                onClick={handleCreateTicket}
              />
            </Box>
          </Box>
        </Box>
        <Divider />

        {/* Main Content */}
        <Box className="section-right-content">{children}</Box>
      </Box>

      {/* Mobile Filter Drawer */}
      {isMobile && (
        <RightDrawer
          anchor="right"
          open={openFilterDrawer}
          onClose={() => setOpenFilterDrawer(false)}
          title="Filters"
          className="filter-options-drawer"
          content={
            <FilterComponent
              filters={filters}
              filterData={filterData}
              setFilterData={setFilterData}
              selectedFilters={selectedFilters}
              toggleFilter={toggleFilter}
              saveLayout={saveLayout}
              setOpenFilterDrawer={setOpenFilterDrawer}
              setSelectedFilters={setSelectedFilters}
              getFirstFourFilters={getFirstFourFilters}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              handleKeyPress={handleKeyPress}
              isMobile={true}
              handleApplyFilter={handleApplyFilter}
              handleClearFilter={handleClearFilter}
            />
          }
        />
      )}
    </Box>
  );
}
