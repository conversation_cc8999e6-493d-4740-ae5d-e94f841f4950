'use client';
import React from 'react';
import { Box, Typography } from '@mui/material';
import { useDropzone } from 'react-dropzone';
import CancelIcon from '@mui/icons-material/Cancel';
import HeaderImage from '@/components/UI/ImageSecurity';
import { UploadImageIcon } from '@/helper/common/images';
import './imageupload.scss';

export default function CustomImageUploader({
  imagePreview,
  acceptFiles,
  onDropAccepted,
  onRemoveImage,
  emptyMedia = false,
  fieldErrorText = 'This field is required',
  label,
  required = false,
  placeholder = 'Drop your image here',
  uploadClassName = '',
  previewClassName = '',
}) {
  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        onDropAccepted(acceptedFiles[0]);
      }
    },
    accept: { 'image/*': [] },
    multiple: false,
  });

  const showPreview =
    imagePreview || (acceptFiles?.link && acceptFiles?.isAdded);

  return (
    <>
      {label && (
        <Typography className="other-field-label">
          {label} {required && <span className="required">*</span>}
        </Typography>
      )}

      {showPreview ? (
        <Box className={`dropzone-media-preview-container ${previewClassName}`}>
          <HeaderImage
            imageUrl={imagePreview || acceptFiles?.link}
            alt="not found"
            draggable="false"
            className="cursor-pointer"
            type="lazyload"
          />

          <CancelIcon
            className="cancel-icon cursor-pointer"
            onClick={onRemoveImage}
          />
        </Box>
      ) : (
        <Box
          className={`dropzone-media-container text-align cursor-pointer ${uploadClassName}`}
        >
          <Box
            {...getRootProps({ className: 'dropzone' })}
            // className="upload-area"
          >
            {/* <CollectionsIcon /> */}
            <UploadImageIcon />
            <input {...getInputProps()} />
            <Typography className="title-text upload-text">
              <span className="blue-text">Upload</span>
              {placeholder && ` or ${placeholder}`}
            </Typography>
          </Box>
        </Box>
      )}

      {emptyMedia && !acceptFiles && (
        <Typography className="other-field-error-text">
          {fieldErrorText}
        </Typography>
      )}
    </>
  );
}
