@import '@/app/_globals.scss';

.chart-widget-container {
  background: var(--color-white);
  border: 1px solid var(--color-light-gray);
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow, 0 1px 2px rgba(0,0,0,0.05));
}

.chart-widget-container.fullscreen {
  position: fixed;
  inset: 1rem;
  z-index: 50;
}

.chart-widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--color-light-grayish-blue);
}

.chart-widget-title {
  font-family: var(--font-family-primary);
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-color-primary);
}

.chart-widget-subtitle {
  font-size: 0.875rem;
  color: var(--text-color-muted);
}

.chart-widget-options {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.chart-widget-content {
  padding: 1rem;
  height: 20rem;
}

.chart-widget-content.fullscreen {
  height: calc(100vh - 8rem);
}

.chart-widget-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--color-light-grayish-blue);
  background: var(--color-off-white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
}

.chart-widget-legend {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chart-widget-legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-widget-legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 9999px;
}

.chart-widget-export-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-primary);
  cursor: pointer;
  background: none;
  border: none;
  transition: color 0.2s;
}

.chart-widget-export-btn:hover {
  color: var(--color-dark-blue);
}

.chart-widget-heatmap-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  height: 100%;
}

.chart-widget-heatmap-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: transform 0.2s;
}

.chart-widget-heatmap-cell:hover {
  transform: scale(1.05);
} 