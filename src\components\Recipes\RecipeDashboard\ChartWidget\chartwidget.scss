.chart-widget {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  border: var(--border-width-xs) solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;

  // Fullscreen modifier
  &--fullscreen {
    position: fixed;
    inset: var(--spacing-lg);
    z-index: 50;
    border-radius: var(--border-radius-md);
  }

  // Header Section
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid #f3f4f6;
    background-color: var(--color-white);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: #111827;
    margin-bottom: var(--spacing-tiny);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: #6b7280;
  }

  // Options and Controls
  &__options {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }
  }

  &__controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  &__option-btn {
    background: transparent;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);

    &:hover {
      color: #2D5A3D;
    }

    &--active {
      background-color: rgba(45, 90, 61, 0.1);
      color: #2D5A3D;
    }
  }

  &__fullscreen-btn {
    background: transparent;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);

    &:hover {
      color: #2D5A3D;
    }
  }

  // Content Section
  &__content {
    padding: var(--spacing-lg);
    height: 20rem;
    position: relative;

    .chart-widget--fullscreen & {
      height: calc(100vh - 8rem);
    }

    @media (max-width: 768px) {
      height: 16rem;
      padding: var(--spacing-md);
    }
  }

  // Footer Section
  &__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: var(--border-width-xs) solid #f3f4f6;
    background-color: #f9fafb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-sm);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  // Legend Section
  &__legend {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: var(--spacing-md);
    }
  }

  &__legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__legend-color {
    width: var(--spacing-md);
    height: var(--spacing-md);
    border-radius: var(--border-radius-full);
    flex-shrink: 0;
  }

  &__legend-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: #6b7280;
    font-weight: var(--font-weight-regular);
  }

  // Export Button
  &__export-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: #2D5A3D;
    cursor: pointer;
    background: transparent;
    border: none;
    transition: color 0.15s ease-out;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);

    &:hover {
      color: #1e3a28;
    }
  }

  &__export-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
  }

  // Heatmap Styles
  &__heatmap-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
    height: 100%;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__heatmap-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    transition: transform 0.15s ease-out;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
    }
  }

  &__heatmap-hour,
  &__heatmap-day {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
  }

  &__heatmap-value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    margin-top: var(--spacing-tiny);
  }

  // Funnel Chart Styles
  &__funnel-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    height: 100%;
    justify-content: center;
  }

  &__funnel-stage {
    position: relative;
  }

  &__funnel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
  }

  &__funnel-stage-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: #111827;
  }

  &__funnel-stage-percent {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
    color: #6b7280;
  }

  &__funnel-bar-bg {
    width: 100%;
    height: var(--spacing-md);
    background-color: #e5e7eb;
    border-radius: var(--border-radius-full);
    overflow: hidden;
    position: relative;
  }

  &__funnel-bar {
    height: var(--spacing-md);
    border-radius: var(--border-radius-full);
    transition: width 1s ease-out;
  }

  &__funnel-count {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: #6b7280;
    margin-top: var(--spacing-xs);
  }
}