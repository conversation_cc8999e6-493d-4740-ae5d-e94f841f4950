.chart-widget {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  border: var(--border-width-xs) solid var(--color-light-gray);
  box-shadow: var(--box-shadow-xs);
  overflow: hidden;

  // Fullscreen modifier
  &--fullscreen {
    position: fixed;
    inset: var(--spacing-lg);
    z-index: 50;
    border-radius: var(--border-radius-md);
  }

  // Header Section
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid var(--color-light-grayish-blue);
    background-color: var(--color-white);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-xl);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-tiny);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  // Options and Controls
  &__options {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }
  }

  &__controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  &__option-btn {
    background: transparent;
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    color: var(--text-color-slate-gray);
    cursor: pointer;
    transition: all 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);

    &:hover {
      background-color: var(--color-off-white);
      border-color: var(--color-primary);
      color: var(--text-color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      border-color: var(--color-primary);
      color: var(--text-color-white);
    }
  }

  &__fullscreen-btn {
    background: transparent;
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    color: var(--text-color-slate-gray);
    cursor: pointer;
    transition: all 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);

    &:hover {
      background-color: var(--color-off-white);
      border-color: var(--color-primary);
      color: var(--text-color-primary);
    }
  }

  // Content Section
  &__content {
    padding: var(--spacing-lg);
    height: 20rem;
    position: relative;

    .chart-widget--fullscreen & {
      height: calc(100vh - 12rem);
    }

    @media (max-width: 768px) {
      height: 16rem;
      padding: var(--spacing-md);
    }
  }

  // Footer Section
  &__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: var(--border-width-xs) solid var(--color-light-grayish-blue);
    background-color: var(--color-off-white);
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  // Legend Section
  &__legend {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: var(--spacing-md);
    }
  }

  &__legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__legend-color {
    width: var(--spacing-md);
    height: var(--spacing-md);
    border-radius: var(--border-radius-full);
    flex-shrink: 0;
  }

  &__legend-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    font-weight: var(--font-weight-medium);
  }

  // Export Button
  &__export-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--color-primary);
    cursor: pointer;
    background: transparent;
    border: none;
    transition: color 0.15s ease-out;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    &:hover {
      color: var(--color-dark-blue);
    }
  }

  &__export-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  // Heatmap Styles
  &__heatmap-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
    height: 100%;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__heatmap-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: transform 0.15s ease-out;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
    }
  }

  &__heatmap-hour,
  &__heatmap-day {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
  }

  &__heatmap-value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    margin-top: var(--spacing-tiny);
  }

  // Funnel Chart Styles
  &__funnel-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    height: 100%;
  }

  &__funnel-stage {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__funnel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__funnel-stage-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__funnel-stage-percent {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
  }

  &__funnel-bar-bg {
    width: 100%;
    height: var(--spacing-xxl);
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    position: relative;
  }

  &__funnel-bar {
    height: 100%;
    border-radius: var(--border-radius-sm);
    transition: width 0.3s ease-out;
  }

  &__funnel-count {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }
}