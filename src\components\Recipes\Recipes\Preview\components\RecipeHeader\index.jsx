import React, { useContext, useState } from 'react';
import { useRouter } from 'next/navigation';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import SplideThumbnailCarousel from '@/components/SplideCarousel/SplideThumbnailCarousel';
import MediaIconsCarousel from '@/components/UI/MediaIconsCarousel';
import AudioPlayer from '@/components/UI/AudioPlayer';
import NutritionCard from '../NutritionCard';
import { getCurrencySymbol } from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import Image from 'next/image';
import './RecipeHeader.scss';

const RecipeHeader = ({ recipeData, isPublicPage = false }) => {
  const router = useRouter();
  const [selectedAudio, setSelectedAudio] = useState(null);
  const { authState } = useContext(AuthContext);
  const currency = getCurrencySymbol(authState?.currency_details);

  const handleAudioClick = (audioItem) => {
    setSelectedAudio(audioItem);
  };

  const handleCloseAudio = () => {
    setSelectedAudio(null);
  };

  const handleBack = () => {
    // Determine proper back navigation based on current route
    if (isPublicPage) {
      // For public pages, go to public recipes list or home
      router.push('/recipe');
    } else {
      // For private pages, go to recipes list
      router.push('/recipes');
    }
  };

  // Helper function to get appropriate icon based on resource type
  const getIconForResourceType = (type) => {
    switch (type) {
      case 'pdf':
        return 'FileText';
      case 'audio':
        return 'Music';
      case 'youtube':
        return 'Youtube';
      case 'video':
        return 'Play';
      case 'image':
        return 'Image';
      default:
        return 'Link';
    }
  };

  // Use actual API data only
  const resourcesData = recipeData?.resources || [];

  // Helper function to check if resource is image/video for SplideThumbnailCarousel
  const isImageOrVideo = (resource) => {
    const itemType = resource?.item_detail?.item_type?.toLowerCase();
    return (
      itemType?.includes('image') ||
      itemType?.includes('video') ||
      itemType?.includes('youtube') ||
      itemType === 'video'
    );
  };

  // Helper function to check if resource is PDF/Audio/Link for MediaIconsCarousel
  const isPdfAudioOrLink = (resource) => {
    const itemType = resource?.item_detail?.item_type?.toLowerCase();

    // Check item_type specifically for PDF, Audio, and generic links (not youtube/video)
    const isPdf = itemType === 'pdf';
    const isAudio = itemType === 'audio';
    const isGenericLink =
      itemType === 'link' && itemType !== 'youtube' && itemType !== 'video';

    return isPdf || isAudio || isGenericLink;
  };

  // Filter resources for SplideThumbnailCarousel (Images & Videos only)
  const imageVideoResources = resourcesData?.filter(isImageOrVideo) || [];

  // Filter resources for MediaIconsCarousel (PDF, Audio, Links only)
  const pdfAudioLinkResources = resourcesData?.filter(isPdfAudioOrLink) || [];

  // Transform filtered resources into media data for SplideThumbnailCarousel
  const mediaData =
    imageVideoResources?.map((resource) => resource?.item_detail?.item_link) ||
    [];

  // Transform filtered resources into media icons for MediaIconsCarousel
  const mediaIcons =
    pdfAudioLinkResources?.map((resource) => {
      const icon = getIconForResourceType(resource?.item_detail?.item_type);
      const itemType = resource?.item_detail?.item_type?.toLowerCase();

      // Map the type correctly for MediaIconsCarousel component
      let mappedType = resource?.type;
      if ((itemType && itemType.includes('pdf')) || itemType.includes('/pdf')) {
        mappedType = 'document'; // MediaIconsCarousel expects 'document' for PDFs
      } else if (itemType && itemType.includes('audio')) {
        mappedType = 'audio';
      } else {
        mappedType = 'link'; // For other link types
      }
      return {
        id: resource?.id,
        type: mappedType,
        icon: icon,
        url: resource?.item_detail?.item_link,
      };
    }) || [];

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'low':
      case 'easy':
        return 'recipe-header__difficulty--low';
      case 'medium':
        return 'recipe-header__difficulty--medium';
      case 'hard':
      case 'difficult':
        return 'recipe-header__difficulty--hard';
      default:
        return 'recipe-header__difficulty--default';
    }
  };

  return (
    <div className="recipe-header">
      {/* Back button for public pages only */}
      {isPublicPage && (
        <div className="recipe-header__back-section">
          <CustomButton
            variant="outlined"
            title="Back"
            leftIcon={<Icon name="ArrowLeft" size={16} />}
            onClick={handleBack}
            className="recipe-header__back-btn"
          />
        </div>
      )}
      <div className="recipe-header__content">
        <div className="recipe-header__info">
          {/* Show media only if enabled in settings for public pages, always show for private pages, and only if data exists */}
          {(!isPublicPage ||
            recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
              ?.media) &&
            mediaData &&
            mediaData.length > 0 && (
              <>
                <SplideThumbnailCarousel
                  media={mediaData}
                  thumbPosition="right"
                />

                {selectedAudio && (
                  <AudioPlayer
                    audioUrl={selectedAudio.url}
                    onClose={handleCloseAudio}
                  />
                )}
              </>
            )}

          {/* Show links (media icons) only if enabled in settings for public pages, always show for private pages, and only if data exists */}
          {(!isPublicPage ||
            recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
              ?.links) &&
            mediaIcons &&
            mediaIcons.length > 0 && (
              <MediaIconsCarousel
                mediaIcons={mediaIcons}
                onAudioClick={handleAudioClick}
              />
            )}

          <div className="recipe-header__info-content">
            {/* Show nutritional information only if enabled in settings for public pages, always show for private pages */}
            {(!isPublicPage ||
              recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
                ?.nutritionalInformation) && (
              <NutritionCard recipeData={recipeData} />
            )}
          </div>
        </div>

        <div className="recipe-header__stats">
          <div className="recipe-header__title-info">
            <p className="recipe-header__title">{recipeData.recipe_title}</p>
            <p className="recipe-header__subtitle">
              {recipeData?.recipe_description}
            </p>
          </div>
          <div className="recipe-header__stats-grid">
            {/* Show total time only if enabled in settings for public pages, always show for private pages, and only if value exists */}
            {(!isPublicPage ||
              recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
                ?.totalTime) &&
              recipeData?.recipe_cook_time && (
                <div className="recipe-header__stat-card recipe-header__stat-card--time">
                  <Icon
                    name="Clock"
                    size={16}
                    color="var(--color-warning)"
                    className="recipe-header__stat-icon"
                  />
                  <p className="recipe-header__stat-value">
                    {recipeData?.recipe_cook_time} min
                  </p>
                  <p className="recipe-header__stat-label">Total Time</p>
                </div>
              )}

            {/* Show yield/portioning only if enabled in settings for public pages, always show for private pages, and only if value exists */}
            {(!isPublicPage ||
              recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
                ?.yieldPortioning) &&
              recipeData?.recipe_yield && (
                <div className="recipe-header__stat-card recipe-header__stat-card--portions">
                  <Icon
                    name="Users"
                    size={16}
                    color="var(--color-success)"
                    className="recipe-header__stat-icon"
                  />
                  <p className="recipe-header__stat-value">
                    {recipeData?.recipe_yield}
                  </p>
                  <p className="recipe-header__stat-label">Portions</p>
                </div>
              )}

            {/* Show cost only if enabled in settings for public pages, always show for private pages, and only if value exists */}
            {(!isPublicPage ||
              recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
                ?.yieldPortioning) &&
              recipeData?.recipe_total_portions && (
                <div className="recipe-header__stat-card recipe-header__stat-card--cost">
                  <Icon
                    name={currency}
                    size={16}
                    color="var(--color-danger)"
                    className="recipe-header__stat-icon"
                  />
                  <p className="recipe-header__stat-value">
                    {currency}
                    {recipeData?.recipe_total_portions}
                  </p>
                  <p className="recipe-header__stat-label">Per Portion</p>
                </div>
              )}

            {/* Show scale/portion size only if enabled in settings for public pages, always show for private pages, and only if value exists */}
            {(!isPublicPage ||
              recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
                ?.yieldPortioning) &&
              recipeData?.recipe_single_portion_size && (
                <div className="recipe-header__stat-card recipe-header__stat-card--size">
                  <Icon
                    name="Scale"
                    size={20}
                    color="var(--color-warning)"
                    className="recipe-header__stat-icon"
                  />
                  <p className="recipe-header__stat-value">
                    {recipeData?.recipe_single_portion_size}g
                  </p>
                  <p className="recipe-header__stat-label">Portion Size</p>
                </div>
              )}
          </div>
          {/* Show categories only if enabled in settings for public pages, always show for private pages */}
          {(!isPublicPage ||
            recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
              ?.category) && (
            <div className="recipe-header__categories">
              {recipeData.categories && recipeData.categories.length > 0 ? (
                recipeData.categories.map((category, index) => (
                  <span key={index} className="recipe-header__category-tag">
                    {category?.category_name}
                  </span>
                ))
              ) : (
                <span className="recipe-header__category-tag recipe-header__category-tag--empty">
                  No categories assigned
                </span>
              )}
            </div>
          )}
          {recipeData?.recipe_complexity_level && (
            <div
              className={`recipe-header__difficulty ${getDifficultyColor(recipeData?.recipe_complexity_level)}`}
            >
              <Icon name="ChefHat" size={18} />
              <p className="recipe-header__difficulty-title">
                {recipeData?.recipe_complexity_level}
              </p>
            </div>
          )}
          {/* Show allergen information only if enabled in settings for public pages, always show for private pages */}
          {(!isPublicPage ||
            recipeData?.organization_settings?.recipeDetailsToDisplayPublicly
              ?.allergenInformation) && (
            <div className="recipe-header__allergen-warning">
              <Icon
                name="AlertTriangle"
                size={20}
                className="recipe-header__allergen-icon"
              />
              <div>
                <p className="recipe-header__allergen-title">
                  Allergen Warning
                </p>

                {recipeData?.allergen_attributes?.contains &&
                recipeData?.allergen_attributes?.contains.length > 0 ? (
                  <div className="recipe-header__allergen-icons-wrap">
                    {recipeData.allergen_attributes.contains.map(
                      (allergen, index) => (
                        <Image
                          key={index}
                          src={allergen.item_detail.item_link}
                          alt={allergen.attribute_title}
                          width={30}
                          height={30}
                          className="recipe-header__allergen-icon-item"
                          title={allergen.attribute_title}
                        />
                      )
                    )}
                  </div>
                ) : (
                  <p className="recipe-header__allergen-text">
                    No allergens identified
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RecipeHeader;
