'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '../CustomButton';
import './restrictedModal.scss';

export default function RestrictedModal({
  handleConfirm,
  restrictedModal,
  limit = false,
}) {
  // Helper functions to extract complex logic
  const getFirstMessage = () => {
    if (
      restrictedModal?.user_status &&
      restrictedModal?.purchase_plan === false
    ) {
      return 'Your profile information is incomplete.';
    }
    if (restrictedModal?.purchase_plan === false) {
      return '';
    }
    return 'Your profile information is incomplete.';
  };

  const shouldShowLineBreaks = () => {
    if (
      restrictedModal?.user_status &&
      restrictedModal?.purchase_plan === false
    ) {
      return true;
    }
    if (restrictedModal?.purchase_plan === false) {
      return false;
    }
    return true;
  };

  const getSecondMessage = () => {
    if (
      restrictedModal?.user_status &&
      restrictedModal?.purchase_plan === false
    ) {
      return 'Complete your profile and plan details to enjoy all features.';
    }
    if (restrictedModal?.purchase_plan === false) {
      return 'Update your plan details to access more features.';
    }
    return 'Update your profile to unlock more features.';
  };

  const renderOrgViewContent = () => (
    <Typography className="restricted-text">
      {getFirstMessage()}
      {shouldShowLineBreaks() && (
        <>
          <br />
          <br />
        </>
      )}
      {getSecondMessage()}
    </Typography>
  );

  const renderDefaultContent = () => (
    <Typography className="restricted-text">
      Your profile information is incomplete.
      <br />
      <br />
      Update your profile to unlock more features.
    </Typography>
  );

  return (
    <Box className="restricted-modal-wrap">
      {limit ? (
        <Typography className="restricted-text">
          Staff creation limit exceeded.
          <br />
          <br />
          Please upgrade your plan or contact admin to add more staff members.
        </Typography>
      ) : (
        <>
          {restrictedModal?.isOrgView
            ? renderOrgViewContent()
            : renderDefaultContent()}
        </>
      )}
      <Box className="restricted-action">
        <CustomButton
          fullWidth
          className=""
          variant="contained"
          title="Ok"
          onClick={() => handleConfirm()}
        />
      </Box>
    </Box>
  );
}
