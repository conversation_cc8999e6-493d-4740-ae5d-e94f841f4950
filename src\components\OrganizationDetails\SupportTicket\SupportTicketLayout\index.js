'use client';
import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined';
import AddIcon from '@mui/icons-material/Add';
import CustomButton from '@/components/UI/CustomButton';
import FilterComponent from '@/components/UI/FilterComponent';
import RightDrawer from '@/components/UI/RightDrawer';
import SupportTicket from '../index';
import './supportticketlayout.scss';

export default function SupportTicketLayout() {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  // Filter related state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState(['search']);
  const [filterData, setFilterData] = useState({
    status: '',
    priority: '',
    assignee: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    priority: '',
    assignee: '',
    searchValue: '',
  });
  const [searchValue, setSearchValue] = useState('');

  // Filter configuration
  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: [
          { label: 'Open', value: 'open' },
          { label: 'Escalated', value: 'escalated' },
          { label: 'In-Progress', value: 'in_progress' },
          { label: 'Invoiced', value: 'invoiced' },
          { label: 'On Hold', value: 'on_hold' },
          { label: 'QA Review', value: 'qa_review' },
          { label: 'Assigned', value: 'assigned' },
          { label: 'Under Review', value: 'under_review' },
          { label: 'Closed', value: 'closed' },
        ],
        permission: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        options: [
          { label: 'Low', value: 'low' },
          { label: 'Medium', value: 'medium' },
          { label: 'High', value: 'high' },
          { label: 'Critical', value: 'critical' },
        ],
        permission: true,
      },
      {
        key: 'assignee',
        label: 'Assignee',
        options: [
          { label: 'Unassigned', value: 'unassigned' },
          { label: 'John Doe', value: 'john_doe' },
          { label: 'Jane Smith', value: 'jane_smith' },
        ],
        permission: true,
      },
    ],
    []
  );

  // Filter functions
  const toggleFilter = (filterKey) => {
    setSelectedFilters((prev) =>
      prev.includes(filterKey)
        ? prev.filter((key) => key !== filterKey)
        : [...prev, filterKey]
    );
  };

  const saveLayout = () => {
    setFilterDataApplied({ ...filterData, searchValue });
  };

  const getFirstFourFilters = () => {
    return selectedFilters.slice(0, 4);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      saveLayout();
    }
  };

  const handleApplyFilter = () => {
    saveLayout();
    setOpenFilterDrawer(false);
  };

  const handleClearFilter = () => {
    setFilterData({
      status: '',
      priority: '',
      assignee: '',
    });
    setSearchValue('');
    setFilterDataApplied({
      status: '',
      priority: '',
      assignee: '',
      searchValue: '',
    });
  };

  const handleCreateTicket = () => {
    router.push('/add-ticket');
  };

  return (
    <Box className="section-wrapper">
      {/* Filter Section (Left Side) - Only show on desktop */}
      {!isMobile && (
        <Box className="section-left">
          <Box className="section-left-title">
            <Typography className="sub-header-text">Filters</Typography>
          </Box>
          <Box className="side-menu-list">
            <FilterComponent
              filters={filters}
              filterData={filterData}
              setFilterData={setFilterData}
              selectedFilters={selectedFilters}
              toggleFilter={toggleFilter}
              saveLayout={saveLayout}
              setOpenFilterDrawer={setOpenFilterDrawer}
              setSelectedFilters={setSelectedFilters}
              getFirstFourFilters={getFirstFourFilters}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              handleKeyPress={handleKeyPress}
              isMobile={false}
              handleApplyFilter={handleApplyFilter}
              handleClearFilter={handleClearFilter}
              isInSidebar={true}
            />
          </Box>
        </Box>
      )}

      {/* Main Content Section (Right Side) */}
      <Box className="section-right">
        {/* Header with Support Tickets text and buttons on right side */}
        <Box className="recipe-category-filter-wrap">
          <Box className="d-flex justify-space-between align-center w-100">
            <Box className="section-right-title">
              <Typography className="sub-header-text">
                Support Tickets
              </Typography>
            </Box>
            <Box className="d-flex gap-10 align-center">
              {/* Filter icon - only show on mobile (< 1500px) */}
              {isMobile && (
                <Box
                  className="filter-icon-btn"
                  onClick={() => setOpenFilterDrawer(true)}
                  sx={{
                    cursor: 'pointer',
                    padding: '8px',
                    borderRadius: '4px',
                    '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' },
                  }}
                >
                  <FilterAltOutlinedIcon />
                </Box>
              )}
              <CustomButton
                title="Create Ticket"
                startIcon={<AddIcon />}
                onClick={handleCreateTicket}
              />
            </Box>
          </Box>
        </Box>
        <Divider />

        {/* Main Content */}
        <Box className="section-right-content">
          <SupportTicket />
        </Box>
      </Box>

      {/* Mobile Filter Drawer */}
      {isMobile && (
        <RightDrawer
          anchor="right"
          open={openFilterDrawer}
          onClose={() => setOpenFilterDrawer(false)}
          title="Filters"
          className="filter-options-drawer"
          content={
            <FilterComponent
              filters={filters}
              filterData={filterData}
              setFilterData={setFilterData}
              selectedFilters={selectedFilters}
              toggleFilter={toggleFilter}
              saveLayout={saveLayout}
              setOpenFilterDrawer={setOpenFilterDrawer}
              setSelectedFilters={setSelectedFilters}
              getFirstFourFilters={getFirstFourFilters}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              handleKeyPress={handleKeyPress}
              isMobile={true}
              handleApplyFilter={handleApplyFilter}
              handleClearFilter={handleClearFilter}
            />
          }
        />
      )}
    </Box>
  );
}
