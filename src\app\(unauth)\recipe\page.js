import { Box } from '@mui/material';
import PublicRecipe from '@/components/PublicRecipe';
import { generateMetadata } from '@/helper/common/commonFunctions';
import RecipeDashboard from '@/components/Recipes/RecipeDashboard';

export const metadata = generateMetadata({
  pageTitle: 'Public Recipe',
});

export default function PublicRecipePage() {
  return (
    <Box className="main-page-container">
      <PublicRecipe />
      <RecipeDashboard />
    </Box>
  );
}
