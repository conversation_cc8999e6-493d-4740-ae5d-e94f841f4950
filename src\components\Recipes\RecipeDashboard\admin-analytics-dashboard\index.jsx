'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Tooltip,
  Typography,
  Popover,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import FilterListIcon from '@mui/icons-material/FilterList';
// Note: Header and Sidebar components can be added when needed
import StatisticsTile from './components/StatisticsTile';
import ChartWidget from './components/ChartWidget';
import QuickActionPanel from './components/QuickActionPanel';
import DateRangePicker from './components/DateRangePicker';
import RecentActivity from './components/RecentActivity';
import './adminanalyticsdashboard.scss';

const AdminAnalyticsDashboard = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  const [selectedDateRange, setSelectedDateRange] = useState('30days');
  const [activeFilters, setActiveFilters] = useState({
    category: '',
    userType: '',
    region: '',
    device: '',
  });
  const [realTimeData, setRealTimeData] = useState({});

  // Filter drawer state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([
    'category',
    'userType',
    'region',
    'device',
  ]);
  const [filterData, setFilterData] = useState({
    category: '',
    userType: '',
    region: '',
    device: '',
  });
  const [searchValue, setSearchValue] = useState('');

  // Export popover state
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const id = open ? 'export-popover' : undefined;

  // Mock statistics data
  const statisticsData = [
    {
      id: 'total-recipes',
      title: 'Total Recipes',
      value: '1,234',
      change: '+12.5%',
      trend: 'up',
      sparklineData: [45, 52, 48, 61, 55, 67, 73, 69, 78, 82],
      icon: 'ChefHat',
      color: 'primary',
      description: 'Published recipes across all categories',
    },
    {
      id: 'active-users',
      title: 'Active Users',
      value: '856',
      change: '+8.3%',
      trend: 'up',
      sparklineData: [32, 38, 35, 42, 48, 45, 52, 58, 55, 61],
      icon: 'Users',
      color: 'success',
      description: 'Monthly active users',
    },
    {
      id: 'popular-categories',
      title: 'Top Category',
      value: 'Desserts',
      change: '+15.2%',
      trend: 'up',
      sparklineData: [28, 32, 35, 38, 42, 45, 48, 52, 55, 58],
      icon: 'TrendingUp',
      color: 'accent',
      description: 'Most viewed recipe category',
    },
    {
      id: 'revenue-metrics',
      title: 'Revenue',
      value: '$12,450',
      change: '+22.1%',
      trend: 'up',
      sparklineData: [15, 18, 22, 25, 28, 32, 35, 38, 42, 45],
      icon: 'DollarSign',
      color: 'warning',
      description: 'Monthly subscription revenue',
    },
    {
      id: 'growth-indicators',
      title: 'Growth Rate',
      value: '18.5%',
      change: '+3.2%',
      trend: 'up',
      sparklineData: [12, 15, 18, 21, 24, 27, 30, 33, 36, 39],
      icon: 'BarChart3',
      color: 'secondary',
      description: 'User acquisition growth',
    },
    {
      id: 'engagement-rate',
      title: 'Engagement',
      value: '74.2%',
      change: '+5.8%',
      trend: 'up',
      sparklineData: [65, 68, 70, 72, 71, 73, 75, 74, 76, 78],
      icon: 'Heart',
      color: 'error',
      description: 'User engagement rate',
    },
  ];

  // Mock chart data
  const chartWidgets = [
    {
      id: 'recipe-views',
      title: 'Recipe Views Trend',
      type: 'line',
      data: [
        { name: 'Jan', views: 4000, likes: 2400 },
        { name: 'Feb', views: 3000, likes: 1398 },
        { name: 'Mar', views: 2000, likes: 9800 },
        { name: 'Apr', views: 2780, likes: 3908 },
        { name: 'May', views: 1890, likes: 4800 },
        { name: 'Jun', views: 2390, likes: 3800 },
      ],
    },
    {
      id: 'category-performance',
      title: 'Category Performance',
      type: 'bar',
      data: [
        { name: 'Desserts', recipes: 245, views: 12500 },
        { name: 'Main Course', recipes: 189, views: 9800 },
        { name: 'Appetizers', recipes: 156, views: 7200 },
        { name: 'Beverages', recipes: 98, views: 4500 },
        { name: 'Snacks', recipes: 87, views: 3200 },
      ],
    },
    {
      id: 'user-engagement',
      title: 'User Engagement Heatmap',
      type: 'heatmap',
      data: [
        { hour: '00:00', day: 'Mon', value: 12 },
        { hour: '06:00', day: 'Mon', value: 45 },
        { hour: '12:00', day: 'Mon', value: 78 },
        { hour: '18:00', day: 'Mon', value: 92 },
        { hour: '00:00', day: 'Tue', value: 15 },
        { hour: '06:00', day: 'Tue', value: 52 },
        { hour: '12:00', day: 'Tue', value: 85 },
        { hour: '18:00', day: 'Tue', value: 98 },
      ],
    },
    {
      id: 'conversion-analytics',
      title: 'Conversion Analytics',
      type: 'funnel',
      data: [
        { stage: 'Visitors', count: 10000, percentage: 100 },
        { stage: 'Recipe Views', count: 7500, percentage: 75 },
        { stage: 'Bookmarks', count: 3200, percentage: 32 },
        { stage: 'Shares', count: 1800, percentage: 18 },
        { stage: 'Subscriptions', count: 450, percentage: 4.5 },
      ],
    },
  ];

  // Mock quick actions data
  const quickActions = [
    {
      id: 'approve-recipes',
      title: 'Approve Pending Recipes',
      count: 23,
      icon: 'CheckCircle',
      color: 'success',
      action: '/admin/pending-recipes',
    },
    {
      id: 'manage-featured',
      title: 'Manage Featured Content',
      count: 8,
      icon: 'Star',
      color: 'warning',
      action: '/admin/featured-content',
    },
    {
      id: 'user-feedback',
      title: 'Respond to Feedback',
      count: 15,
      icon: 'MessageCircle',
      color: 'primary',
      action: '/contact-form-management',
    },
    {
      id: 'content-reports',
      title: 'Review Content Reports',
      count: 5,
      icon: 'AlertTriangle',
      color: 'error',
      action: '/admin/reports',
    },
  ];

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeData({
        timestamp: new Date().toLocaleTimeString(),
        activeUsers: Math.floor(Math.random() * 100) + 800,
        newRecipes: Math.floor(Math.random() * 5),
        pendingReviews: Math.floor(Math.random() * 10) + 20,
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const handleStatisticClick = () => {
    // Navigate to detailed report
    // TODO: Implement navigation logic
  };

  const handleDateRangeChange = (range) => {
    setSelectedDateRange(range);
    // TODO: Implement date range change logic
  };

  // Filter options
  const filters = [
    {
      key: 'category',
      label: 'Category',
      permission: true,
      options: [
        { value: '', label: 'All Categories' },
        { value: 'recipes', label: 'Recipes' },
        { value: 'ingredients', label: 'Ingredients' },
        { value: 'users', label: 'Users' },
        { value: 'orders', label: 'Orders' },
      ],
    },
    {
      key: 'userType',
      label: 'User Type',
      permission: true,
      options: [
        { value: '', label: 'All Users' },
        { value: 'premium', label: 'Premium' },
        { value: 'free', label: 'Free' },
        { value: 'trial', label: 'Trial' },
      ],
    },
    {
      key: 'region',
      label: 'Region',
      permission: true,
      options: [
        { value: '', label: 'All Regions' },
        { value: 'north', label: 'North America' },
        { value: 'europe', label: 'Europe' },
        { value: 'asia', label: 'Asia' },
        { value: 'other', label: 'Other' },
      ],
    },
    {
      key: 'device',
      label: 'Device',
      permission: true,
      options: [
        { value: '', label: 'All Devices' },
        { value: 'mobile', label: 'Mobile' },
        { value: 'desktop', label: 'Desktop' },
        { value: 'tablet', label: 'Tablet' },
      ],
    },
  ];

  const handleFilterChange = (filters) => {
    setActiveFilters(filters);
    // TODO: Implement filter change logic
  };

  // Filter handler functions
  const toggleFilter = (filterKey) => {
    setSelectedFilters((prev) => {
      if (prev.includes(filterKey)) {
        return prev.filter((key) => key !== filterKey);
      } else {
        return [...prev, filterKey];
      }
    });
  };

  const saveLayout = () => {
    // Save layout logic if needed
    console.log('Layout saved');
  };

  const getFirstFourFilters = () => {
    return selectedFilters.slice(0, 4);
  };

  const handleKeyPress = (event) => {
    if (event?.key === 'Enter') {
      // Handle search on enter
      console.log('Search triggered');
    }
  };

  const handleApplyFilter = () => {
    // Apply filters
    setActiveFilters(filterData);
    if (isMobile) {
      setOpenFilterDrawer(false);
    }
  };

  const handleClearFilter = () => {
    // Clear all filters
    const clearedFilters = {
      category: '',
      userType: '',
      region: '',
      device: '',
    };
    setFilterData(clearedFilters);
    setActiveFilters(clearedFilters);
    setSearchValue('');
    if (isMobile) {
      setOpenFilterDrawer(false);
    }
  };

  // Open export popover
  const handleExportClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // Close export popover
  const handleExportClose = () => {
    setAnchorEl(null);
  };

  // Handle export download
  const handleExportDownload = async (format) => {
    try {
      // Simulate export process
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Create mock data for download
      const mockData =
        format === 'excel'
          ? 'Mock Excel Data for Analytics Dashboard'
          : 'Mock CSV Data for Analytics Dashboard';

      // Create blob and trigger download
      const blob = new Blob([mockData], {
        type:
          format === 'excel'
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'text/csv',
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute(
        'download',
        `analytics_dashboard.${format === 'excel' ? 'xlsx' : 'csv'}`
      );
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);

      // TODO: Implement actual export API call here
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      handleExportClose();
    }
  };

  return (
    <div className="admin-analytics-dashboard">
      <main className="admin-analytics-dashboard__main">
        <div className="admin-analytics-dashboard__container">
          {/* Header Section */}
          <div className="admin-analytics-dashboard__header">
            <div className="admin-analytics-dashboard__header-content">
              <div className="admin-analytics-dashboard__header-info">
                <h1 className="sub-header-text">Analytics Dashboard</h1>
                <p className="admin-analytics-dashboard__subtitle ">
                  Comprehensive insights into recipe performance and user
                  engagement
                </p>
                {realTimeData.timestamp && (
                  <div className="admin-analytics-dashboard__live-indicator">
                    <div className="admin-analytics-dashboard__live-dot"></div>
                    <span className="admin-analytics-dashboard__live-text">
                      Live data • Last updated {realTimeData.timestamp}
                    </span>
                  </div>
                )}
              </div>

              <div className="admin-analytics-dashboard__header-actions">
                <DateRangePicker
                  value={selectedDateRange}
                  onChange={handleDateRangeChange}
                />
                <div className="admin-analytics-dashboard__export-buttons">
                  <Box>
                    <CustomButton
                      variant="outlined"
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={
                            <Typography className="sub-title-text">
                              Export
                            </Typography>
                          }
                          arrow
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                        >
                          <Icon name="Download" size={16} />
                        </Tooltip>
                      }
                      onClick={handleExportClick}
                      className="admin-analytics-dashboard__export-btn"
                    />
                  </Box>
                </div>
              </div>
            </div>

            {/* Filter Button */}
            <Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">Filter</Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <FilterListIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setOpenFilterDrawer(true);
                }}
              />
            </Box>
          </div>

          {/* Statistics Tiles Grid */}
          <div className="admin-analytics-dashboard__stats-grid">
            {statisticsData.map((stat) => (
              <StatisticsTile
                key={stat.id}
                {...stat}
                onClick={() => handleStatisticClick(stat.id)}
                realTimeValue={realTimeData[stat.id]}
              />
            ))}
          </div>

          {/* Chart Widgets Grid */}
          <div className="admin-analytics-dashboard__charts-grid">
            {chartWidgets.map((widget) => (
              <ChartWidget
                key={widget.id}
                {...widget}
                dateRange={selectedDateRange}
                filters={activeFilters}
              />
            ))}
          </div>

          {/* Bottom Section */}
          <div className="admin-analytics-dashboard__bottom-section">
            {/* Quick Action Panels */}
            <QuickActionPanel actions={quickActions} />

            {/* Recent Activity */}
            <RecentActivity />
          </div>
        </div>
      </main>

      {/* Export Popover */}
      <Popover
        className="export-popover"
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleExportClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box className="export-option">
          <Typography
            className="p14 fw600 pb8 cursor-pointer"
            onClick={() => handleExportDownload('excel')}
          >
            Excel
          </Typography>
          <Typography
            className="p14 fw600 cursor-pointer"
            onClick={() => handleExportDownload('csv')}
          >
            CSV
          </Typography>
        </Box>
      </Popover>

      {/* Filter Drawer */}
      <RightDrawer
        anchor={'right'}
        open={openFilterDrawer}
        onClose={() => setOpenFilterDrawer(false)}
        title="Filter"
        className="filter-options-drawer"
        content={
          <FilterComponent
            filters={filters}
            filterData={filterData}
            setFilterData={setFilterData}
            selectedFilters={selectedFilters}
            toggleFilter={toggleFilter}
            saveLayout={saveLayout}
            setOpenFilterDrawer={setOpenFilterDrawer}
            setSelectedFilters={setSelectedFilters}
            getFirstFourFilters={getFirstFourFilters}
            setSearchValue={setSearchValue}
            searchValue={searchValue}
            handleKeyPress={handleKeyPress}
            isMobile={isMobile}
            handleApplyFilter={handleApplyFilter}
            handleClearFilter={handleClearFilter}
          />
        }
      />
    </div>
  );
};

export default AdminAnalyticsDashboard;
