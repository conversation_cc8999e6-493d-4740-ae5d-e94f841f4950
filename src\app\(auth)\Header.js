'use client';

import React, { useEffect, useState, useContext } from 'react';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Drawer,
  Tooltip,
} from '@mui/material';
import NotificationsNoneIcon from '@mui/icons-material/NotificationsNone';
import AuthContext from '@/helper/authcontext';
import MenuIcon from '@mui/icons-material/Menu';
import {
  checkOrganizationRole,
  getEnvPrefix,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import AdministratorSelect from '@/components/administratorSelect';
import axiosInstance from '@/helper/axios/axiosInstance';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { URLS, ORG_URLS } from '@/helper/constants/urls';
import SideBarMenuList from '@/components/Sidebarmenulist';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import {
  fetchFromStorage,
  removeFromStorage,
  saveToStorage,
} from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import OneSignal from 'react-onesignal';
import { Helmet } from 'react-helmet';
import HeaderSubMenu from '@/components/UI/HeaderSubMenu';
import { Config } from '@/helper/context/config';
import _ from 'lodash';
import PreLoader from '@/components/UI/Loader';
import { leftSideMenu } from '@/helper/common/commonMenus';
import useOneSignalToken from '@/hooks/useOneSignal';
// import ExitToAppIcon from '@mui/icons-material/ExitToApp';
// import NotificationBanner from '@/components/UI/NotificationBanner';
import { pageSEO } from '@/helper/context/seoConfig';
import './layout.scss';

const Header = ({ open }) => {
  const {
    authState,
    setAuthState,
    reFetch,
    isActive,
    setIsActive,
    // userdata,
    setUserdata,
    AllListsData,
    setAllListsData,
    setRestrictedModal,
    orgDetails,
    setOrgDetails,
  } = useContext(AuthContext);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [loader, setLoader] = useState(false);
  const [UserDetails, setUserDetails] = useState('');
  const [isMenuActive, setIsMenuActive] = useState('');
  useOneSignalToken();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const branchName = searchParams.get('name');
  let isDeviceId = fetchFromStorage(identifiers?.DEVICEID);
  const authdata = fetchFromStorage(identifiers?.AUTH_DATA);
  const isNormalLogin =
    authdata?.organizationId && authdata?.organizationStatus;
  const isNormalUser = fetchFromStorage(identifiers?.NORMAL_LOGIN);
  // List of departments
  const getDepartmentList = async (alllist) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );

      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterDepList = data?.data?.map((department) => ({
          label: department?.department_name,
          value: department?.id,
        }));
        let mergeDepList = _.concat(alloption, filterDepList);
        setAllListsData({
          ...alllist,
          SelectDepartmentList: mergeDepList,
          ActiveDepartmentList: filterDepList,
        });
      }
    } catch (error) {
      console.error(error);
      setLoader(false);
    }
  };
  // List of Active branches
  const getBranchListActive = async (alllist) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${''}&page=${1}&size=${''}&branchStatus=active`
      );
      if (status === 200) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterBranchList = data?.data?.map((branch) => ({
          label: branch?.branch_name,
          value: branch?.id,
          color: branch?.branch_color,
          textcolor: branch?.text_color,
        }));
        let mergeBranchList = _.concat(alloption, filterBranchList);
        // setAllListsData({
        //   ...alllist,
        //   ActiveBranchList: filterBranchList
        // });
        getDepartmentList({
          ...alllist,
          SelectBranchList: mergeBranchList,
          ActiveBranchList: filterBranchList,
        });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getAllBranchList = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS.GET_BRANCH_LIST + `?search=&page=&size=`
      );
      if (status === 200) {
        // setAllListsData({ ...AllListsData, branchList: data?.data });
        getBranchListActive({ ...AllListsData, branchList: data?.data });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const isActiveMenu = leftSideMenu?.find((f) => f?.id === isMenuActive);
  // Get general settings data
  const getSubscriptionUsage = async (user) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_SUBSCRIPTION_USAGE
      );

      if (status === 200) {
        setAuthState({
          ...user,
          subscriptionUsage: {
            ...data?.data,
            total_size_gb:
              data?.data?.unit === 'TB'
                ? parseFloat((data?.data?.total_size * 1024).toFixed(2))
                : data?.data?.unit === 'KB'
                  ? parseFloat(
                      (data?.data?.total_size / (1024 * 1024)).toFixed(2)
                    )
                  : data?.data?.unit === 'MB'
                    ? parseFloat((data?.data?.total_size / 1024).toFixed(2))
                    : data?.data?.total_size,
          },
        });
        saveToStorage(identifiers?.USER_DATA, {
          ...user,
          subscriptionUsage: data?.data,
        });
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Get general settings data
  const getSettingsDetails = async (user) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_GENERAL_SETTINGS
      );

      if (status === 200) {
        getSubscriptionUsage({
          ...user,
          generalSeetings: data?.data,
        });
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getUserPermission = async (user) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.USER_PERMISSION);
      if (status === 200) {
        if (!isNormalLogin) {
          // setAuthState({
          //   ...user,
          //   UserPermission: data?.data,
          // });
          getSettingsDetails({
            ...user,
            UserPermission: data?.data,
          });
        } else {
          getUserDetailsOrg({
            ...user,
            UserPermission: data?.data,
          });
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getUserDetailsOrg = async (orgUser) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.MY_PROFILE);
      if (status === 200) {
        let filterUserList = data?.data?.user_roles?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        const userdata = {
          ...data?.data,
          ...orgUser,
          user_roles: filterUserList,
          user_first_name: orgUser?.firstName,
          user_last_name: orgUser?.lastName,
        };
        // setAuthState(userdata);
        setUserDetails(userdata);
        getSettingsDetails(userdata);
        if (
          data?.data?.webAppToken === '' ||
          data?.data?.webAppToken === null ||
          data?.data?.webAppToken !== isDeviceId
        ) {
          setTimeout(() => {
            const playerId = JSON.parse(
              JSON.stringify(OneSignal?.User?.PushSubscription)
            )?.id;
            if (playerId && playerId !== undefined) {
              console.error('error', playerId, 'header org');
              saveToStorage(identifiers?.DEVICEID, playerId);
              updateOnesignalToken(playerId);
            }
          }, 2000);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getUserDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.MY_PROFILE);
      if (status === 200) {
        let filterUserList = data?.data?.user_roles?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        setUserDetails({ ...data?.data, user_roles: filterUserList });
        const userdata = { ...data?.data, user_roles: filterUserList };
        getUserPermission(userdata);
        if (
          data?.data?.webAppToken === '' ||
          data?.data?.webAppToken === null ||
          data?.data?.webAppToken !== isDeviceId
        ) {
          setTimeout(() => {
            const playerId = JSON.parse(
              JSON.stringify(OneSignal?.User?.PushSubscription)
            )?.id;
            if (playerId && playerId !== undefined) {
              console.error('error', playerId, 'header');
              saveToStorage(identifiers?.DEVICEID, playerId);
              updateOnesignalToken(playerId);
            }
          }, 2000);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getUserDetailsORG = async (userId) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstanceOrg.get(
        ORG_URLS?.GET_USER_DATA_BY_USER_ID + `/${userId}`
      );
      if (status === 200) {
        // let filterUserList = data?.data?.user_roles?.map((user) => ({
        //   label: user?.role_name,
        //   value: user?.id
        // }));
        setUserDetails({ ...data?.data });
        setAuthState({
          ...data?.data?.user,
        });
        getUserPermission(data?.data?.user);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getOrgbyID = async (id) => {
    try {
      const { status, data } = await axiosInstanceOrg.get(
        ORG_URLS.GET_ORGANIZATION_BY_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          setOrgDetails(data?.data);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    getAllBranchList();
    if (!authdata?.organizationId && !authdata?.organizationStatus) {
      getUserDetails();
    } else {
      getUserDetailsORG(authdata?.user_id); //authdata?.user_id
      checkOrganizationRole('org_master') &&
        authdata?.organizationId &&
        getOrgbyID(authdata?.organizationId);
    }
  }, []);

  const updateOnesignalToken = async (playerId) => {
    // setLoader(true);
    const sendData = {
      webAppToken: playerId,
    };
    try {
      const { status } = await axiosInstance.post(
        URLS?.UPDATE_ONESIGNAL_TOKEN,
        sendData
      );

      if (status === 200) {
        // setLoader(false);
      }
    } catch (error) {
      console.error(error);
      // setLoader(false);
    }
  };

  const pageNames = {
    '/chart-dashboard': 'Dashboard',
    '/chart-dashboard/template': 'Dashboard Template',
    '/own-leave': 'Leave Status',
    '/leave-remark': 'Leave Remark',
    '/staff-notification': 'General Notification',
    '/own-notification': 'Notification',
    '/activity': 'Activity Logs',
    '/resignation': 'Resignation',
    '/resignation-remark': 'Resignation',
    '/change-request': 'Change Request',
    '/logbook-reports': 'Logbook Reports',
    '/empcontracts': 'Contract Center',
    '/organization': 'Settings',
    '/budget-forecast': 'Budget & Forecast',
  };
  const pageIncludes = (pageName, isPage) => {
    if (isPage) {
      return pageName;
    }
    return Config?.environment
      ? Config?.environment + ' - ' + pageName
      : pageName;
  };
  const getPageName = (isPage) => {
    if (pathname.includes('/branch/')) {
      return pageIncludes(branchName ? branchName : 'Branch details', isPage);
    } else if (
      pathname.includes('/document-staff/') ||
      pathname.includes('/document-own/') ||
      pathname.includes('/create-content') ||
      pathname.includes('/create-category')
    ) {
      return pageIncludes('Document Center', isPage);
    } else if (pathname.includes('/organization/')) {
      return pageIncludes('dashboard', isPage);
    } else if (pathname.includes('/chart-dashboard/template/')) {
      return pageIncludes('Dashboard Template', isPage);
    } else if (pathname.includes('/chart-dashboard/')) {
      return pageIncludes('Dashboard', isPage);
    } else if (pathname.includes('/budget-forecast/')) {
      return pageIncludes('Budget & Forecast', isPage);
    } else if (pathname.includes('/org/setup')) {
      return pageIncludes('Comapny Settings', isPage);
    }
    return pageIncludes(pageNames[pathname] || '', isPage);
  };

  // const handleLogout = async () => {
  //   if (isDeviceId) {
  //     const sendData = {
  //       webAppToken: isDeviceId,
  //     };
  //     try {
  //       const { status } = await axiosInstance.post(URLS?.LOGOUT, sendData);

  //       if (status === 200) {
  //         setTimeout(() => {
  //           removeFromStorage(identifiers?.AUTH_DATA);
  //           removeFromStorage(identifiers?.USER_DATA);
  //           removeFromStorage(identifiers?.RedirectData);
  //           removeFromStorage(identifiers?.DEVICEID);
  //           if (
  //             checkOrganizationRole('super_admin') ||
  //             checkOrganizationRole('org_master')
  //           ) {
  //             router.push('/org/login');
  //           } else {
  //             router.push('/');
  //           }
  //         }, 100);
  //         setTimeout(() => {
  //           setApiMessage('success', 'Logout successfully');
  //         }, 1000);
  //       }
  //     } catch (error) {
  //       setApiMessage('error', error?.response?.data?.message);
  //       setTimeout(() => {
  //         removeFromStorage(identifiers?.AUTH_DATA);
  //         removeFromStorage(identifiers?.USER_DATA);
  //         removeFromStorage(identifiers?.RedirectData);
  //         removeFromStorage(identifiers?.DEVICEID);
  //         if (
  //           checkOrganizationRole('super_admin') ||
  //           checkOrganizationRole('org_master')
  //         ) {
  //           router.push('/org/login');
  //         } else {
  //           router.push('/');
  //         }
  //       }, 100);
  //       setTimeout(() => {
  //         setApiMessage('success', 'Logout successfully');
  //       }, 1000);
  //     }
  //   } else {
  //     setTimeout(() => {
  //       removeFromStorage(identifiers?.AUTH_DATA);
  //       removeFromStorage(identifiers?.USER_DATA);
  //       removeFromStorage(identifiers?.RedirectData);
  //       removeFromStorage(identifiers?.DEVICEID);
  //       if (
  //         checkOrganizationRole('super_admin') ||
  //         checkOrganizationRole('org_master')
  //       ) {
  //         router.push('/org/login');
  //       } else {
  //         router.push('/');
  //       }
  //     }, 100);
  //     setTimeout(() => {
  //       setApiMessage('success', 'Logout successfully');
  //     }, 1000);
  //   }
  // };

  useEffect(() => {
    if (UserDetails?.webAppToken !== isDeviceId) {
      if (isDeviceId) {
        updateOnesignalToken(isDeviceId);
      }
    }
  }, [UserDetails?.webAppToken, isDeviceId]);

  useEffect(() => {
    let activeId = fetchFromStorage('activeMenuItem');
    if (isActive) {
      setIsMenuActive(isActive);
    } else {
      setIsMenuActive(activeId);
      setIsActive(activeId);
    }
  }, [isActive, reFetch]);

  const seoData = pageSEO[pathname] || '';
  const envPrefix = getEnvPrefix();
  const seoTitle = identifiers?.SEO_TITLE || identifiers?.APP_NAME;
  const fullTitle = `${envPrefix}${seoData?.title} | ${seoTitle}`;

  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.getElementById('site-header');
      if (header) {
        document.documentElement.style.setProperty(
          '--header-height',
          `${header.offsetHeight}px`
        );
      }
    };

    updateHeaderHeight();
    window.addEventListener('resize', updateHeaderHeight);
    return () => window.removeEventListener('resize', updateHeaderHeight);
  }, []);

  return (
    <>
      {loader && <PreLoader />}
      <Box
        className={`${open ? 'header-bar open-header-bar high-priority-class' : 'header-bar high-priority-class'} `}
        id="site-header"
      >
        {/* {(pathname !== '/org/organization' || pathname !== '/org/setup') && (
          <Helmet>
            <title> {getPageName()}</title>
          </Helmet>
        )} */}
        {seoData && (
          <Helmet>
            <title>{fullTitle}</title>
          </Helmet>
        )}
        <AppBar position="static" className="header-app-bar">
          <Toolbar disableGutters>
            <Box className="d-flex align-center header-content w100">
              <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
                <IconButton
                  size="large"
                  aria-label="account of current user"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={() => setMobileOpen(true)}
                  color="inherit"
                  className="menu-icon-drawer"
                >
                  <MenuIcon />
                </IconButton>
              </Box>

              {isActiveMenu &&
              isActiveMenu?.submenu &&
              isActiveMenu?.submenu?.length > 0 ? (
                <HeaderSubMenu
                  headerMenu={isActiveMenu?.submenu}
                  pathname={pathname}
                  isMenuActive={isMenuActive}
                />
              ) : (
                <Typography className="title-sm fw600 page-title text-capital">
                  {pathname !== '/org/setup' && getPageName(true)}
                </Typography>
              )}

              <Box className="administrator-box d-flex gap-5">
                {(checkOrganizationRole('org_master') ||
                  checkOrganizationRole('super_admin') ||
                  authState?.web_user_active_role_id !== 1) && (
                  <Box className="notification-icon-wrap d-flex align-center gap-5">
                    <Tooltip
                      arrow
                      title={<Typography>Notification</Typography>}
                      classes={{
                        tooltip: 'info-tooltip-container ',
                      }}
                    >
                      <NotificationsNoneIcon
                        className="notification-icon cursor-pointer"
                        onClick={() => {
                          const isOrgView =
                            checkOrganizationRole('org_master') || false;
                          const isRestricted = isNormalUser
                            ? false
                            : isOrgView
                              ? !orgDetails?.attributes?.email ||
                                !orgDetails?.attributes?.contact_person ||
                                authState?.purchase_plan === false
                              : !authState?.profile_status;

                          if (isRestricted) {
                            if (isOrgView) {
                              if (pathname === '/org/organization') {
                                setRestrictedModal({
                                  isOrgView: isOrgView,
                                  purchase_plan: authState?.purchase_plan,
                                  user_status:
                                    !orgDetails?.attributes?.email ||
                                    !orgDetails?.attributes?.contact_person,
                                  profile_status: authState?.profile_status,
                                });
                              } else {
                                router.push('/org/organization');
                              }
                            } else {
                              if (pathname === '/myprofile') {
                                setRestrictedModal({
                                  isOrgView: isOrgView,
                                  purchase_plan: authState?.purchase_plan,
                                  profile_status: authState?.profile_status,
                                });
                              } else {
                                router.push('/myprofile');
                              }
                            }
                          } else {
                            setUserdata();
                            removeFromStorage(identifiers?.RedirectData);
                            if (
                              checkOrganizationRole('org_master') ||
                              checkOrganizationRole('super_admin')
                            ) {
                              router.push('/own-notification');
                            } else if (
                              authState?.UserPermission?.['notification'] ===
                                2 ||
                              authState?.UserPermission?.['notification'] === 1
                            ) {
                              router.push('/staff-notification');
                            } else {
                              router.push('/own-notification');
                            }
                          }
                        }}
                      />
                    </Tooltip>
                    {/* <Tooltip arrow title="Log out">
                      <ExitToAppIcon
                        className="logout-icon cursor-pointer"
                        onClick={() => handleLogout()}
                      />
                    </Tooltip> */}
                  </Box>
                )}

                <Box>
                  <Drawer
                    sx={{ width: '40%' }}
                    anchor="left"
                    open={mobileOpen}
                    onClose={() => setMobileOpen(false)}
                    className="menu-drawer-sec"
                  >
                    <SideBarMenuList setMobileOpen={setMobileOpen} />
                  </Drawer>
                </Box>
                <AdministratorSelect
                  UserDetails={authState}
                  getUserDetails={getUserDetails}
                />
              </Box>
            </Box>
          </Toolbar>
        </AppBar>
      </Box>
    </>
  );
};

export default Header;
