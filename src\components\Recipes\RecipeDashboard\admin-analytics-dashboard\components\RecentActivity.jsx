'use client';

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Icon from '@/components/UI/AppIcon/AppIcon';
import Image from '@/components/UI/AppImage/AppImage';
import './recentactivity.scss';

const RecentActivity = () => {
  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  // Mock activity data
  const mockActivities = [
    {
      id: 1,
      type: 'recipe_created',
      user: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
        role: 'Content Creator',
      },
      action: 'created a new recipe',
      target: 'Chocolate Lava Cake',
      timestamp: new Date(Date.now() - 300000), // 5 minutes ago
      metadata: {
        category: 'Desserts',
        difficulty: 'Medium',
      },
    },
    {
      id: 2,
      type: 'user_registered',
      user: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
        role: 'User',
      },
      action: 'registered as a new user',
      target: null,
      timestamp: new Date(Date.now() - 900000), // 15 minutes ago
      metadata: {
        plan: 'Premium',
        referral: 'Social Media',
      },
    },
    {
      id: 3,
      type: 'recipe_approved',
      user: {
        name: 'Admin',
        avatar: null,
        role: 'Administrator',
      },
      action: 'approved recipe',
      target: 'Spicy Thai Curry',
      timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
      metadata: {
        category: 'Main Course',
        author: 'Emma Wilson',
      },
    },
    {
      id: 4,
      type: 'comment_posted',
      user: {
        name: 'David Rodriguez',
        avatar: 'https://randomuser.me/api/portraits/men/28.jpg',
        role: 'User',
      },
      action: 'commented on',
      target: 'Classic Caesar Salad',
      timestamp: new Date(Date.now() - 2700000), // 45 minutes ago
      metadata: {
        rating: 5,
        comment: 'Absolutely delicious! Made it for dinner last night.',
      },
    },
    {
      id: 5,
      type: 'recipe_featured',
      user: {
        name: 'Admin',
        avatar: null,
        role: 'Administrator',
      },
      action: 'featured recipe',
      target: 'Mediterranean Quinoa Bowl',
      timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      metadata: {
        category: 'Healthy',
        views: 1250,
      },
    },
    {
      id: 6,
      type: 'user_upgraded',
      user: {
        name: 'Lisa Thompson',
        avatar: 'https://randomuser.me/api/portraits/women/55.jpg',
        role: 'Premium User',
      },
      action: 'upgraded to Premium',
      target: null,
      timestamp: new Date(Date.now() - 5400000), // 1.5 hours ago
      metadata: {
        plan: 'Annual Premium',
        discount: '20%',
      },
    },
    {
      id: 7,
      type: 'recipe_reported',
      user: {
        name: 'Anonymous User',
        avatar: null,
        role: 'User',
      },
      action: 'reported recipe',
      target: 'Quick Pasta Recipe',
      timestamp: new Date(Date.now() - 7200000), // 2 hours ago
      metadata: {
        reason: 'Inappropriate content',
        status: 'Under review',
      },
    },
    {
      id: 8,
      type: 'bulk_import',
      user: {
        name: 'System',
        avatar: null,
        role: 'System',
      },
      action: 'imported recipes',
      target: '25 new recipes',
      timestamp: new Date(Date.now() - 10800000), // 3 hours ago
      metadata: {
        source: 'Partner API',
        success: 23,
        failed: 2,
      },
    },
  ];

  const filterOptions = [
    { value: 'all', label: 'All Activity', icon: 'Activity' },
    { value: 'recipe_created', label: 'Recipe Created', icon: 'Plus' },
    { value: 'user_registered', label: 'New Users', icon: 'UserPlus' },
    { value: 'recipe_approved', label: 'Approvals', icon: 'CheckCircle' },
    { value: 'comment_posted', label: 'Comments', icon: 'MessageCircle' },
    { value: 'system', label: 'System Events', icon: 'Settings' },
  ];

  useEffect(() => {
    // Simulate loading
    setIsLoading(true);
    setTimeout(() => {
      const filteredActivities =
        filter === 'all'
          ? mockActivities
          : mockActivities.filter((activity) => {
              if (filter === 'system') {
                return [
                  'bulk_import',
                  'recipe_featured',
                  'recipe_approved',
                ].includes(activity.type);
              }
              return activity.type === filter;
            });

      setActivities(filteredActivities);
      setIsLoading(false);
    }, 500);
  }, [filter]);

  const getActivityIcon = (type) => {
    const iconMap = {
      recipe_created: 'Plus',
      user_registered: 'UserPlus',
      recipe_approved: 'CheckCircle',
      comment_posted: 'MessageCircle',
      recipe_featured: 'Star',
      user_upgraded: 'Crown',
      recipe_reported: 'AlertTriangle',
      bulk_import: 'Download',
    };
    return iconMap[type] || 'Activity';
  };

  const getActivityColorClass = (type) => {
    const classMap = {
      recipe_created: 'recent-activity__item-icon--recipe-created',
      user_registered: 'recent-activity__item-icon--user-registered',
      recipe_approved: 'recent-activity__item-icon--recipe-approved',
      comment_posted: 'recent-activity__item-icon--comment-posted',
      recipe_featured: 'recent-activity__item-icon--recipe-featured',
      user_upgraded: 'recent-activity__item-icon--user-upgraded',
      recipe_reported: 'recent-activity__item-icon--recipe-reported',
      bulk_import: 'recent-activity__item-icon--bulk-import',
    };
    return classMap[type] || 'recent-activity__item-icon--bulk-import';
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const renderActivityMetadata = (activity) => {
    const { type, metadata } = activity;

    switch (type) {
      case 'recipe_created':
        return (
          <div className="recent-activity__metadata">
            {metadata.category} • {metadata.difficulty}
          </div>
        );
      case 'user_registered':
        return (
          <div className="recent-activity__metadata">
            {metadata.plan} • via {metadata.referral}
          </div>
        );
      case 'comment_posted':
        return (
          <div className="recent-activity__metadata">
            ⭐ {metadata.rating}/5 • "{metadata.comment.substring(0, 30)}..."
          </div>
        );
      case 'recipe_featured':
        return (
          <div className="recent-activity__metadata">
            {metadata.views} views • {metadata.category}
          </div>
        );
      case 'bulk_import':
        return (
          <div className="recent-activity__metadata">
            {metadata.success} success, {metadata.failed} failed
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="recent-activity">
      {/* Header */}
      <div className="recent-activity__header">
        <div className="recent-activity__header-top">
          <h3 className="recent-activity__title">Recent Activity</h3>
          <Link to="/admin/activity-log" className="recent-activity__view-all">
            <span>View all</span>
            <Icon name="ExternalLink" size={14} />
          </Link>
        </div>

        {/* Filter Tabs */}
        <div className="recent-activity__filters">
          {filterOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setFilter(option.value)}
              className={`recent-activity__filter-btn ${
                filter === option.value
                  ? 'recent-activity__filter-btn--active'
                  : 'recent-activity__filter-btn--inactive'
              }`}
            >
              <Icon name={option.icon} size={12} />
              <span>{option.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Activity List */}
      <div className="recent-activity__content">
        {isLoading ? (
          <div className="recent-activity__loading">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="recent-activity__loading-item">
                <div className="recent-activity__loading-content">
                  <div className="recent-activity__loading-avatar"></div>
                  <div className="recent-activity__loading-text">
                    <div className="recent-activity__loading-line recent-activity__loading-line--title"></div>
                    <div className="recent-activity__loading-line recent-activity__loading-line--subtitle"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : activities.length === 0 ? (
          <div className="recent-activity__empty">
            <Icon
              name="Activity"
              size={48}
              className="recent-activity__empty-icon"
            />
            <p className="recent-activity__empty-text">
              No recent activity found
            </p>
          </div>
        ) : (
          <div className="recent-activity__list">
            {activities.map((activity) => (
              <div key={activity.id} className="recent-activity__item">
                {/* Activity Icon */}
                <div
                  className={`recent-activity__item-icon ${getActivityColorClass(activity.type)}`}
                >
                  <Icon name={getActivityIcon(activity.type)} size={14} />
                </div>

                {/* Activity Content */}
                <div className="recent-activity__item-content">
                  <div className="recent-activity__item-header">
                    <div className="recent-activity__item-main">
                      {/* User Info */}
                      <div className="recent-activity__user-info">
                        {activity.user.avatar ? (
                          <div className="recent-activity__user-avatar">
                            <Image
                              src={activity.user.avatar}
                              alt={activity.user.name}
                            />
                          </div>
                        ) : (
                          <div className="recent-activity__user-placeholder">
                            <Icon name="User" size={10} />
                          </div>
                        )}
                        <span className="recent-activity__user-name">
                          {activity.user.name}
                        </span>
                        <span className="recent-activity__user-role">
                          {activity.user.role}
                        </span>
                      </div>

                      {/* Activity Description */}
                      <p className="recent-activity__description">
                        {activity.action}{' '}
                        {activity.target && (
                          <span className="recent-activity__target">
                            {activity.target}
                          </span>
                        )}
                      </p>

                      {/* Metadata */}
                      {renderActivityMetadata(activity)}
                    </div>

                    {/* Timestamp */}
                    <span className="recent-activity__timestamp">
                      {formatTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="recent-activity__footer">
        <div className="recent-activity__footer-content">
          <span className="recent-activity__footer-text">
            Showing {activities.length} recent activities
          </span>
          <button className="recent-activity__refresh-btn">Refresh</button>
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;
