import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Icon from 'components/AppIcon';
import Image from 'components/AppImage';

const RecentActivity = () => {
  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  // Mock activity data
  const mockActivities = [
    {
      id: 1,
      type: 'recipe_created',
      user: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
        role: 'Content Creator',
      },
      action: 'created a new recipe',
      target: 'Chocolate Lava Cake',
      timestamp: new Date(Date.now() - 300000), // 5 minutes ago
      metadata: {
        category: 'Desserts',
        difficulty: 'Medium',
      },
    },
    {
      id: 2,
      type: 'user_registered',
      user: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
        role: 'User',
      },
      action: 'registered as a new user',
      target: null,
      timestamp: new Date(Date.now() - 900000), // 15 minutes ago
      metadata: {
        plan: 'Premium',
        referral: 'Social Media',
      },
    },
    {
      id: 3,
      type: 'recipe_approved',
      user: {
        name: 'Admin',
        avatar: null,
        role: 'Administrator',
      },
      action: 'approved recipe',
      target: 'Spicy Thai Curry',
      timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
      metadata: {
        category: 'Main Course',
        author: 'Emma Wilson',
      },
    },
    {
      id: 4,
      type: 'comment_posted',
      user: {
        name: 'David Rodriguez',
        avatar: 'https://randomuser.me/api/portraits/men/28.jpg',
        role: 'User',
      },
      action: 'commented on',
      target: 'Classic Caesar Salad',
      timestamp: new Date(Date.now() - 2700000), // 45 minutes ago
      metadata: {
        rating: 5,
        comment: 'Absolutely delicious! Made it for dinner last night.',
      },
    },
    {
      id: 5,
      type: 'recipe_featured',
      user: {
        name: 'Admin',
        avatar: null,
        role: 'Administrator',
      },
      action: 'featured recipe',
      target: 'Mediterranean Quinoa Bowl',
      timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      metadata: {
        category: 'Healthy',
        views: 1250,
      },
    },
    {
      id: 6,
      type: 'user_upgraded',
      user: {
        name: 'Lisa Thompson',
        avatar: 'https://randomuser.me/api/portraits/women/55.jpg',
        role: 'Premium User',
      },
      action: 'upgraded to Premium',
      target: null,
      timestamp: new Date(Date.now() - 5400000), // 1.5 hours ago
      metadata: {
        plan: 'Annual Premium',
        discount: '20%',
      },
    },
    {
      id: 7,
      type: 'recipe_reported',
      user: {
        name: 'Anonymous User',
        avatar: null,
        role: 'User',
      },
      action: 'reported recipe',
      target: 'Quick Pasta Recipe',
      timestamp: new Date(Date.now() - 7200000), // 2 hours ago
      metadata: {
        reason: 'Inappropriate content',
        status: 'Under review',
      },
    },
    {
      id: 8,
      type: 'bulk_import',
      user: {
        name: 'System',
        avatar: null,
        role: 'System',
      },
      action: 'imported recipes',
      target: '25 new recipes',
      timestamp: new Date(Date.now() - 10800000), // 3 hours ago
      metadata: {
        source: 'Partner API',
        success: 23,
        failed: 2,
      },
    },
  ];

  const filterOptions = [
    { value: 'all', label: 'All Activity', icon: 'Activity' },
    { value: 'recipe_created', label: 'Recipe Created', icon: 'Plus' },
    { value: 'user_registered', label: 'New Users', icon: 'UserPlus' },
    { value: 'recipe_approved', label: 'Approvals', icon: 'CheckCircle' },
    { value: 'comment_posted', label: 'Comments', icon: 'MessageCircle' },
    { value: 'system', label: 'System Events', icon: 'Settings' },
  ];

  useEffect(() => {
    // Simulate loading
    setIsLoading(true);
    setTimeout(() => {
      const filteredActivities =
        filter === 'all'
          ? mockActivities
          : mockActivities.filter((activity) => {
              if (filter === 'system') {
                return [
                  'bulk_import',
                  'recipe_featured',
                  'recipe_approved',
                ].includes(activity.type);
              }
              return activity.type === filter;
            });

      setActivities(filteredActivities);
      setIsLoading(false);
    }, 500);
  }, [filter]);

  const getActivityIcon = (type) => {
    const iconMap = {
      recipe_created: 'Plus',
      user_registered: 'UserPlus',
      recipe_approved: 'CheckCircle',
      comment_posted: 'MessageCircle',
      recipe_featured: 'Star',
      user_upgraded: 'Crown',
      recipe_reported: 'AlertTriangle',
      bulk_import: 'Download',
    };
    return iconMap[type] || 'Activity';
  };

  const getActivityColor = (type) => {
    const colorMap = {
      recipe_created: 'text-success bg-success-50',
      user_registered: 'text-primary bg-primary-50',
      recipe_approved: 'text-success bg-success-50',
      comment_posted: 'text-accent bg-accent-50',
      recipe_featured: 'text-warning bg-warning-50',
      user_upgraded: 'text-secondary bg-secondary-50',
      recipe_reported: 'text-error bg-error-50',
      bulk_import: 'text-text-secondary bg-gray-50',
    };
    return colorMap[type] || 'text-text-secondary bg-gray-50';
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const renderActivityMetadata = (activity) => {
    const { type, metadata } = activity;

    switch (type) {
      case 'recipe_created':
        return (
          <div className="text-xs text-text-secondary">
            {metadata.category} • {metadata.difficulty}
          </div>
        );
      case 'user_registered':
        return (
          <div className="text-xs text-text-secondary">
            {metadata.plan} • via {metadata.referral}
          </div>
        );
      case 'comment_posted':
        return (
          <div className="text-xs text-text-secondary">
            ⭐ {metadata.rating}/5 • "{metadata.comment.substring(0, 30)}..."
          </div>
        );
      case 'recipe_featured':
        return (
          <div className="text-xs text-text-secondary">
            {metadata.views} views • {metadata.category}
          </div>
        );
      case 'bulk_import':
        return (
          <div className="text-xs text-text-secondary">
            {metadata.success} success, {metadata.failed} failed
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-surface border border-gray-200 rounded-lg shadow-card h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-heading font-semibold text-lg text-text-primary">
            Recent Activity
          </h3>
          <Link
            to="/admin/activity-log"
            className="text-sm text-primary hover:text-primary-600 transition-smooth flex items-center space-x-1"
          >
            <span>View all</span>
            <Icon name="ExternalLink" size={14} />
          </Link>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap gap-1">
          {filterOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setFilter(option.value)}
              className={`flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium transition-smooth ${
                filter === option.value
                  ? 'bg-primary text-white'
                  : 'text-text-secondary hover:bg-primary-50 hover:text-primary'
              }`}
            >
              <Icon name={option.icon} size={12} />
              <span>{option.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Activity List */}
      <div className="p-4">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-8">
            <Icon
              name="Activity"
              size={48}
              className="text-text-secondary mx-auto mb-3"
            />
            <p className="text-text-secondary">No recent activity found</p>
          </div>
        ) : (
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {activities.map((activity) => (
              <div
                key={activity.id}
                className="flex items-start space-x-3 group"
              >
                {/* Activity Icon */}
                <div
                  className={`p-2 rounded-full ${getActivityColor(activity.type)}`}
                >
                  <Icon name={getActivityIcon(activity.type)} size={14} />
                </div>

                {/* Activity Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* User Info */}
                      <div className="flex items-center space-x-2 mb-1">
                        {activity.user.avatar ? (
                          <div className="w-5 h-5 rounded-full overflow-hidden">
                            <Image
                              src={activity.user.avatar}
                              alt={activity.user.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center">
                            <Icon
                              name="User"
                              size={10}
                              className="text-gray-600"
                            />
                          </div>
                        )}
                        <span className="text-sm font-medium text-text-primary">
                          {activity.user.name}
                        </span>
                        <span className="text-xs text-text-secondary">
                          {activity.user.role}
                        </span>
                      </div>

                      {/* Activity Description */}
                      <p className="text-sm text-text-secondary">
                        {activity.action}{' '}
                        {activity.target && (
                          <span className="font-medium text-text-primary">
                            {activity.target}
                          </span>
                        )}
                      </p>

                      {/* Metadata */}
                      {renderActivityMetadata(activity)}
                    </div>

                    {/* Timestamp */}
                    <span className="text-xs text-text-secondary whitespace-nowrap">
                      {formatTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between text-sm">
          <span className="text-text-secondary">
            Showing {activities.length} recent activities
          </span>
          <button className="text-primary hover:text-primary-600 transition-smooth">
            Refresh
          </button>
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;
