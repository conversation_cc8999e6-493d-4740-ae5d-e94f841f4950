import React, { useState } from 'react';
import Icon from 'components/AppIcon';

const FilterControls = ({ filters, onChange }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const filterOptions = {
    category: [
      { value: '', label: 'All Categories' },
      { value: 'desserts', label: 'Desserts' },
      { value: 'main-course', label: 'Main Course' },
      { value: 'appetizers', label: 'Appetizers' },
      { value: 'beverages', label: 'Beverages' },
      { value: 'snacks', label: 'Snacks' },
    ],
    userType: [
      { value: '', label: 'All Users' },
      { value: 'premium', label: 'Premium Users' },
      { value: 'free', label: 'Free Users' },
      { value: 'admin', label: 'Admin Users' },
      { value: 'content-creator', label: 'Content Creators' },
    ],
    region: [
      { value: '', label: 'All Regions' },
      { value: 'north-america', label: 'North America' },
      { value: 'europe', label: 'Europe' },
      { value: 'asia', label: 'Asia' },
      { value: 'south-america', label: 'South America' },
      { value: 'africa', label: 'Africa' },
      { value: 'oceania', label: 'Oceania' },
    ],
    device: [
      { value: '', label: 'All Devices' },
      { value: 'desktop', label: 'Desktop' },
      { value: 'mobile', label: 'Mobile' },
      { value: 'tablet', label: 'Tablet' },
    ],
  };

  const handleFilterChange = (filterType, value) => {
    const newFilters = {
      ...filters,
      [filterType]: value,
    };
    onChange(newFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      category: '',
      userType: '',
      region: '',
      device: '',
    };
    onChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(filters).some((value) => value !== '');
  const activeFilterCount = Object.values(filters).filter(
    (value) => value !== ''
  ).length;

  return (
    <div className="bg-surface border border-gray-200 rounded-lg shadow-card">
      {/* Filter Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-3">
          <Icon name="Filter" size={20} className="text-text-secondary" />
          <div>
            <h3 className="font-medium text-text-primary">Filters</h3>
            {hasActiveFilters && (
              <p className="text-sm text-text-secondary">
                {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''}{' '}
                applied
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="text-sm text-error hover:text-error-600 transition-smooth"
            >
              Clear all
            </button>
          )}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 text-text-secondary hover:text-primary transition-smooth"
          >
            <Icon name={isExpanded ? 'ChevronUp' : 'ChevronDown'} size={16} />
          </button>
        </div>
      </div>

      {/* Quick Filters (Always Visible) */}
      <div className="px-4 pb-4">
        <div className="flex flex-wrap gap-2">
          {/* Category Quick Filter */}
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
          >
            {filterOptions.category.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* User Type Quick Filter */}
          <select
            value={filters.userType}
            onChange={(e) => handleFilterChange('userType', e.target.value)}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
          >
            {filterOptions.userType.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="border-t border-gray-100 p-4 animate-slide-down">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Category
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
              >
                {filterOptions.category.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* User Type Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                User Type
              </label>
              <select
                value={filters.userType}
                onChange={(e) => handleFilterChange('userType', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
              >
                {filterOptions.userType.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Region Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Region
              </label>
              <select
                value={filters.region}
                onChange={(e) => handleFilterChange('region', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
              >
                {filterOptions.region.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Device Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Device Type
              </label>
              <select
                value={filters.device}
                onChange={(e) => handleFilterChange('device', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-smooth"
              >
                {filterOptions.device.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <h4 className="text-sm font-medium text-text-primary mb-2">
                Active Filters:
              </h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(filters).map(([key, value]) => {
                  if (!value) return null;

                  const option = filterOptions[key]?.find(
                    (opt) => opt.value === value
                  );
                  if (!option) return null;

                  return (
                    <span
                      key={key}
                      className="inline-flex items-center space-x-1 px-3 py-1 bg-primary-50 text-primary rounded-full text-sm"
                    >
                      <span>{option.label}</span>
                      <button
                        onClick={() => handleFilterChange(key, '')}
                        className="hover:text-primary-600 transition-smooth"
                      >
                        <Icon name="X" size={14} />
                      </button>
                    </span>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterControls;
