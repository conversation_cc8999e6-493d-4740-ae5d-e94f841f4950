// Main Form Content Component Styles
.main-form-content {
  flex: 1;

  // Header Actions
  &__header {
    border-bottom: var(--border-width-xs) solid var(--border-color-light-gray);
    padding: var(--spacing-lg) var(--spacing-lg);

    @media (min-width: 1024px) {
      padding: var(--spacing-lg) var(--spacing-xxl);
    }
  }

  &__header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
  }

  &__header-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  // Action Buttons
  &__preview-button {
    .custom-btn {
      background-color: var(--color-secondary) !important;
      color: var(--text-color-white) !important;
      border-color: var(--color-secondary) !important;

      &:hover {
        background-color: var(--color-dark-50) !important;
      }
    }
  }

  &__save-button {
    .custom-btn {
      background-color: var(--color-secondary) !important;
      color: var(--text-color-primary) !important;
      border-color: var(--border-color-light-gray) !important;

      &:hover {
        background-color: var(--color-off-white) !important;
      }
    }
  }

  // Mobile Section Selector
  &__mobile-selector {
    background-color: var(--color-secondary);
    border-bottom: var(--border-width-xs) solid var(--border-color-light-gray);
    padding: var(--spacing-md) var(--spacing-lg);

    @media (min-width: 768px) {
      display: none;
    }
  }

  &__mobile-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__mobile-nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-md);
    color: var(--text-color-slate-gray);
    background: none;
    border: none;
    cursor: pointer;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  &__mobile-nav-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
  }

  &__mobile-counter {
    text-align: center;
  }

  &__mobile-counter-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Form Content
  &__form-content {
    padding: var(--spacing-lg) var(--spacing-xxl);
  }

  // Button text responsive behavior
  &__button-text {
    @media (max-width: 639px) {
      display: none;
    }
  }
}
