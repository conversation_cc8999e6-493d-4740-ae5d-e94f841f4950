.chartWidget {
  background: var(--surface, #fff);
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow, 0 1px 2px rgba(0,0,0,0.05));
}

.chartWidget--fullscreen {
  position: fixed;
  inset: 1rem;
  z-index: 50;
}

.chartWidget__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.chartWidget__title {
  font-family: var(--font-heading, inherit);
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-primary, #222);
}

.chartWidget__subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
}

.chartWidget__options {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.chartWidget__content {
  padding: 1rem;
  height: 20rem;
}

.chartWidget__content--fullscreen {
  height: calc(100vh - 8rem);
}

.chartWidget__footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid #f3f4f6;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
}

.chartWidget__legend {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chartWidget__legendItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chartWidget__legendColor {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 9999px;
}

.chartWidget__exportBtn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--primary, #2D5A3D);
  cursor: pointer;
  background: none;
  border: none;
  transition: color 0.2s;
}

.chartWidget__exportBtn:hover {
  color: var(--primary-hover, #27613a);
}

.chartWidget__heatmapCell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: transform 0.2s;
}

.chartWidget__heatmapCell:hover {
  transform: scale(1.05);
} 