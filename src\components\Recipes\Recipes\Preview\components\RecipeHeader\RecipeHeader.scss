.recipe-header {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  border: var(--border-width-xs) solid var(--color-light-gray);
  padding: var(--spacing-lg) var(--spacing-xxl);
  margin-bottom: var(--spacing-xxl);
  box-shadow: var(--box-shadow-xs);

  &__back-section {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__back-btn {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);

    @media (min-width: 1201px) {
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
    }
  }

  &__info {
    width: 58%;
    @media (max-width: 1200px) {
      width: 100%;
    }
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    margin-bottom: var(--spacing-xs);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-sm);
  }

  // Categories
  &__categories {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__category-tag {
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: var(--color-primary-opacity);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);
  }

  // Stats Section
  &__stats {
    width: 42%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    @media (max-width: 1200px) {
      width: 100%;
    }
  }

  &__stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    text-align: center;

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__stat-card {
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-xs);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);

    &--time {
      background-color: var(--color-warning-opacity);
    }

    &--portions {
      background-color: var(--color-success-opacity);
    }

    &--cost {
      background-color: var(--color-danger-opacity);
    }

    &--size {
      background-color: var(--color-warning-opacity);
    }
  }

  &__allergen-icons-wrap {
    line-height: 0px;
    display: flex;
    gap: var(--spacing-xsm);
    svg {
      height: 30px;
      width: 30px;
    }
  }

  &__stat-icon {
    // margin-bottom: var(--spacing-xs);
  }

  &__stat-value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__stat-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Allergen Warning
  &__allergen-warning {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-xsm);
  }

  &__difficulty {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    max-width: max-content;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);

    &--low {
      color: var(--color-success);
      background-color: var(--color-success-opacity);
    }

    &--medium {
      color: var(--color-warning);
      background-color: var(--color-warning-opacity);
    }

    &--hard {
      color: var(--color-danger);
      background-color: var(--color-danger-opacity);
    }

    &--default {
      color: var(--text-color-slate-gray);
      background-color: var(--color-light-gray);
    }
  }

  &__difficulty-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-sm);
  }

  &__allergen-text{
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  &__allergen-icon {
    flex-shrink: 0;
    height: 16px;
    width: 16px;
  }

  &__allergen-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-sm);
    margin-bottom: var(--spacing-xs);
  }

  // Allergen Icons
  &__allergen-icons {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
  }

  &__allergen-icon-item {
    height: 30px;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      height: 30px;
      width: 30px;
    }
  }
}
