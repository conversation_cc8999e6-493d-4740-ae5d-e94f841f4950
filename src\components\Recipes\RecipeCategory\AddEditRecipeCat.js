'use client';

import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { staticOptions } from '@/helper/common/staticOptions';
import CustomImageUploader from '@/components/ImageUpload/CustomImageUploader';
import { setApiMessage } from '@/helper/common/commonFunctions';
import {
  createRecipeCategory,
  updateRecipeCategory,
} from '@/services/recipeService';

const AddEditRecipeCat = ({ singleData, handleCloseAddEditModal }) => {
  const [loader, setLoader] = useState(false);
  const isDefault = singleData?.isDefault || false;
  const categoryType = 'recipe';

  return (
    <Box>
      {/* {loader && <PreLoader />} */}
      <Formik
        initialValues={{
          category_name: singleData?.category_name || '',
          category_icon: singleData?.iconItem?.iconUrl || '',
          category_status: singleData?.category_status || 'active',
          imagePreview: singleData?.iconItem?.iconUrl || '',
          description: singleData?.category_description || '',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          category_name: Yup.string().trim().required('This field is required'),
          category_status: Yup.string()
            .trim()
            .required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          setLoader(true);

          // Prepare data object
          const body = new FormData();
          body.append('category_name', requestData?.category_name);
          body.append(
            'category_status',
            requestData?.category_status || 'active'
          );
          body.append('category_description', requestData?.description);
          body.append('category_type', categoryType);

          // Handle file upload - only append if file exists and is a File object
          body.append('categoryIcon', requestData?.category_icon);

          const config = {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          };
          try {
            if (singleData?.id) {
              const data = await updateRecipeCategory(
                singleData.id,
                body,
                config
              );
              setApiMessage('success', data?.message);
            } else {
              // Create new category
              const data = await createRecipeCategory(body, config);
              setApiMessage('success', data?.message);
            }

            setLoader(false);
            // Close modal and refresh parent component
            handleCloseAddEditModal(true); // Pass true to indicate successful operation
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
          isValid,
        }) => {
          return (
            <Box className="recipe-modal-form">
              <Form onSubmit={handleSubmit}>
                <Box className="category-grid-container">
                  <Box className="">
                    <CustomImageUploader
                      imagePreview={values.imagePreview}
                      acceptFiles={values.acceptFiles}
                      error={errors?.category_icon}
                      fieldErrorText={errors?.category_icon}
                      onDropAccepted={(file) => {
                        const url = URL.createObjectURL(file);
                        setFieldValue('imagePreview', url);
                        setFieldValue('acceptFiles', { link: url });
                        setFieldValue('category_icon', file); // Store only filename for form validation
                      }}
                      onRemoveImage={() => {
                        setFieldValue('imagePreview', '');
                        setFieldValue('acceptFiles', null);
                        setFieldValue('category_icon', '');
                      }}
                      label="Category icon"
                      placeholder=""
                      uploadClassName="category-upload-container"
                      previewClassName="category-preview-container"
                    />
                  </Box>
                  <Box className="w100 ml8">
                    <Box className="w100">
                      <CustomTextField
                        fullWidth
                        name="category_name"
                        value={values?.category_name}
                        label="Category name"
                        placeholder="Enter category name"
                        error={Boolean(
                          touched?.category_name && errors?.category_name
                        )}
                        helperText={
                          touched?.category_name && errors?.category_name
                        }
                        onBlur={handleBlur}
                        onChange={(e) => {
                          if (e.target.value.length <= 60) {
                            handleChange(e);
                          }
                        }}
                        required
                        disabled={isDefault}
                      />
                      <Typography className="sub-title-text text-align-end">
                        {values?.category_name?.length + '/ 90'}
                      </Typography>
                    </Box>
                    <Box className="w100">
                      <CustomSelect
                        fullWidth
                        label="Status"
                        name="category_status"
                        placeholder="Status"
                        options={staticOptions?.ORG_STATUS}
                        value={
                          staticOptions?.ORG_STATUS?.find(
                            (opt) => opt?.value === values?.category_status
                          ) || ''
                        }
                        onChange={(selectedOption) =>
                          setFieldValue(
                            'category_status',
                            selectedOption?.value || ''
                          )
                        }
                        error={Boolean(
                          touched?.category_status && errors?.category_status
                        )}
                        helperText={
                          touched?.category_status && errors?.category_status
                        }
                        disabled={isDefault}
                        isClearable={false}
                        required
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    </Box>
                  </Box>
                </Box>

                <Box className="w100 mt8">
                  <CustomTextField
                    fullWidth
                    name="description"
                    value={values?.description}
                    label="Description"
                    placeholder="Enter description"
                    error={Boolean(touched?.description && errors?.description)}
                    helperText={touched?.description && errors?.description}
                    onBlur={handleBlur}
                    multiline={true}
                    rows={2}
                    disabled={isDefault}
                    onChange={(e) => {
                      if (e.target.value.length <= 250) {
                        handleChange(e);
                      }
                    }}
                  />
                  <Typography className="sub-title-text text-align-end">
                    {values?.description?.length + '/ 250'}
                  </Typography>
                </Box>

                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    variant="outlined"
                    title="Cancel"
                    onClick={() => handleCloseAddEditModal(false)}
                  />
                  <CustomButton
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!(dirty && isValid) || loader}
                    title={`${loader ? 'Save...' : 'Save'}`}
                  />
                </Box>
              </Form>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
};
export default AddEditRecipeCat;
