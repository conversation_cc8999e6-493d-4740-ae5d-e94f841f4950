'use client';

import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './statisticstile.scss';

const StatisticsTile = ({
  id,
  title,
  value,
  change,
  trend,
  sparklineData,
  icon,
  color,
  description,
  onClick,
  realTimeValue,
}) => {
  const getColorClass = (colorType) => {
    return `statistics-tile__icon--${colorType}`;
  };

  const getTrendClass = (trendType) => {
    return `statistics-tile__trend--${trendType}`;
  };

  // Simple sparkline SVG
  const renderSparkline = () => {
    if (!sparklineData || sparklineData.length === 0) return null;

    const max = Math.max(...sparklineData);
    const min = Math.min(...sparklineData);
    const range = max - min || 1;

    const points = sparklineData
      .map((value, index) => {
        const x = (index / (sparklineData.length - 1)) * 60;
        const y = 20 - ((value - min) / range) * 20;
        return `${x},${y}`;
      })
      .join(' ');

    return (
      <svg width="60" height="20" className="statistics-tile__sparkline">
        <polyline
          points={points}
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  };

  return (
    <div onClick={onClick} className="statistics-tile">
      <div className="statistics-tile__header">
        <div className={`statistics-tile__icon ${getColorClass(color)}`}>
          <Icon name={icon} size={20} />
        </div>
        {change && (
          <div className={`statistics-tile__change ${getTrendClass(trend)}`}>
            <Icon
              name={
                trend === 'up'
                  ? 'TrendingUp'
                  : trend === 'down'
                    ? 'TrendingDown'
                    : 'Minus'
              }
              size={14}
            />
            <span className="statistics-tile__change-text">{change}</span>
          </div>
        )}
      </div>

      <div className="statistics-tile__content">
        <h3 className="statistics-tile__title">{title}</h3>
        <div className="statistics-tile__value-container">
          <p className="statistics-tile__value">{realTimeValue || value}</p>
          {realTimeValue && realTimeValue !== value && (
            <span className="statistics-tile__updated">Updated</span>
          )}
        </div>
      </div>

      <div className="statistics-tile__footer">
        <p className="statistics-tile__description">{description}</p>
        <div
          className={`statistics-tile__sparkline-container ${getColorClass(color)}`}
        >
          {renderSparkline()}
        </div>
      </div>

      {/* Hover effect indicator */}
      <div className="statistics-tile__hover-indicator">
        <div className="statistics-tile__view-details">
          <span>View details</span>
          <Icon name="ArrowRight" size={12} />
        </div>
      </div>
    </div>
  );
};

export default StatisticsTile;
