import React from 'react';
import Icon from 'components/AppIcon';

const StatisticsTile = ({ 
  id, 
  title, 
  value, 
  change, 
  trend, 
  sparklineData, 
  icon, 
  color, 
  description, 
  onClick,
  realTimeValue 
}) => {
  const colorClasses = {
    primary: 'text-primary bg-primary-50 border-primary-200',
    success: 'text-success bg-success-50 border-success-200',
    warning: 'text-warning bg-warning-50 border-warning-200',
    error: 'text-error bg-error-50 border-error-200',
    accent: 'text-accent bg-accent-50 border-accent-200',
    secondary: 'text-secondary bg-secondary-50 border-secondary-200'
  };

  const trendColors = {
    up: 'text-success',
    down: 'text-error',
    neutral: 'text-text-secondary'
  };

  // Simple sparkline SVG
  const renderSparkline = () => {
    if (!sparklineData || sparklineData.length === 0) return null;

    const max = Math.max(...sparklineData);
    const min = Math.min(...sparklineData);
    const range = max - min || 1;
    
    const points = sparklineData.map((value, index) => {
      const x = (index / (sparklineData.length - 1)) * 60;
      const y = 20 - ((value - min) / range) * 20;
      return `${x},${y}`;
    }).join(' ');

    return (
      <svg width="60" height="20" className="opacity-60">
        <polyline
          points={points}
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  };

  return (
    <div
      onClick={onClick}
      className="bg-surface border border-gray-200 rounded-lg p-4 hover:shadow-modal transition-smooth cursor-pointer hover-lift group"
    >
      <div className="flex items-start justify-between mb-3">
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon name={icon} size={20} />
        </div>
        {change && (
          <div className={`flex items-center space-x-1 ${trendColors[trend]}`}>
            <Icon 
              name={trend === 'up' ? 'TrendingUp' : trend === 'down' ? 'TrendingDown' : 'Minus'} 
              size={14} 
            />
            <span className="text-sm font-medium">{change}</span>
          </div>
        )}
      </div>

      <div className="mb-2">
        <h3 className="text-sm font-medium text-text-secondary mb-1">{title}</h3>
        <div className="flex items-baseline space-x-2">
          <p className="text-2xl font-bold text-text-primary">
            {realTimeValue || value}
          </p>
          {realTimeValue && realTimeValue !== value && (
            <span className="text-xs text-success font-medium animate-pulse">
              Updated
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <p className="text-xs text-text-secondary">{description}</p>
        <div className={`${colorClasses[color].split(' ')[0]}`}>
          {renderSparkline()}
        </div>
      </div>

      {/* Hover effect indicator */}
      <div className="mt-3 opacity-0 group-hover:opacity-100 transition-smooth">
        <div className="flex items-center space-x-1 text-xs text-primary">
          <span>View details</span>
          <Icon name="ArrowRight" size={12} />
        </div>
      </div>
    </div>
  );
};

export default StatisticsTile;