import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import { InputAdornment } from '@mui/material';
import { getAttributeList } from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './NutritionComponent.scss';

const NutritionComponent = ({ formData, dispatch }) => {
  // State for nutrition list management
  const [nutritionDatabase, setNutritionDatabase] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(true);

  // Local state for immediate UI updates
  const [localNutritions, setLocalNutritions] = useState(() => {
    // Extract nutrition from all ingredients (old flow)
    const allNutritions = [];
    formData?.ingredients?.forEach?.((ingredient) => {
      if (ingredient?.nutrition && Array.isArray(ingredient.nutrition)) {
        // Add ingredient name to each nutrition item
        const nutritionsWithIngredient = ingredient.nutrition.map(
          (nutrition) => ({
            ...nutrition,
            ingredient_name:
              ingredient?.ingredient_name ||
              ingredient?.name ||
              'Unknown Ingredient',
          })
        );
        allNutritions.push(...nutritionsWithIngredient);
      }
    });
    return allNutritions;
  });

  // Use local state for immediate UI updates
  const nutritions = localNutritions;

  // Sync local state with prop changes
  useEffect(() => {
    // Extract nutrition from all ingredients (old flow)
    const allNutritions = [];
    formData?.ingredients?.forEach?.((ingredient) => {
      if (ingredient?.nutrition && Array.isArray(ingredient.nutrition)) {
        // Add ingredient name to each nutrition item
        const nutritionsWithIngredient = ingredient.nutrition.map(
          (nutrition) => ({
            ...nutrition,
            ingredient_name:
              ingredient?.ingredient_name ||
              ingredient?.name ||
              'Unknown Ingredient',
          })
        );
        allNutritions.push(...nutritionsWithIngredient);
      }
    });
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: {
          nutrition: allNutritions,
        },
      });
    }
    setLocalNutritions(allNutritions);
  }, [formData?.ingredients]);

  // Fetch nutrition options from API
  useEffect(() => {
    const fetchNutritions = async () => {
      try {
        setLoading(true);
        const response = await getAttributeList(
          '',
          '',
          { status: 'active' },
          '',
          '',
          'nutrition'
        );

        // Transform API response to component format
        const transformedNutritions =
          response?.attributes?.map((nutrition) => ({
            id: nutrition?.id,
            attribute_title: nutrition?.attribute_title,
            description: nutrition?.attribute_description || '',
            unit_of_measure: nutrition?.unit_of_measure || 'g', // Default unit, can be customized
            icon: 'Activity', // Default icon
          })) || [];

        setNutritionDatabase(transformedNutritions);
      } catch (error) {
        console.error('Error fetching nutritions:', error);
        setApiMessage('error', 'Failed to load nutrition options');
        setNutritionDatabase([]);
      } finally {
        setLoading(false);
      }
    };

    fetchNutritions();
  }, []);

  // Filter nutrition suggestions based on search
  const filteredSuggestions =
    nutritionDatabase?.filter?.((nutrition) =>
      nutrition?.attribute_title
        ?.toLowerCase?.()
        ?.includes?.(searchTerm?.toLowerCase?.() || '')
    ) || [];

  // Add nutrition to the list
  const addNutrition = (nutritionData) => {
    const newNutrition = {
      ...nutritionData,
      value: 0,
      unit_of_measure: nutritionData?.unit_of_measure || 'g',
      ingredient_name: 'Manual Entry', // Mark as manually added
    };

    const updatedNutritions = [...(nutritions || []), newNutrition];

    // Update local state immediately for UI responsiveness
    setLocalNutritions(updatedNutritions);

    // Also update the ingredients array to persist the nutrition
    const updatedIngredients = [...(formData?.ingredients || [])];

    // Find or create a nutrition container ingredient
    let nutritionContainerIndex = updatedIngredients.findIndex(
      (ing) => ing.id === 'nutrition-container'
    );

    if (nutritionContainerIndex === -1) {
      // Create nutrition container if it doesn't exist
      updatedIngredients.push({
        id: 'nutrition-container',
        ingredient_name: 'Manual Nutrition',
        name: 'Manual Nutrition Entries',
        quantity: 1,
        unit: 'serving',
        nutrition: [newNutrition],
      });
    } else {
      // Add to existing nutrition container
      const existingNutrition =
        updatedIngredients[nutritionContainerIndex].nutrition || [];
      updatedIngredients[nutritionContainerIndex].nutrition = [
        ...existingNutrition,
        newNutrition,
      ];
    }

    // Dispatch both nutrition array and updated ingredients
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: {
          nutrition: updatedNutritions,
        },
      });

      // Also update ingredients to persist the data
      dispatch({
        type: 'UPDATE_INGREDIENTS',
        payload: updatedIngredients,
      });
    }

    setSearchTerm('');
    setShowSuggestions(false);
  };

  // Update nutrition value
  const updateNutrition = (index, field, value) => {
    if (!nutritions?.[index]) return;

    const updatedNutritions = [...(nutritions || [])];
    updatedNutritions[index] = {
      ...updatedNutritions[index],
      [field]: field === 'value' ? parseFloat(value) || 0 : value,
    };

    // Update local state immediately for UI responsiveness
    setLocalNutritions(updatedNutritions);

    // Also update the ingredients array to persist changes
    const updatedIngredients = [...(formData?.ingredients || [])];

    // Update nutrition in all ingredients
    updatedIngredients.forEach((ingredient, ingIndex) => {
      if (ingredient?.nutrition && Array.isArray(ingredient.nutrition)) {
        ingredient.nutrition.forEach((nutrition, nutIndex) => {
          // Find the nutrition item by matching properties
          const currentNutrition = nutritions[index];
          if (
            nutrition?.id === currentNutrition?.id ||
            nutrition?.attribute_title === currentNutrition?.attribute_title
          ) {
            updatedIngredients[ingIndex].nutrition[nutIndex] = {
              ...nutrition,
              [field]: field === 'value' ? parseFloat(value) || 0 : value,
            };
          }
        });
      }
    });

    // Dispatch both nutrition array and updated ingredients
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: {
          nutrition: updatedNutritions,
        },
      });

      // Also update ingredients to persist the data
      dispatch({
        type: 'UPDATE_INGREDIENTS',
        payload: updatedIngredients,
      });
    }
  };

  // Remove nutrition from list
  const removeNutrition = (index) => {
    const nutritionToRemove = nutritions[index];
    const updatedNutritions = nutritions?.filter?.((_, i) => i !== index) || [];

    // Update local state immediately for UI responsiveness
    setLocalNutritions(updatedNutritions);

    // Also update the ingredients array to persist changes
    const updatedIngredients = [...(formData?.ingredients || [])];

    // Remove nutrition from all ingredients
    updatedIngredients.forEach((ingredient, ingIndex) => {
      if (ingredient?.nutrition && Array.isArray(ingredient.nutrition)) {
        updatedIngredients[ingIndex].nutrition = ingredient.nutrition.filter(
          (nutrition) => {
            // Remove the nutrition item by matching properties
            return !(
              nutrition?.id === nutritionToRemove?.id ||
              nutrition?.attribute_title === nutritionToRemove?.attribute_title
            );
          }
        );
      }
    });

    // Dispatch both nutrition array and updated ingredients
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: {
          nutrition: updatedNutritions,
        },
      });

      // Also update ingredients to persist the data
      dispatch({
        type: 'UPDATE_INGREDIENTS',
        payload: updatedIngredients,
      });
    }
  };

  // Get unique nutritions with combined values
  const getUniqueNutritions = () => {
    const nutritionMap = new Map();

    nutritions?.forEach?.((nutrition) => {
      const key = nutrition?.id || nutrition?.attribute_title;
      if (nutritionMap.has(key)) {
        // Combine values for same nutrition type
        const existing = nutritionMap.get(key);
        existing.value += nutrition?.value || 0;
      } else {
        nutritionMap.set(key, { ...nutrition });
      }
    });

    return Array.from(nutritionMap.values());
  };

  const uniqueNutritions = getUniqueNutritions();

  // Handle search input
  const handleSearchChange = (e) => {
    const value = e?.target?.value || '';
    setSearchTerm(value);
    setShowSuggestions(value?.length >= 2);
  };

  // Handle suggestion click
  const handleSuggestionClick = (nutrition) => {
    // Check if nutrition already exists
    const exists = nutritions?.some?.(
      (n) =>
        n?.id === nutrition?.id ||
        n?.attribute_title === nutrition?.attribute_title
    );

    if (!exists) {
      addNutrition(nutrition);
    } else {
      setApiMessage('warning', 'This nutrition is already added');
      setSearchTerm('');
      setShowSuggestions(false);
    }
  };

  if (loading) {
    return (
      <div className="nutrition-component">
        <div className="nutrition-component__loading">
          <Icon name="Loader2" size={24} color="var(--color-primary)" />
          <span>Loading nutrition options...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="nutrition-component">
      {/* Header */}
      <div className="nutrition-component__header">
        <div className="nutrition-component__header-content">
          <Icon name="BarChart3" size={20} color="var(--color-primary)" />
          <div>
            <h3 className="nutrition-component__title">
              Nutrition Information
            </h3>
            <p className="nutrition-component__description">
              Add nutritional values per serving
            </p>
          </div>
        </div>
      </div>

      {/* Search and Add Nutrition */}
      <div className="nutrition-component__search">
        <div className="nutrition-component__search-container">
          <CustomTextField
            placeholder="Search nutrition to add..."
            value={searchTerm}
            onChange={handleSearchChange}
            onFocus={() => setShowSuggestions(searchTerm?.length >= 2)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Icon
                    name="Search"
                    size={16}
                    color="var(--text-color-slate-gray)"
                  />
                </InputAdornment>
              ),
            }}
            fullWidth
          />

          {/* Suggestions Dropdown */}
          {showSuggestions && filteredSuggestions?.length > 0 && (
            <div className="nutrition-component__suggestions">
              {filteredSuggestions?.slice(0, 5)?.map?.((nutrition) => (
                <button
                  key={nutrition?.id}
                  onClick={() => handleSuggestionClick(nutrition)}
                  className="nutrition-component__suggestion-item"
                >
                  <Icon
                    name="Activity"
                    size={16}
                    color="var(--color-primary)"
                  />
                  <span>{nutrition?.attribute_title}</span>
                  <Icon
                    name="Plus"
                    size={14}
                    color="var(--text-color-slate-gray)"
                  />
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Nutrition List */}
      {(nutritions?.length || 0) > 0 && (
        <div className="nutrition-component__list">
          <h4 className="nutrition-component__list-title">
            Added Nutritions ({nutritions?.length || 0})
          </h4>
          <div className="nutrition-component__items">
            {nutritions?.map?.((nutrition, index) => (
              <div
                key={`${nutrition?.id}-${index}`}
                className="nutrition-component__item"
              >
                <div className="nutrition-component__item-info">
                  <Icon
                    name="Activity"
                    size={16}
                    color="var(--color-primary)"
                  />
                  <div className="nutrition-component__item-details">
                    <span className="nutrition-component__item-name">
                      {nutrition?.attribute_title || nutrition?.name}
                    </span>
                    {nutrition?.ingredient_name && (
                      <span className="nutrition-component__item-ingredient">
                        from {nutrition.ingredient_name}
                      </span>
                    )}
                  </div>
                </div>

                <div className="nutrition-component__item-controls">
                  <CustomTextField
                    type="number"
                    value={nutrition?.value || ''}
                    onChange={(e) =>
                      updateNutrition(index, 'value', e?.target?.value || '')
                    }
                    placeholder="0"
                    inputProps={{ step: 0.1, min: 0 }}
                    size="small"
                    style={{ width: '80px' }}
                  />
                  <span className="nutrition-component__item-unit">
                    {nutrition?.unit_of_measure || 'g'}
                  </span>
                  <button
                    onClick={() => removeNutrition(index)}
                    className="nutrition-component__item-remove"
                    aria-label={`Remove ${nutrition?.id}`}
                  >
                    <Icon name="X" size={14} color="currentColor" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Unique Nutritions Summary */}
      {uniqueNutritions?.length > 0 &&
        uniqueNutritions?.length !== nutritions?.length && (
          <div className="nutrition-component__summary">
            <h4 className="nutrition-component__summary-title">
              Combined Nutrition Summary ({uniqueNutritions?.length})
            </h4>
            <div className="nutrition-component__summary-items">
              {uniqueNutritions?.map?.((nutrition) => (
                <div
                  key={nutrition?.id || nutrition?.attribute_title}
                  className="nutrition-component__summary-item"
                >
                  <div className="nutrition-component__summary-details">
                    <span className="nutrition-component__summary-label">
                      {nutrition?.attribute_title || nutrition?.name}
                    </span>
                    {nutrition?.ingredient_name && (
                      <span className="nutrition-component__summary-ingredient">
                        from {nutrition.ingredient_name}
                      </span>
                    )}
                  </div>
                  <span className="nutrition-component__summary-value">
                    {nutrition?.value?.toFixed?.(1) || 0}{' '}
                    {nutrition?.unit_of_measure || 'g'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

      {/* Empty State */}
      {(nutritions?.length || 0) === 0 && (
        <div className="nutrition-component__empty">
          <Icon
            name="Activity"
            size={48}
            color="var(--text-color-slate-gray)"
          />
          <h4>No Nutrition Added</h4>
          <p>Search and add nutrition information for this recipe</p>
        </div>
      )}
    </div>
  );
};

export default NutritionComponent;
