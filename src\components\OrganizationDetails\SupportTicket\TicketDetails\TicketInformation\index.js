'use client';
import React, { useState } from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import CustomEditor from '@/components/UI/CustomEditor';
import CustomDateTimePicker from '@/components/UI/datetimepicker';
import CustomButton from '@/components/UI/button';
import CustomSelect from '@/components/UI/CustomSelect';
import Image from 'next/image';
import { Box, Menu, MenuItem, Tooltip, Typography } from '@mui/material';
import dayjs from 'dayjs';
import Profile from '../../../../../../public/images/Companylogo.png';
import Follower1 from '../../../../../../public/images/tea.png';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import './ticketinformation.scss';
import { CustomTextField } from '@/components/UI/CommonField';
import SingleSelect from '@/components/UI/SelectWithSearch';
import CustomNameImageSelect from '@/components/UI/CustomNameImageSelect';

const validationSchema = Yup.object().shape({
  description: Yup.string().required('Description is required'),
  sdate: Yup.date().required('Start Date is required'),
  status: Yup.string().required('Status is required'),
  priority: Yup.string().required('Priority is required'),
  classification: Yup.string().required('Classification is required'),
});

const taskFollowersData = [
  { name: 'John Doe', image: Follower1 },
  { name: 'Jane Smith', image: Profile },
  { name: 'Alice Johnson', image: Follower1 },
  { name: 'steve smith', image: Profile },
];

const assigneeOptions = [
  { label: 'John Doe', value: 'john_doe', image: Follower1 },
  { label: 'Jane Smith', value: 'jane_smith', image: Profile },
  { label: 'Alice Johnson', value: 'alice_johnson', image: Follower1 },
  { label: 'Steve Smith', value: 'steve_smith', image: Profile },
];

export default function TicketInformation() {
  const [taskFollowers, setTaskFollowers] = useState(taskFollowersData);
  const [anchorEl, setAnchorEl] = useState(null); // For the dropdown
  const [selectedFollower, setSelectedFollower] = useState(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [isAddFollowerOpen, setIsAddFollowerOpen] = useState(false);
  // Filter available followers based on search input
  const statusOptions = [
    { label: 'Open', value: 'open' },
    { label: 'Escalated', value: 'escalated' },
    { label: 'In-Progress', value: 'in_progress' },
    { label: 'Invoiced', value: 'invoiced' },
    { label: 'On Hold', value: 'on_hold' },
    { label: 'QA Review', value: 'qa_review' },
    { label: 'Assigned', value: 'assigned' },
    { label: 'Under Review', value: 'under_review' },
    { label: 'Closed', value: 'closed' },
  ];

  const priorityOptions = [
    { value: 'none', label: 'None' },
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
  ];

  const classificationOptions = [
    { value: 'question', label: 'Question' },
    { value: 'problem', label: 'Problem' },
    { value: 'feature', label: 'Features' },
    { value: 'others', label: 'Others' },
  ];

  // Toggle dropdown
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // Add follower to the task
  const addFollower = (follower) => {
    setTaskFollowers([...taskFollowers, follower]);
    handleClose(); // Close the dropdown after adding a follower
  };

  // Remove a follower from the task
  const removeFollowers = (index) => {
    const existingFollowers = taskFollowers?.filter(
      (follower, i) => i !== index
    );
    setTaskFollowers(existingFollowers);
  };

  // Get available followers (those who are not yet added)
  const availableFollowers = taskFollowersData.filter(
    (follower) =>
      !taskFollowers.some((taskFollower) => taskFollower.name === follower.name)
  );

  const handleToggleClick = () => {
    setIsAddFollowerOpen((prevState) => !prevState);
  };
  return (
    <Formik
      initialValues={{
        description: '',
        sdate: '',
        status: '',
        priority: 'none',
        classification: '',
        assignee: null,
      }}
      validationSchema={validationSchema}
      onSubmit={(values) => {
        console.log(values);
      }}
    >
      {({
        values,
        errors,
        touched,
        setFieldValue,
        handleBlur,
        handleSubmit,
        handleChange,
      }) => (
        <Form onSubmit={handleSubmit} className="form-wrap">
          <Box className="pt32">
            <Typography className="Inter12 discription-text" variant="h6">
              Description*
            </Typography>
            <CustomEditor
              content={values.description}
              setContent={(content) => setFieldValue('description', content)}
              height="200px"
            />
            {touched.description && errors.description && (
              <div className="error">{errors.description}</div>
            )}
          </Box>

          <Typography className="pt16 key-information-wrap" variant="h6">
            Key Information
          </Typography>

          <Box className="select-assignee-wrap d-flex pt16 align-center">
            <Typography className="assignee-text" component="p">
              Assignee
            </Typography>
            <SingleSelect
              placeholder="Select assignee"
              className="slected-wrap assignee-select"
              options={assigneeOptions}
              value={
                assigneeOptions.find(
                  (opt) => opt?.value === values?.assignee
                ) || ''
              }
              name="assignee"
              onChange={(selectedOption) =>
                setFieldValue('assignee', selectedOption?.value || '')
              }
              // label={
              //   <span>
              //     a<span className="primary-color"> *</span>
              //   </span>
              // }
            />
          </Box>
          <Box className="ticket-owner-wrap pt16">
            <Typography component="p" className="ticket-owner Inter12">
              Ticket Owner
            </Typography>
            <Box className="info-wrap d-flex align-center gap-20">
              <Image
                src={Profile}
                alt="profile photo"
                className="preview-img"
                width={40}
                height={40}
              />
              <Typography component="p" className="profile-name">
                Yagnik Siddhapra
              </Typography>
            </Box>
          </Box>

          <Box className="custom-select-wrap pt16">
            <Box className="pt32">
              <CustomSelect
                label="Status"
                name="status"
                placeholder="Select Status"
                options={statusOptions}
                value={
                  statusOptions?.find((opt) => opt?.value === values?.status) ||
                  ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('status', selectedOption?.value || '')
                }
                error={touched?.status && errors?.status}
                helperText={touched?.status && errors?.status}
                required
              />
            </Box>
            <Box className="pt32">
              <CustomDateTimePicker
                label={
                  <span>
                    Start Date ( DD/MM/YYYY hh:mm aa )
                    <span className="primary-color"> *</span>
                  </span>
                }
                className={
                  touched.sdate && errors.sdate
                    ? 'textfeild-error'
                    : 'custom-date-time'
                }
                name="sdate"
                value={dayjs(values.sdate)}
                onBlur={handleBlur}
                onChange={(date) => setFieldValue('sdate', date)}
                disablePast
                inputVariant="outlined"
                format="DD/M/YYYY hh:mm A"
              />
              {touched.sdate && errors.sdate && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.sdate}
                </Typography>
              )}
            </Box>
          </Box>

          <Box className="followers-wrap d-flex">
            <Typography className="follower-text-wrap">
              Task Followers
            </Typography>
            <Box className="followers-list-wrap gap-16">
              <Box className="followers-list d-flex">
                {taskFollowers.map((follower, index) => (
                  <Tooltip title={follower?.name} arrow key={index}>
                    <Box className="follower-item d-flex align-center gap-8">
                      <Image
                        src={follower.image}
                        alt={`${follower.name}'s photo`}
                        className="follower-img"
                        width={30}
                        height={30}
                      />
                      <Box className="close-icon-wrap">
                        <CloseIcon
                          onClick={() => removeFollowers(index)}
                          className="close-icon"
                          sx={{ cursor: 'pointer' }}
                        />
                      </Box>
                    </Box>
                  </Tooltip>
                ))}
              </Box>

              <Box
                className="add-follower-wrap d-flex align-center"
                onClick={handleToggleClick}
              >
                <AddIcon className="add-btn" />
                <Typography component="p" className="add-follow-text">
                  Add Followers
                </Typography>
              </Box>

              {isAddFollowerOpen && (
                <Box className="followers-menu">
                  <Box className="search-box-wrap">
                    <SingleSelect
                      placeholder="Search Followers"
                      className="followers-search"
                      options={availableFollowers.map((follower) => ({
                        value: follower.name,
                        label: follower.name,
                      }))}
                      value={searchTerm}
                      onChange={(selectedOption) => {
                        const selectedFollower = availableFollowers.find(
                          (follower) => follower.name === selectedOption.value
                        );
                        if (selectedFollower) {
                          addFollower(selectedFollower);
                          setIsAddFollowerOpen(false);
                        }
                        setSearchTerm('');
                      }}
                    />
                  </Box>
                </Box>
              )}
            </Box>
          </Box>

          <Typography className="additional-wrap" variant="h6">
            Additional Information
          </Typography>

          <Box className="custom-select-wrap">
            <Box className="pt32">
              <CustomSelect
                label="Priority"
                name="priority"
                placeholder="Select priority"
                options={priorityOptions}
                value={
                  priorityOptions?.find(
                    (opt) => opt?.value === values?.priority
                  ) || ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('priority', selectedOption?.value || '')
                }
              />
            </Box>
            <Box className="pt32">
              <CustomSelect
                label="Classifications"
                name="classification"
                placeholder="Select Classification"
                options={classificationOptions}
                value={
                  classificationOptions?.find(
                    (opt) => opt?.value === values?.classification
                  ) || ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('classification', selectedOption?.value || '')
                }
                error={touched?.classification && errors?.classification}
                helperText={touched?.classification && errors?.classification}
                required
              />
            </Box>
          </Box>
          <Box className="pt24 d-flex justify-end gap-20 buttons-wrap">
            <CustomButton
              className="p16 cancel-btn"
              type="button"
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              title="Cancel"
            />

            <CustomButton
              className="p16 submit-btn"
              type="submit"
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              title="Submit"
            />
          </Box>
        </Form>
      )}
    </Formik>
  );
}
