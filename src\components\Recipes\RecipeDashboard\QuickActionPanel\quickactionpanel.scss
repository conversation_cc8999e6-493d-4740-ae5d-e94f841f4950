.quick-action-panel {
  background-color: var(--color-white);
  border: var(--border-width-xs) solid var(--color-light-gray);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-xs);
  overflow: hidden;

  // Header Section
  &__header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid var(--color-light-grayish-blue);
  }

  &__header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-xl);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-tiny);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  &__header-icon {
    color: var(--color-warning);
    flex-shrink: 0;
  }

  // Content Section
  &__content {
    padding: var(--spacing-lg);
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // Action Cards
  &__action-card {
    position: relative;
    padding: var(--spacing-lg);
    border: var(--border-width-xs) solid;
    border-radius: var(--border-radius-md);
    text-decoration: none;
    transition: all 0.15s ease-out;
    display: block;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    // Color variants
    &--primary {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border-color: var(--color-primary);

      &:hover {
        background-color: rgba(19, 94, 150, 0.15);
      }
    }

    &--success {
      background-color: var(--color-success-opacity);
      color: var(--color-success);
      border-color: var(--color-success);

      &:hover {
        background-color: rgba(3, 141, 42, 0.15);
      }
    }

    &--warning {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
      border-color: var(--color-warning);

      &:hover {
        background-color: rgba(219, 151, 44, 0.15);
      }
    }

    &--error {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);
      border-color: var(--color-danger);

      &:hover {
        background-color: rgba(211, 47, 47, 0.15);
      }
    }

    &--accent {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
      border-color: var(--color-warning);

      &:hover {
        background-color: rgba(219, 151, 44, 0.15);
      }
    }
  }

  &__action-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  &__action-icon-wrapper {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    background-color: var(--color-white);
    box-shadow: var(--box-shadow-xs);
    flex-shrink: 0;
  }

  &__action-icon {
    display: block;
  }

  &__action-details {
    flex: 1;
  }

  &__action-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-tiny);
    transition: color 0.15s ease-out;

    .quick-action-panel__action-card:hover & {
      color: currentColor;
    }
  }

  &__action-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__action-count {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    opacity: 0.8;
  }

  &__action-arrow {
    opacity: 0;
    transition: all 0.15s ease-out;
    transform: translateX(0);

    .quick-action-panel__action-card:hover & {
      opacity: 1;
      transform: translateX(4px);
    }
  }

  // Count Badge
  &__badge {
    position: absolute;
    top: -8px;
    right: -8px;
    width: var(--spacing-xxl);
    height: var(--spacing-xxl);
    background-color: var(--color-danger);
    color: var(--text-color-white);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // Footer Section
  &__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: var(--border-width-xs) solid var(--color-light-grayish-blue);
    background-color: var(--color-off-white);
  }

  &__footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 480px) {
      flex-direction: column;
      gap: var(--spacing-sm);
      align-items: flex-start;
    }
  }

  &__footer-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  &__footer-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    text-decoration: none;
    transition: color 0.15s ease-out;

    &:hover {
      color: var(--color-dark-blue);
    }
  }
}
